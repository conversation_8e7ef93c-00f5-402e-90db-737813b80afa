// 简单的favicon生成脚本
// 这个脚本可以帮助生成基本的ICO文件

const fs = require('fs');
const path = require('path');

// 创建一个简单的16x16 ICO文件的十六进制数据
// 这是一个最小的ICO文件格式
const icoData = Buffer.from([
  // ICO文件头 (6字节)
  0x00, 0x00, // 保留字段
  0x01, 0x00, // 图像类型 (1 = ICO)
  0x01, 0x00, // 图像数量 (1个图像)
  
  // 图像目录条目 (16字节)
  0x10,       // 宽度 (16像素)
  0x10,       // 高度 (16像素)
  0x00,       // 颜色数 (0 = 256色以上)
  0x00,       // 保留字段
  0x01, 0x00, // 颜色平面数
  0x20, 0x00, // 每像素位数 (32位)
  0x68, 0x05, 0x00, 0x00, // 图像数据大小
  0x16, 0x00, 0x00, 0x00, // 图像数据偏移
  
  // BMP信息头 (40字节)
  0x28, 0x00, 0x00, 0x00, // 信息头大小
  0x10, 0x00, 0x00, 0x00, // 图像宽度
  0x20, 0x00, 0x00, 0x00, // 图像高度 (包括掩码)
  0x01, 0x00,             // 颜色平面数
  0x20, 0x00,             // 每像素位数
  0x00, 0x00, 0x00, 0x00, // 压缩方式
  0x00, 0x05, 0x00, 0x00, // 图像数据大小
  0x00, 0x00, 0x00, 0x00, // 水平分辨率
  0x00, 0x00, 0x00, 0x00, // 垂直分辨率
  0x00, 0x00, 0x00, 0x00, // 颜色数
  0x00, 0x00, 0x00, 0x00, // 重要颜色数
]);

// 创建16x16像素的图像数据 (橙色背景，白色"I"字母)
const imageData = [];

// 生成16x16的RGBA像素数据
for (let y = 0; y < 16; y++) {
  for (let x = 0; x < 16; x++) {
    let r, g, b, a;
    
    // 创建一个简单的"I"字母图案
    if (
      // 上横线
      (y >= 2 && y <= 3 && x >= 4 && x <= 11) ||
      // 中间竖线
      (y >= 4 && y <= 11 && x >= 7 && x <= 8) ||
      // 下横线
      (y >= 12 && y <= 13 && x >= 4 && x <= 11)
    ) {
      // 白色字母
      r = 255; g = 255; b = 255; a = 255;
    } else {
      // 橙色背景
      r = 255; g = 107; b = 53; a = 255;
    }
    
    // BMP格式是BGRA顺序，从下到上
    imageData.push(b, g, r, a);
  }
}

// 反转行顺序 (BMP是从下到上存储的)
const reversedImageData = [];
for (let y = 15; y >= 0; y--) {
  for (let x = 0; x < 16; x++) {
    const index = (y * 16 + x) * 4;
    reversedImageData.push(
      imageData[index],     // B
      imageData[index + 1], // G
      imageData[index + 2], // R
      imageData[index + 3]  // A
    );
  }
}

// 添加AND掩码 (32字节，全部为0表示不透明)
const andMask = new Array(32).fill(0);

// 合并所有数据
const fullIcoData = Buffer.concat([
  icoData,
  Buffer.from(reversedImageData),
  Buffer.from(andMask)
]);

// 写入文件
const outputPath = path.join(__dirname, '../public/favicon.ico');
fs.writeFileSync(outputPath, fullIcoData);

console.log('✅ favicon.ico 生成成功!');
console.log(`📁 文件位置: ${outputPath}`);
console.log(`📊 文件大小: ${fullIcoData.length} 字节`);
