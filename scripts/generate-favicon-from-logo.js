const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function generateFavicon() {
  const inputPath = path.join(__dirname, '..', 'public', 'logo-url.ico');
  const outputDir = path.join(__dirname, '..', 'public');
  
  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(inputPath)) {
      console.error('输入文件不存在:', inputPath);
      return;
    }
    
    console.log('开始处理 favicon...');
    
    // 生成 16x16 favicon.ico
    await sharp(inputPath)
      .resize(16, 16, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(path.join(outputDir, 'favicon-16x16.png'));
    
    // 生成 32x32 favicon.ico
    await sharp(inputPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(path.join(outputDir, 'favicon-32x32.png'));
    
    // 生成主要的 favicon.ico (32x32)
    await sharp(inputPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(path.join(outputDir, 'favicon-new.ico'));
    
    // 生成 Apple Touch Icon (180x180)
    await sharp(inputPath)
      .resize(180, 180, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(path.join(outputDir, 'apple-touch-icon.png'));
    
    console.log('Favicon 生成完成!');
    console.log('生成的文件:');
    console.log('- favicon-16x16.png');
    console.log('- favicon-32x32.png');
    console.log('- favicon-new.ico');
    console.log('- apple-touch-icon.png');
    
  } catch (error) {
    console.error('生成 favicon 时出错:', error);
  }
}

generateFavicon();