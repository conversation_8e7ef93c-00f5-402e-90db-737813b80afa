#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要清理的目录和文件
const cleanTargets = [
  // Next.js 缓存目录
  '.next/cache',
  '.next/static/chunks/webpack',
  
  // 大文件模式
  '.next/cache/webpack',
  '.next/cache/webpack/client-production',
  '.next/cache/webpack/server-production',
  
  // 其他可能的大文件
  '.next/trace',
  '.next/build-manifest.json.gz',
  '.next/react-loadable-manifest.json.gz',
];

// 清理函数
function cleanDirectory(dirPath) {
  const fullPath = path.resolve(dirPath);
  
  if (fs.existsSync(fullPath)) {
    try {
      const stats = fs.statSync(fullPath);
      const sizeInMB = stats.size / (1024 * 1024);
      
      if (stats.isDirectory()) {
        // 检查目录大小
        const dirSize = getDirSize(fullPath);
        const dirSizeInMB = dirSize / (1024 * 1024);
        
        console.log(`📁 目录: ${dirPath} (${dirSizeInMB.toFixed(2)} MB)`);
        
        if (dirSizeInMB > 25) {
          console.log(`🗑️  删除大目录: ${dirPath}`);
          fs.rmSync(fullPath, { recursive: true, force: true });
        } else {
          fs.rmSync(fullPath, { recursive: true, force: true });
          console.log(`✅ 清理目录: ${dirPath}`);
        }
      } else if (stats.isFile()) {
        console.log(`📄 文件: ${dirPath} (${sizeInMB.toFixed(2)} MB)`);
        
        if (sizeInMB > 25) {
          console.log(`🗑️  删除大文件: ${dirPath}`);
          fs.unlinkSync(fullPath);
        }
      }
    } catch (error) {
      console.log(`⚠️  清理失败: ${dirPath} - ${error.message}`);
    }
  }
}

// 计算目录大小
function getDirSize(dirPath) {
  let totalSize = 0;
  
  try {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        totalSize += getDirSize(filePath);
      } else {
        totalSize += stats.size;
      }
    }
  } catch (error) {
    // 忽略错误
  }
  
  return totalSize;
}

// 主清理函数
function main() {
  console.log('🧹 开始清理构建缓存...\n');
  
  // 清理指定目标
  cleanTargets.forEach(target => {
    cleanDirectory(target);
  });
  
  // 查找并清理所有 .pack 文件
  console.log('\n🔍 查找 .pack 文件...');
  findAndCleanPackFiles('.next');
  
  console.log('\n✨ 缓存清理完成！');
}

// 查找并清理 .pack 文件
function findAndCleanPackFiles(dir) {
  if (!fs.existsSync(dir)) return;
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        findAndCleanPackFiles(filePath);
      } else if (file.endsWith('.pack')) {
        const sizeInMB = stats.size / (1024 * 1024);
        console.log(`📦 发现 .pack 文件: ${filePath} (${sizeInMB.toFixed(2)} MB)`);
        
        if (sizeInMB > 25) {
          fs.unlinkSync(filePath);
          console.log(`🗑️  删除大 .pack 文件: ${filePath}`);
        }
      }
    }
  } catch (error) {
    // 忽略错误
  }
}

// 运行清理
main();
