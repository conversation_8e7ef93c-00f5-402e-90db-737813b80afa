#!/usr/bin/env node

/**
 * Vercel 专用构建脚本
 * 简化构建过程，避免堆栈溢出问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始 Vercel 构建...');

// 设置环境变量
process.env.NODE_ENV = 'production';
process.env.NEXT_TELEMETRY_DISABLED = '1';

try {
  // 清理可能导致问题的缓存
  console.log('🧹 清理构建缓存...');
  
  const cacheDirs = [
    '.next/cache',
    'node_modules/.cache',
    '.cache'
  ];
  
  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`✅ 清理: ${dir}`);
      } catch (error) {
        console.log(`⚠️ 清理失败: ${dir} - ${error.message}`);
      }
    }
  });

  // 使用简化的配置文件
  console.log('📝 使用 Vercel 优化配置...');
  if (fs.existsSync('next.config.vercel.js')) {
    fs.copyFileSync('next.config.vercel.js', 'next.config.js');
    console.log('✅ 已切换到 Vercel 优化配置');
  }

  // 执行 Next.js 构建
  console.log('📦 执行 Next.js 构建...');
  execSync('npx next build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      // 禁用可能导致问题的功能
      NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK: '1',
      NEXT_PRIVATE_STANDALONE: 'false',
      // 禁用输出文件追踪
      NEXT_PRIVATE_OUTPUT_FILE_TRACING_ROOT: '',
      // 禁用构建追踪
      NEXT_BUILD_TRACE: 'false',
    }
  });

  console.log('✅ Vercel 构建完成！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
