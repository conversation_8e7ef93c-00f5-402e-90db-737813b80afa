const fs = require('fs');
const path = require('path');

// 读取所有翻译文件并合并到静态翻译文件中
function syncTranslations() {
  const localesDir = path.join(__dirname, '../src/locales');
  const enDir = path.join(localesDir, 'en');
  const zhDir = path.join(localesDir, 'zh');
  
  // 读取英文翻译文件
  const enTranslations = {};
  const enFiles = fs.readdirSync(enDir).filter(file => file.endsWith('.json'));
  
  enFiles.forEach(file => {
    const moduleName = path.basename(file, '.json');
    const content = JSON.parse(fs.readFileSync(path.join(enDir, file), 'utf8'));
    enTranslations[moduleName] = content;
  });
  
  // 读取中文翻译文件
  const zhTranslations = {};
  const zhFiles = fs.readdirSync(zhDir).filter(file => file.endsWith('.json'));
  
  zhFiles.forEach(file => {
    const moduleName = path.basename(file, '.json');
    const content = JSON.parse(fs.readFileSync(path.join(zhDir, file), 'utf8'));
    zhTranslations[moduleName] = content;
  });
  
  // 生成静态翻译文件内容
  const staticTranslationsContent = `// 静态翻译文件，用于解决生产环境下的翻译加载问题
export const staticTranslations = {
  en: ${JSON.stringify(enTranslations, null, 4)},
  zh: ${JSON.stringify(zhTranslations, null, 4)}
}
`;
  
  // 写入静态翻译文件
  const staticTranslationsPath = path.join(localesDir, 'static-translations.ts');
  fs.writeFileSync(staticTranslationsPath, staticTranslationsContent, 'utf8');
  
  console.log('✅ 翻译文件同步完成！');
  console.log(`📁 英文模块: ${Object.keys(enTranslations).join(', ')}`);
  console.log(`📁 中文模块: ${Object.keys(zhTranslations).join(', ')}`);
}

// 如果直接运行此脚本
if (require.main === module) {
  syncTranslations();
}

module.exports = { syncTranslations };
