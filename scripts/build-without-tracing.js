#!/usr/bin/env node

/**
 * 无构建追踪的 Next.js 构建脚本
 * 专门解决 Vercel 部署时的堆栈溢出问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始无追踪构建...');

// 设置环境变量
process.env.NODE_ENV = 'production';
process.env.NEXT_TELEMETRY_DISABLED = '1';

// 创建临时的 next.config.js
const tempConfig = `
/** @type {import('next').NextConfig} */
const nextConfig = {
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },
  
  // 完全禁用实验性功能
  experimental: {},
  
  // 禁用输出追踪
  output: undefined,
  
  // 基础图片配置
  images: {
    unoptimized: false,
    domains: [
      'vivid-pleasure-04cb3dbd82.strapiapp.com',
      'vivid-pleasure-04cb3dbd82.media.strapiapp.com',
    ],
  },
  
  // 基础设置
  compress: true,
  poweredByHeader: false,
  reactStrictMode: true,
  
  // 最小化 webpack 配置
  webpack: (config) => {
    config.resolve.fallback = { fs: false, path: false };
    return config;
  },
}

module.exports = nextConfig;
`;

try {
  // 备份原配置
  if (fs.existsSync('next.config.js')) {
    fs.copyFileSync('next.config.js', 'next.config.js.backup');
    console.log('📋 已备份原配置文件');
  }

  // 写入临时配置
  fs.writeFileSync('next.config.js', tempConfig);
  console.log('📝 已创建临时配置文件');

  // 清理缓存
  console.log('🧹 清理构建缓存...');
  const cacheDirs = ['.next/cache', 'node_modules/.cache'];
  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ 清理: ${dir}`);
    }
  });

  // 执行构建
  console.log('📦 执行 Next.js 构建...');
  execSync('npx next build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      // 禁用所有可能导致问题的功能
      NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK: '1',
      NEXT_PRIVATE_STANDALONE: 'false',
      NEXT_BUILD_TRACE: 'false',
    }
  });

  console.log('✅ 构建完成！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
} finally {
  // 恢复原配置
  if (fs.existsSync('next.config.js.backup')) {
    fs.copyFileSync('next.config.js.backup', 'next.config.js');
    fs.unlinkSync('next.config.js.backup');
    console.log('🔄 已恢复原配置文件');
  }
}
