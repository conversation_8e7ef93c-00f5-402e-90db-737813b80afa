const fs = require('fs')
const path = require('path')
const sharp = require('sharp')

// Configuration
const CONFIG = {
  inputDir: 'public/images',
  outputDir: 'public/images/optimized',
  formats: ['webp', 'avif'],
  qualities: {
    jpeg: 85,
    webp: 80,
    avif: 75
  },
  sizes: {
    thumbnail: 150,
    small: 400,
    medium: 800,
    large: 1200,
    xlarge: 1920
  }
}

// Ensure output directory exists
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
}

// Get all image files recursively
function getImageFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      getImageFiles(fullPath, files)
    } else if (/\.(jpg|jpeg|png)$/i.test(item)) {
      files.push(fullPath)
    }
  }
  
  return files
}

// Generate responsive images
async function generateResponsiveImages(inputPath, outputDir) {
  const filename = path.basename(inputPath, path.extname(inputPath))
  const relativePath = path.relative(CONFIG.inputDir, path.dirname(inputPath))
  const outputPath = path.join(outputDir, relativePath)
  
  ensureDir(outputPath)
  
  try {
    const image = sharp(inputPath)
    const metadata = await image.metadata()
    
    console.log(`Processing: ${inputPath}`)
    console.log(`Original: ${metadata.width}x${metadata.height}, ${Math.round(metadata.size / 1024)}KB`)
    
    const results = []
    
    // Generate different sizes
    for (const [sizeName, width] of Object.entries(CONFIG.sizes)) {
      // Skip if original is smaller than target size
      if (metadata.width < width) continue
      
      // Generate JPEG
      const jpegPath = path.join(outputPath, `${filename}-${sizeName}.jpg`)
      await image
        .resize(width, null, { withoutEnlargement: true })
        .jpeg({ quality: CONFIG.qualities.jpeg })
        .toFile(jpegPath)
      
      // Generate WebP
      const webpPath = path.join(outputPath, `${filename}-${sizeName}.webp`)
      await image
        .resize(width, null, { withoutEnlargement: true })
        .webp({ quality: CONFIG.qualities.webp })
        .toFile(webpPath)
      
      // Generate AVIF
      const avifPath = path.join(outputPath, `${filename}-${sizeName}.avif`)
      await image
        .resize(width, null, { withoutEnlargement: true })
        .avif({ quality: CONFIG.qualities.avif })
        .toFile(avifPath)
      
      results.push({
        size: sizeName,
        width,
        formats: ['jpg', 'webp', 'avif']
      })
    }
    
    // Generate blur placeholder
    const blurPath = path.join(outputPath, `${filename}-blur.jpg`)
    await image
      .resize(10, 10)
      .blur(1)
      .jpeg({ quality: 20 })
      .toFile(blurPath)
    
    console.log(`Generated ${results.length} sizes with 3 formats each + blur placeholder`)
    return results
    
  } catch (error) {
    console.error(`Error processing ${inputPath}:`, error.message)
    return []
  }
}

// Generate image manifest
function generateManifest(processedImages) {
  const manifest = {
    generated: new Date().toISOString(),
    images: processedImages,
    config: CONFIG
  }
  
  const manifestPath = path.join(CONFIG.outputDir, 'manifest.json')
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  console.log(`Generated manifest: ${manifestPath}`)
}

// Main optimization function
async function optimizeImages() {
  console.log('Starting image optimization...')
  console.log(`Input directory: ${CONFIG.inputDir}`)
  console.log(`Output directory: ${CONFIG.outputDir}`)
  
  ensureDir(CONFIG.outputDir)
  
  const imageFiles = getImageFiles(CONFIG.inputDir)
  console.log(`Found ${imageFiles.length} images to process`)
  
  const processedImages = []
  
  for (const imagePath of imageFiles) {
    const results = await generateResponsiveImages(imagePath, CONFIG.outputDir)
    if (results.length > 0) {
      processedImages.push({
        original: imagePath,
        sizes: results
      })
    }
  }
  
  generateManifest(processedImages)
  
  console.log(`\nOptimization complete!`)
  console.log(`Processed ${processedImages.length} images`)
  console.log(`Generated responsive variants in: ${CONFIG.outputDir}`)
}

// Run if called directly
if (require.main === module) {
  optimizeImages().catch(console.error)
}

module.exports = { optimizeImages, CONFIG }
