{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:fast": "cross-env NODE_ENV=development next dev --turbo --port 3000", "build": "next build", "build:clean": "next build && node scripts/clean-build.js", "build:dev": "cross-env NODE_ENV=development next build", "build:static": "next build", "build:cloudflare": "npm run build:clean && node scripts/deploy-cloudflare.js", "build:vercel": "node scripts/vercel-build.js", "start": "next start -p 3000", "start:dev": "next dev --turbo", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "security:audit": "npm audit", "security:fix": "npm audit fix", "bundle:analyze": "cross-env ANALYZE=true next build", "setup": "node scripts/setup.js", "clean": "rimraf .next out node_modules/.cache", "clean:all": "rimraf .next out node_modules/.cache && npm install", "deploy:cloudflare": "npm run build:cloudflare", "deploy:vercel": "vercel --prod", "deploy:vercel:preview": "vercel", "analyze": "cross-env ANALYZE=true next build", "dev:debug": "cross-env NODE_OPTIONS='--inspect' next dev --turbo", "optimize:images": "node scripts/optimize-images.js", "download:images": "node scripts/download-images.js", "verify:deployment": "node scripts/verify-deployment.js", "sync-translations": "node scripts/sync-translations.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "dotenv": "^17.2.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "next-mdx-remote": "^4.4.1", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/jest": "^29.5.12", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "eslint-plugin-testing-library": "^6.2.0", "eslint-plugin-jest-dom": "^5.1.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "rimraf": "^6.0.1", "serve": "^14.2.4", "sharp": "^0.33.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}