{"framework": "nextjs", "buildCommand": "NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK=1 NEXT_BUILD_TRACE=false NEXT_PRIVATE_OUTPUT_FILE_TRACING_ROOT= npx next build", "outputDirectory": ".next", "installCommand": "npm install", "env": {"NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK": "1", "NEXT_BUILD_TRACE": "false", "NEXT_PRIVATE_OUTPUT_FILE_TRACING_ROOT": "", "NEXT_TELEMETRY_DISABLED": "1"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}]}