# Dependencies
node_modules/

# Build outputs
.next/
out/

# Environment files
.env*.local
.env.production.local

# Logs
*.log
logs/

# Cache
.cache/
.parcel-cache/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Test files
coverage/
test/
tests/
__tests__/

# Documentation
*.md
docs/

# Scripts (keep only essential ones)
scripts/
!scripts/clean-build.js

# 其他项目目录
lattepanda-backend/
lattepanda-clone/
Linnuo-strapi/
任务书文件夹/

# 分析和文档文件
API_DOCUMENTATION.md
API_ISSUES_ANALYSIS.md
LANGUAGE_SWITCHING_ANALYSIS.md
VERCEL_DEPLOYMENT_GUIDE.md

# Backup files
*-backup.*
*.bak
*.tmp

# Large files
*.pack
*.gz
*.zip
