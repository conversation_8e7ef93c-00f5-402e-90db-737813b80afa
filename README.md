# 🖥️ LinnuoClone Website

A modern, responsive clone of the Linnuowebsite built with Next.js 14, TypeScript, and Tailwind CSS.

## ✨ Features

### 🎠 **Image Carousel**
- **Simplified pure image carousel** with clean data structure
- **Auto-play functionality** (5-second intervals)
- **Manual controls** (arrow buttons, indicators)
- **Keyboard navigation** (←→ spacebar)
- **Touch gestures** for mobile devices
- **Responsive design** for all screen sizes

### 🧭 **Navigation**
- **Sticky navigation header** with smooth transitions
- **Dropdown menus** with hover persistence
- **Mobile-responsive** hamburger menu
- **Search functionality** with Ctrl+K shortcut
- **No authentication required** - simplified user experience

## 🛠 技术栈

### 前端
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **图标**: Lucide React
- **组件库**: Radix UI

### 内容管理
- **文档**: MDX (Markdown + JSX)
- **内容**: 静态文件 + API Routes
- **搜索**: Algolia DocSearch

### 部署与优化
- **部署**: Vercel
- **图片**: Next.js Image + Cloudinary
- **SEO**: Next.js内置SEO优化
- **性能**: 自动代码分割和优化

## 📁 项目结构

```
Linnuo-clone/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (main)/            # 主网站路由组
│   │   │   ├── page.tsx       # 首页
│   │   │   ├── products/      # 产品页面
│   │   │   ├── blog/          # 博客页面
│   │   │   └── accessories/   # 配件页面
│   │   ├── docs/              # 文档系统
│   │   │   ├── page.tsx       # 文档首页
│   │   │   └── [...slug]/     # 动态文档路由
│   │   ├── globals.css        # 全局样式
│   │   └── layout.tsx         # 根布局
│   ├── components/            # 可复用组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── layout/           # 布局组件
│   │   ├── sections/         # 页面区块组件
│   │   └── docs/             # 文档专用组件
│   ├── lib/                  # 工具函数和配置
│   ├── data/                 # 静态数据
│   └── types/                # TypeScript类型定义
├── content/                  # Markdown内容
│   ├── docs/                # 文档内容
│   └── blog/                # 博客内容
├── public/                  # 静态资源
│   ├── images/             # 图片资源
│   └── icons/              # 图标资源
└── docs/                   # 项目文档
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm/yarn/pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站

### 构建生产版本
```bash
npm run build
npm run start
```

## 📋 功能清单

### 主网站功能
- [x] 响应式导航栏
- [x] 产品轮播展示
- [x] 产品详情页面
- [x] 博客系统
- [x] 项目展示
- [x] 配件展示
- [x] 社交媒体集成
- [x] 搜索功能

### 文档系统功能
- [x] 侧边栏导航
- [x] 文档搜索
- [x] 代码高亮
- [x] 目录导航
- [x] 响应式设计
- [x] GitHub集成

### 性能优化
- [x] 图片懒加载
- [x] 代码分割
- [x] SEO优化
- [x] 缓存策略
- [x] 压缩优化

## 🎨 设计系统

### 颜色方案
- 主色调: Linnuo橙色 (#FF6B35)
- 辅助色: 深蓝色 (#1E3A8A)
- 中性色: 灰色系列

### 字体
- 标题: Inter
- 正文: Inter
- 代码: JetBrains Mono

### 组件规范
- 按钮: 圆角8px，多种尺寸
- 卡片: 阴影效果，圆角12px
- 输入框: 边框样式，聚焦效果

## 📱 响应式设计

- **桌面端**: 1200px+
- **平板端**: 768px - 1199px
- **移动端**: 320px - 767px

## 🔧 开发指南

### 添加新页面
1. 在 `src/app/` 下创建新的路由文件夹
2. 添加 `page.tsx` 文件
3. 配置路由和导航

### 添加新组件
1. 在 `src/components/` 下创建组件文件
2. 使用 TypeScript 定义 props 类型
3. 添加 Storybook 故事（可选）

### 添加文档内容
1. 在 `content/docs/` 下创建 Markdown 文件
2. 添加 frontmatter 元数据
3. 更新导航配置

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目仅用于学习和演示目的。请遵守原网站的版权和使用条款。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件
- 加入讨论群

---

**注意**: 这是一个学习项目，用于技术研究和教育目的。请尊重原网站的知识产权。
