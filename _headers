# Cloudflare Pages Headers Configuration

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Access-Control-Allow-Origin: https://vivid-pleasure-04cb3dbd82.strapiapp.com
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

# Cache static assets
/images/*
  Cache-Control: public, max-age=31536000, immutable

/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache HTML files for shorter time
/*.html
  Cache-Control: public, max-age=3600

# Service Worker
/sw.js
  Cache-Control: public, max-age=0, must-revalidate

# Next.js RSC payload files (.txt)
/*.txt
  Content-Type: text/plain; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable
