# Production environment configuration for Vercel

# Backend API configuration
NEXT_PUBLIC_API_BASE_URL=http://*************:1337
NEXT_PUBLIC_ENABLE_STRAPI=true

# Strapi backend configuration
NEXT_PUBLIC_STRAPI_URL=http://*************:1337
NEXT_PUBLIC_STRAPI_API_TOKEN=8d51dcc71ba9ed091e1312a3f56dcd11c1d741ba1bca5150e208725faebb6492bebb714dc694fe170afe3520e18c36d2b7319554d34a4dffc47e50647c9dc877dcf9a758dd26cb591141f5bb9532dc756e5b798525025cc3025fcc01389dae1237e8fe235f7cfbd1c5e4263ae644c7e4f771b3348521a45fcace6c090e72364f
# Vercel specific settings
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_DEPLOYMENT_TARGET=vercel

# Build configuration
NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK=1

# Image optimization
NEXT_PUBLIC_IMAGE_DOMAINS=*************
