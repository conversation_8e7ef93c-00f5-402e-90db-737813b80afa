<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1A202C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="serverGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGlow" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4299E1;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#63B3ED;stop-opacity:0.3" />
    </linearGradient>
    <radialGradient id="lightGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#48BB78;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#48BB78;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#bgGrad)"/>
  
  <!-- Floor -->
  <rect x="0" y="700" width="1200" height="100" fill="#1A202C"/>
  
  <!-- Server Racks Row 1 -->
  <g id="serverRack1">
    <!-- Rack 1 -->
    <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
    <!-- Server Units -->
    <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="115" y="225" width="8" height="8" fill="#48BB78" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="125" y="225" width="8" height="8" fill="#4299E1" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="135" y="225" width="8" height="8" fill="#ED8936" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    
    <rect x="110" y="250" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="115" y="255" width="8" height="8" fill="#48BB78" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.8s" repeatCount="indefinite"/>
    </rect>
    <rect x="125" y="255" width="8" height="8" fill="#4299E1" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2.2s" repeatCount="indefinite"/>
    </rect>
    
    <rect x="110" y="280" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="110" y="310" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="110" y="340" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
  </g>
  
  <!-- Rack 2 -->
  <g transform="translate(150,0)">
    <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
    <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="115" y="225" width="8" height="8" fill="#E53E3E" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.2s" repeatCount="indefinite"/>
    </rect>
    <rect x="125" y="225" width="8" height="8" fill="#48BB78" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2.8s" repeatCount="indefinite"/>
    </rect>
    <rect x="110" y="250" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="110" y="280" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
  </g>
  
  <!-- Rack 3 -->
  <g transform="translate(300,0)">
    <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
    <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="115" y="225" width="8" height="8" fill="#4299E1" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.7s" repeatCount="indefinite"/>
    </rect>
    <rect x="125" y="225" width="8" height="8" fill="#48BB78" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2.1s" repeatCount="indefinite"/>
    </rect>
    <rect x="135" y="225" width="8" height="8" fill="#ED8936" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.9s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Server Racks Row 2 -->
  <g transform="translate(0,100)">
    <!-- Rack 4 -->
    <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
    <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
    <rect x="115" y="225" width="8" height="8" fill="#48BB78" rx="1">
      <animate attributeName="opacity" values="1;0.3;1" dur="2.3s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Rack 5 -->
    <g transform="translate(150,0)">
      <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
      <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
      <rect x="115" y="225" width="8" height="8" fill="#4299E1" rx="1">
        <animate attributeName="opacity" values="1;0.3;1" dur="1.4s" repeatCount="indefinite"/>
      </rect>
    </g>
    
    <!-- Rack 6 -->
    <g transform="translate(300,0)">
      <rect x="100" y="200" width="80" height="500" fill="url(#serverGrad)" stroke="#718096" stroke-width="2" rx="5"/>
      <rect x="110" y="220" width="60" height="25" fill="#2D3748" stroke="#4A5568" stroke-width="1" rx="2"/>
      <rect x="115" y="225" width="8" height="8" fill="#E53E3E" rx="1">
        <animate attributeName="opacity" values="1;0.3;1" dur="2.6s" repeatCount="indefinite"/>
      </rect>
    </g>
  </g>
  
  <!-- Central Control Station -->
  <rect x="600" y="300" width="200" height="150" fill="#2D3748" stroke="#4A5568" stroke-width="2" rx="10"/>
  
  <!-- Multiple Monitors -->
  <rect x="620" y="320" width="60" height="40" fill="#000" rx="3"/>
  <rect x="625" y="325" width="50" height="30" fill="url(#screenGlow)" rx="2"/>
  <text x="650" y="340" font-family="monospace" font-size="8" fill="#FFF" text-anchor="middle">CPU</text>
  <text x="650" y="350" font-family="monospace" font-size="6" fill="#48BB78" text-anchor="middle">85%</text>
  
  <rect x="690" y="320" width="60" height="40" fill="#000" rx="3"/>
  <rect x="695" y="325" width="50" height="30" fill="url(#screenGlow)" rx="2"/>
  <text x="720" y="340" font-family="monospace" font-size="8" fill="#FFF" text-anchor="middle">RAM</text>
  <text x="720" y="350" font-family="monospace" font-size="6" fill="#4299E1" text-anchor="middle">72%</text>
  
  <rect x="620" y="370" width="60" height="40" fill="#000" rx="3"/>
  <rect x="625" y="375" width="50" height="30" fill="url(#screenGlow)" rx="2"/>
  <text x="650" y="390" font-family="monospace" font-size="8" fill="#FFF" text-anchor="middle">NET</text>
  <text x="650" y="400" font-family="monospace" font-size="6" fill="#ED8936" text-anchor="middle">1.2GB/s</text>
  
  <rect x="690" y="370" width="60" height="40" fill="#000" rx="3"/>
  <rect x="695" y="375" width="50" height="30" fill="url(#screenGlow)" rx="2"/>
  <text x="720" y="390" font-family="monospace" font-size="8" fill="#FFF" text-anchor="middle">TEMP</text>
  <text x="720" y="400" font-family="monospace" font-size="6" fill="#E53E3E" text-anchor="middle">24°C</text>
  
  <!-- Control Panel -->
  <rect x="760" y="420" width="30" height="20" fill="#4A5568" rx="3"/>
  <circle cx="770" cy="425" r="3" fill="#48BB78">
    <animate attributeName="fill" values="#48BB78;#E53E3E;#48BB78" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="780" cy="425" r="3" fill="#4299E1">
    <animate attributeName="fill" values="#4299E1;#ED8936;#4299E1" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Cooling System -->
  <rect x="900" y="150" width="150" height="80" fill="#4A5568" stroke="#718096" stroke-width="2" rx="10"/>
  <text x="975" y="175" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">COOLING</text>
  
  <!-- Cooling Fans -->
  <circle cx="930" cy="200" r="15" fill="#2D3748" stroke="#4A5568" stroke-width="2"/>
  <g transform="translate(930,200)">
    <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#718096" stroke-width="2">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="1s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <circle cx="970" cy="200" r="15" fill="#2D3748" stroke="#4A5568" stroke-width="2"/>
  <g transform="translate(970,200)">
    <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#718096" stroke-width="2">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="0.8s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <circle cx="1010" cy="200" r="15" fill="#2D3748" stroke="#4A5568" stroke-width="2"/>
  <g transform="translate(1010,200)">
    <path d="M-8,-8 L8,8 M8,-8 L-8,8" stroke="#718096" stroke-width="2">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="1.2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Air Flow Visualization -->
  <g opacity="0.6">
    <path d="M 930,240 Q 935,250 940,240" stroke="#87CEEB" stroke-width="2" fill="none">
      <animate attributeName="d" values="M 930,240 Q 935,250 940,240; M 930,240 Q 935,230 940,240; M 930,240 Q 935,250 940,240" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M 970,240 Q 975,250 980,240" stroke="#87CEEB" stroke-width="2" fill="none">
      <animate attributeName="d" values="M 970,240 Q 975,250 980,240; M 970,240 Q 975,230 980,240; M 970,240 Q 975,250 980,240" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </path>
  </g>
  
  <!-- Network Cables -->
  <g stroke="#ED8936" stroke-width="3" fill="none">
    <path d="M 180,400 Q 300,350 600,380">
      <animate attributeName="stroke" values="#ED8936;#4299E1;#ED8936" dur="3s" repeatCount="indefinite"/>
    </path>
    <path d="M 330,400 Q 450,350 600,400">
      <animate attributeName="stroke" values="#4299E1;#48BB78;#4299E1" dur="2.5s" repeatCount="indefinite"/>
    </path>
    <path d="M 480,400 Q 550,380 600,420">
      <animate attributeName="stroke" values="#48BB78;#ED8936;#48BB78" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Data Flow Indicators -->
  <g>
    <circle cx="300" cy="370" r="4" fill="#48BB78">
      <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="450" cy="340" r="4" fill="#4299E1">
      <animate attributeName="r" values="4;8;4" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="550" cy="390" r="4" fill="#ED8936">
      <animate attributeName="r" values="4;8;4" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Emergency Lighting -->
  <circle cx="200" cy="100" r="20" fill="url(#lightGlow)">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="600" cy="100" r="20" fill="url(#lightGlow)">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite" begin="1s"/>
  </circle>
  <circle cx="1000" cy="100" r="20" fill="url(#lightGlow)">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite" begin="2s"/>
  </circle>
  
  <!-- Security Camera -->
  <g transform="translate(50,150)">
    <rect x="0" y="0" width="30" height="20" fill="#2D3748" rx="5"/>
    <circle cx="25" cy="10" r="8" fill="#4A5568"/>
    <circle cx="25" cy="10" r="5" fill="#E53E3E">
      <animate attributeName="fill" values="#E53E3E;#48BB78;#E53E3E" dur="2s" repeatCount="indefinite"/>
    </circle>
    <rect x="-5" y="5" width="10" height="10" fill="#4A5568" rx="2"/>
  </g>
  
  <!-- Fire Suppression System -->
  <g transform="translate(1100,250)">
    <rect x="0" y="0" width="15" height="40" fill="#E53E3E" rx="3"/>
    <circle cx="7.5" cy="50" r="8" fill="#E53E3E"/>
    <text x="7.5" y="55" font-family="Arial" font-size="8" fill="#FFF" text-anchor="middle">F</text>
  </g>
  
  <!-- Power Distribution Unit -->
  <rect x="850" y="600" width="100" height="60" fill="#4A5568" stroke="#718096" stroke-width="2" rx="5"/>
  <text x="900" y="620" font-family="Arial" font-size="10" fill="#FFF" text-anchor="middle">PDU</text>
  <rect x="860" y="630" width="15" height="8" fill="#48BB78" rx="1"/>
  <rect x="880" y="630" width="15" height="8" fill="#4299E1" rx="1"/>
  <rect x="900" y="630" width="15" height="8" fill="#ED8936" rx="1"/>
  <rect x="920" y="630" width="15" height="8" fill="#E53E3E" rx="1"/>
  
  <!-- Status Indicators -->
  <text x="100" y="50" font-family="monospace" font-size="14" fill="#48BB78">SYSTEM STATUS: OPERATIONAL</text>
  <text x="100" y="70" font-family="monospace" font-size="12" fill="#4299E1">LOAD: 78% | TEMP: 22°C | UPTIME: 99.9%</text>
</svg>