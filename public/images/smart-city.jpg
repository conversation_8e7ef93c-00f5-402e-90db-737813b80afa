<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#E0F6FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F7FAFC;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGrad1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E2E8F0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CBD5E0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4299E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182CE;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="roadGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="signalGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#48BB78;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#48BB78;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1200" height="800" fill="url(#skyGradient)"/>
  
  <!-- Ground -->
  <rect x="0" y="650" width="1200" height="150" fill="#A0AEC0"/>
  
  <!-- Main Road -->
  <rect x="0" y="600" width="1200" height="80" fill="url(#roadGrad)"/>
  
  <!-- Road Markings -->
  <g stroke="#FFF" stroke-width="3" stroke-dasharray="20,15">
    <line x1="0" y1="640" x2="1200" y2="640"/>
  </g>
  
  <!-- Building 1 - Smart Office -->
  <rect x="100" y="200" width="120" height="400" fill="url(#buildingGrad1)" stroke="#A0AEC0" stroke-width="2" rx="5"/>
  <!-- Windows with smart lighting -->
  <g>
    <rect x="115" y="230" width="20" height="25" fill="#4A90E2" opacity="0.8" rx="2"/>
    <rect x="145" y="230" width="20" height="25" fill="#48BB78" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
    </rect>
    <rect x="175" y="230" width="20" height="25" fill="#4A90E2" opacity="0.8" rx="2"/>
    
    <rect x="115" y="270" width="20" height="25" fill="#48BB78" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="145" y="270" width="20" height="25" fill="#4A90E2" opacity="0.8" rx="2"/>
    <rect x="175" y="270" width="20" height="25" fill="#ED8936" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2.5s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Building 2 - Residential Smart Tower -->
  <rect x="280" y="150" width="100" height="450" fill="url(#buildingGrad2)" stroke="#2B6CB0" stroke-width="2" rx="5"/>
  <!-- Balconies with IoT sensors -->
  <g>
    <rect x="290" y="200" width="80" height="8" fill="#2D3748" rx="2"/>
    <circle cx="295" cy="204" r="3" fill="#48BB78">
      <animate attributeName="fill" values="#48BB78;#E53E3E;#48BB78" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <rect x="290" y="250" width="80" height="8" fill="#2D3748" rx="2"/>
    <circle cx="365" cy="254" r="3" fill="#4299E1">
      <animate attributeName="fill" values="#4299E1;#ED8936;#4299E1" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <rect x="290" y="300" width="80" height="8" fill="#2D3748" rx="2"/>
    <circle cx="330" cy="304" r="3" fill="#ED8936">
      <animate attributeName="fill" values="#ED8936;#48BB78;#ED8936" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Building 3 - Smart Mall -->
  <rect x="450" y="250" width="150" height="350" fill="url(#buildingGrad1)" stroke="#A0AEC0" stroke-width="2" rx="5"/>
  <!-- Digital Displays -->
  <rect x="470" y="280" width="110" height="60" fill="#000" rx="5"/>
  <rect x="475" y="285" width="100" height="50" fill="#4A90E2" rx="3">
    <animate attributeName="fill" values="#4A90E2;#48BB78;#ED8936;#4A90E2" dur="5s" repeatCount="indefinite"/>
  </rect>
  <text x="525" y="315" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">SMART MALL</text>
  
  <!-- Building 4 - Data Center -->
  <rect x="650" y="300" width="130" height="300" fill="#2D3748" stroke="#4A5568" stroke-width="2" rx="5"/>
  <!-- Cooling vents -->
  <g>
    <rect x="665" y="320" width="15" height="8" fill="#4A5568" rx="2"/>
    <rect x="685" y="320" width="15" height="8" fill="#4A5568" rx="2"/>
    <rect x="705" y="320" width="15" height="8" fill="#4A5568" rx="2"/>
    <rect x="725" y="320" width="15" height="8" fill="#4A5568" rx="2"/>
    <rect x="745" y="320" width="15" height="8" fill="#4A5568" rx="2"/>
  </g>
  <!-- Server status lights -->
  <circle cx="700" cy="350" r="5" fill="#48BB78">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="720" cy="350" r="5" fill="#4299E1">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="740" cy="350" r="5" fill="#ED8936">
    <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Building 5 - Hospital -->
  <rect x="850" y="180" width="140" height="420" fill="url(#buildingGrad1)" stroke="#A0AEC0" stroke-width="2" rx="5"/>
  <!-- Medical cross -->
  <rect x="910" y="220" width="8" height="40" fill="#E53E3E"/>
  <rect x="895" y="235" width="38" height="8" fill="#E53E3E"/>
  <!-- Emergency lights -->
  <circle cx="870" cy="200" r="8" fill="#E53E3E">
    <animate attributeName="opacity" values="1;0.2;1" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="950" cy="200" r="8" fill="#E53E3E">
    <animate attributeName="opacity" values="1;0.2;1" dur="1s" repeatCount="indefinite" begin="0.5s"/>
  </circle>
  
  <!-- Building 6 - School -->
  <rect x="1050" y="220" width="120" height="380" fill="url(#buildingGrad2)" stroke="#2B6CB0" stroke-width="2" rx="5"/>
  <!-- School flag -->
  <rect x="1100" y="180" width="30" height="20" fill="#E53E3E"/>
  <rect x="1105" y="200" width="3" height="40" fill="#4A5568"/>
  
  <!-- Smart Traffic Lights -->
  <g transform="translate(200,520)">
    <rect x="0" y="0" width="15" height="60" fill="#2D3748" rx="3"/>
    <circle cx="7.5" cy="15" r="6" fill="#E53E3E">
      <animate attributeName="fill" values="#E53E3E;#4A5568;#E53E3E" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="7.5" cy="30" r="6" fill="#ED8936">
      <animate attributeName="fill" values="#4A5568;#ED8936;#4A5568" dur="6s" repeatCount="indefinite" begin="2s"/>
    </circle>
    <circle cx="7.5" cy="45" r="6" fill="#48BB78">
      <animate attributeName="fill" values="#4A5568;#48BB78;#4A5568" dur="6s" repeatCount="indefinite" begin="4s"/>
    </circle>
    <rect x="-5" y="60" width="25" height="40" fill="#4A5568" rx="2"/>
  </g>
  
  <!-- Smart Street Lights -->
  <g transform="translate(400,500)">
    <rect x="0" y="0" width="8" height="100" fill="#4A5568" rx="2"/>
    <circle cx="4" cy="-10" r="15" fill="url(#signalGlow)">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4s" repeatCount="indefinite"/>
    </circle>
    <!-- IoT sensor -->
    <rect x="-5" y="20" width="18" height="12" fill="#2D3748" rx="3"/>
    <circle cx="4" cy="26" r="3" fill="#4299E1">
      <animate attributeName="fill" values="#4299E1;#48BB78;#4299E1" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <g transform="translate(800,500)">
    <rect x="0" y="0" width="8" height="100" fill="#4A5568" rx="2"/>
    <circle cx="4" cy="-10" r="15" fill="url(#signalGlow)">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4s" repeatCount="indefinite" begin="2s"/>
    </circle>
    <rect x="-5" y="20" width="18" height="12" fill="#2D3748" rx="3"/>
    <circle cx="4" cy="26" r="3" fill="#ED8936">
      <animate attributeName="fill" values="#ED8936;#4299E1;#ED8936" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Smart Vehicles -->
  <!-- Electric Bus -->
  <g transform="translate(50,620)">
    <rect x="0" y="0" width="80" height="25" fill="#48BB78" rx="5"/>
    <rect x="10" y="5" width="60" height="15" fill="#E2E8F0" rx="2"/>
    <circle cx="15" cy="30" r="8" fill="#2D3748"/>
    <circle cx="65" cy="30" r="8" fill="#2D3748"/>
    <!-- Electric indicator -->
    <circle cx="40" cy="-8" r="5" fill="#48BB78">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <animateTransform attributeName="transform" type="translate" values="50,620; 300,620; 50,620" dur="15s" repeatCount="indefinite"/>
  </g>
  
  <!-- Autonomous Car -->
  <g transform="translate(900,630)">
    <rect x="0" y="0" width="50" height="20" fill="#4299E1" rx="8"/>
    <rect x="5" y="3" width="40" height="14" fill="#E2E8F0" rx="3"/>
    <circle cx="12" cy="25" r="6" fill="#2D3748"/>
    <circle cx="38" cy="25" r="6" fill="#2D3748"/>
    <!-- Sensor array -->
    <circle cx="25" cy="-5" r="3" fill="#E53E3E">
      <animate attributeName="fill" values="#E53E3E;#48BB78;#4299E1;#E53E3E" dur="2s" repeatCount="indefinite"/>
    </circle>
    <animateTransform attributeName="transform" type="translate" values="900,630; 600,630; 900,630" dur="12s" repeatCount="indefinite"/>
  </g>
  
  <!-- Delivery Drone -->
  <g transform="translate(300,300)">
    <rect x="0" y="0" width="20" height="8" fill="#2D3748" rx="2"/>
    <circle cx="5" cy="-5" r="8" fill="#4A5568" opacity="0.3"/>
    <circle cx="15" cy="-5" r="8" fill="#4A5568" opacity="0.3"/>
    <circle cx="5" cy="13" r="8" fill="#4A5568" opacity="0.3"/>
    <circle cx="15" cy="13" r="8" fill="#4A5568" opacity="0.3"/>
    <!-- Package -->
    <rect x="6" y="10" width="8" height="6" fill="#ED8936" rx="1"/>
    <animateTransform attributeName="transform" type="translate" values="300,300; 600,250; 900,300; 300,300" dur="20s" repeatCount="indefinite"/>
  </g>
  
  <!-- 5G Cell Tower -->
  <g transform="translate(1100,100)">
    <rect x="0" y="0" width="8" height="200" fill="#4A5568" rx="2"/>
    <!-- Antennas -->
    <rect x="-15" y="20" width="38" height="6" fill="#2D3748" rx="3"/>
    <rect x="-12" y="40" width="32" height="6" fill="#2D3748" rx="3"/>
    <rect x="-10" y="60" width="28" height="6" fill="#2D3748" rx="3"/>
    <!-- Signal waves -->
    <g opacity="0.6">
      <circle cx="4" cy="50" r="30" fill="none" stroke="#4299E1" stroke-width="2">
        <animate attributeName="r" values="30;60;30" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0;0.6" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="4" cy="50" r="45" fill="none" stroke="#48BB78" stroke-width="2">
        <animate attributeName="r" values="45;75;45" dur="3s" repeatCount="indefinite" begin="1s"/>
        <animate attributeName="opacity" values="0.6;0;0.6" dur="3s" repeatCount="indefinite" begin="1s"/>
      </circle>
    </g>
  </g>
  
  <!-- Solar Panels -->
  <g transform="translate(500,180)">
    <rect x="0" y="0" width="80" height="40" fill="#2D3748" rx="3"/>
    <g fill="#4299E1" opacity="0.8">
      <rect x="5" y="5" width="15" height="15" rx="1"/>
      <rect x="25" y="5" width="15" height="15" rx="1"/>
      <rect x="45" y="5" width="15" height="15" rx="1"/>
      <rect x="65" y="5" width="10" height="15" rx="1"/>
      <rect x="5" y="22" width="15" height="15" rx="1"/>
      <rect x="25" y="22" width="15" height="15" rx="1"/>
      <rect x="45" y="22" width="15" height="15" rx="1"/>
      <rect x="65" y="22" width="10" height="15" rx="1"/>
    </g>
    <!-- Energy flow indicator -->
    <circle cx="40" cy="45" r="3" fill="#48BB78">
      <animate attributeName="r" values="3;6;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Wind Turbine -->
  <g transform="translate(50,50)">
    <rect x="0" y="0" width="6" height="150" fill="#E2E8F0" rx="3"/>
    <circle cx="3" cy="0" r="8" fill="#4A5568"/>
    <g transform="translate(3,0)">
      <path d="M0,0 L-25,-5 L-20,-15 L0,0 L20,-15 L25,-5 L0,0 L5,25 L-5,25 Z" fill="#E2E8F0">
        <animateTransform attributeName="transform" type="rotate" values="0;360" dur="3s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- Data Flow Visualization -->
  <g opacity="0.7">
    <!-- Connection lines between buildings -->
    <path d="M 160,400 Q 300,350 450,400" stroke="#4299E1" stroke-width="3" fill="none" stroke-dasharray="10,5">
      <animate attributeName="stroke-dashoffset" values="0;-15" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M 525,450 Q 650,400 720,450" stroke="#48BB78" stroke-width="3" fill="none" stroke-dasharray="10,5">
      <animate attributeName="stroke-dashoffset" values="0;-15" dur="1.5s" repeatCount="indefinite"/>
    </path>
    <path d="M 780,450 Q 850,400 920,450" stroke="#ED8936" stroke-width="3" fill="none" stroke-dasharray="10,5">
      <animate attributeName="stroke-dashoffset" values="0;-15" dur="2.5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Smart City Control Center Display -->
  <rect x="20" y="20" width="200" height="80" fill="#2D3748" stroke="#4A5568" stroke-width="2" rx="5" opacity="0.9"/>
  <text x="30" y="40" font-family="monospace" font-size="12" fill="#48BB78">SMART CITY CONTROL</text>
  <text x="30" y="55" font-family="monospace" font-size="10" fill="#4299E1">Traffic: Optimal</text>
  <text x="30" y="70" font-family="monospace" font-size="10" fill="#48BB78">Energy: 85% Renewable</text>
  <text x="30" y="85" font-family="monospace" font-size="10" fill="#ED8936">Air Quality: Good</text>
  
  <!-- IoT Sensor Network Indicators -->
  <g>
    <circle cx="160" cy="350" r="3" fill="#48BB78">
      <animate attributeName="r" values="3;6;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="340" cy="300" r="3" fill="#4299E1">
      <animate attributeName="r" values="3;6;3" dur="1.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="525" cy="380" r="3" fill="#ED8936">
      <animate attributeName="r" values="3;6;3" dur="2.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="720" cy="420" r="3" fill="#E53E3E">
      <animate attributeName="r" values="3;6;3" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="920" cy="350" r="3" fill="#48BB78">
      <animate attributeName="r" values="3;6;3" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>