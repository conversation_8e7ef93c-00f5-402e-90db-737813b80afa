<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="serverGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowEffect" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>
  
  <!-- Central server rack -->
  <rect x="350" y="150" width="100" height="180" rx="5" fill="url(#serverGradient)" stroke="#64748b" stroke-width="2"/>
  
  <!-- Server units -->
  <rect x="360" y="160" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="180" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="200" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="220" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="240" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="260" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="280" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  <rect x="360" y="300" width="80" height="15" fill="#1e293b" stroke="#475569" stroke-width="1"/>
  
  <!-- LED indicators -->
  <circle cx="370" cy="167" r="2" fill="#10b981"/>
  <circle cx="375" cy="167" r="2" fill="#f59e0b"/>
  <circle cx="380" cy="167" r="2" fill="#10b981"/>
  <circle cx="370" cy="187" r="2" fill="#10b981"/>
  <circle cx="375" cy="187" r="2" fill="#10b981"/>
  <circle cx="380" cy="187" r="2" fill="#ef4444"/>
  
  <!-- Edge devices -->
  <!-- Left edge device -->
  <rect x="100" y="200" width="60" height="40" rx="3" fill="url(#serverGradient)" stroke="#64748b" stroke-width="1"/>
  <rect x="110" y="210" width="40" height="8" fill="url(#screenGradient)"/>
  <circle cx="115" cy="225" r="2" fill="#10b981"/>
  <circle cx="125" cy="225" r="2" fill="#3b82f6"/>
  <circle cx="135" cy="225" r="2" fill="#10b981"/>
  <circle cx="145" cy="225" r="2" fill="#f59e0b"/>
  
  <!-- Right edge device -->
  <rect x="640" y="180" width="60" height="40" rx="3" fill="url(#serverGradient)" stroke="#64748b" stroke-width="1"/>
  <rect x="650" y="190" width="40" height="8" fill="url(#screenGradient)"/>
  <circle cx="655" cy="205" r="2" fill="#10b981"/>
  <circle cx="665" cy="205" r="2" fill="#10b981"/>
  <circle cx="675" cy="205" r="2" fill="#3b82f6"/>
  <circle cx="685" cy="205" r="2" fill="#10b981"/>
  
  <!-- Top edge device -->
  <rect x="380" y="80" width="40" height="30" rx="3" fill="url(#serverGradient)" stroke="#64748b" stroke-width="1"/>
  <rect x="385" y="85" width="30" height="6" fill="url(#screenGradient)"/>
  <circle cx="390" cy="100" r="1.5" fill="#10b981"/>
  <circle cx="395" cy="100" r="1.5" fill="#3b82f6"/>
  <circle cx="400" cy="100" r="1.5" fill="#10b981"/>
  <circle cx="405" cy="100" r="1.5" fill="#f59e0b"/>
  
  <!-- Bottom edge device -->
  <rect x="370" y="350" width="60" height="30" rx="3" fill="url(#serverGradient)" stroke="#64748b" stroke-width="1"/>
  <rect x="380" y="355" width="40" height="6" fill="url(#screenGradient)"/>
  <circle cx="385" cy="370" r="1.5" fill="#10b981"/>
  <circle cx="395" cy="370" r="1.5" fill="#10b981"/>
  <circle cx="405" cy="370" r="1.5" fill="#3b82f6"/>
  <circle cx="415" cy="370" r="1.5" fill="#10b981"/>
  
  <!-- Connection lines with data flow animation effect -->
  <line x1="160" y1="220" x2="350" y2="240" stroke="#3b82f6" stroke-width="2" opacity="0.8"/>
  <line x1="640" y1="200" x2="450" y2="220" stroke="#3b82f6" stroke-width="2" opacity="0.8"/>
  <line x1="400" y1="110" x2="400" y2="150" stroke="#3b82f6" stroke-width="2" opacity="0.8"/>
  <line x1="400" y1="330" x2="400" y2="350" stroke="#3b82f6" stroke-width="2" opacity="0.8"/>
  
  <!-- Data flow indicators -->
  <circle cx="200" cy="225" r="3" fill="#3b82f6" opacity="0.8">
    <animate attributeName="cx" values="160;350" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="580" cy="210" r="3" fill="#3b82f6" opacity="0.8">
    <animate attributeName="cx" values="640;450" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="130" r="3" fill="#3b82f6" opacity="0.8">
    <animate attributeName="cy" values="110;150" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Network nodes -->
  <circle cx="250" cy="150" r="8" fill="#1e293b" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="550" cy="280" r="8" fill="#1e293b" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="200" cy="320" r="8" fill="#1e293b" stroke="#3b82f6" stroke-width="2"/>
  <circle cx="600" cy="120" r="8" fill="#1e293b" stroke="#3b82f6" stroke-width="2"/>
  
  <!-- Glow effects -->
  <circle cx="400" cy="240" r="80" fill="url(#glowEffect)" opacity="0.3"/>
  
  <!-- Data visualization elements -->
  <rect x="50" y="50" width="120" height="80" rx="5" fill="#1e293b" stroke="#3b82f6" stroke-width="1" opacity="0.8"/>
  <text x="110" y="75" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Edge Node 1</text>
  <text x="110" y="90" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8">Status: Active</text>
  <text x="110" y="105" text-anchor="middle" fill="#f59e0b" font-family="Arial" font-size="8">Load: 67%</text>
  <text x="110" y="120" text-anchor="middle" fill="#ef4444" font-family="Arial" font-size="8">Latency: 2ms</text>
  
  <rect x="630" y="300" width="120" height="80" rx="5" fill="#1e293b" stroke="#3b82f6" stroke-width="1" opacity="0.8"/>
  <text x="690" y="325" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Edge Node 2</text>
  <text x="690" y="340" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8">Status: Active</text>
  <text x="690" y="355" text-anchor="middle" fill="#f59e0b" font-family="Arial" font-size="8">Load: 43%</text>
  <text x="690" y="370" text-anchor="middle" fill="#ef4444" font-family="Arial" font-size="8">Latency: 1ms</text>
</svg>
