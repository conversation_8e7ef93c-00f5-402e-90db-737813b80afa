<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1f2937;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="smokeGradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="800" height="400" fill="url(#skyGradient)"/>
  
  <!-- Factory buildings -->
  <rect x="50" y="200" width="120" height="200" fill="url(#buildingGradient)"/>
  <rect x="200" y="180" width="100" height="220" fill="url(#buildingGradient)"/>
  <rect x="330" y="160" width="140" height="240" fill="url(#buildingGradient)"/>
  <rect x="500" y="190" width="110" height="210" fill="url(#buildingGradient)"/>
  <rect x="640" y="170" width="130" height="230" fill="url(#buildingGradient)"/>
  
  <!-- Smokestacks -->
  <rect x="90" y="120" width="15" height="80" fill="#4b5563"/>
  <rect x="120" y="100" width="15" height="100" fill="#4b5563"/>
  <rect x="240" y="110" width="12" height="70" fill="#4b5563"/>
  <rect x="380" y="90" width="18" height="70" fill="#4b5563"/>
  <rect x="550" y="130" width="14" height="60" fill="#4b5563"/>
  <rect x="700" y="100" width="16" height="70" fill="#4b5563"/>
  
  <!-- Smoke -->
  <ellipse cx="97" cy="110" rx="8" ry="15" fill="url(#smokeGradient)"/>
  <ellipse cx="127" cy="90" rx="10" ry="20" fill="url(#smokeGradient)"/>
  <ellipse cx="246" cy="100" rx="6" ry="12" fill="url(#smokeGradient)"/>
  <ellipse cx="389" cy="80" rx="12" ry="18" fill="url(#smokeGradient)"/>
  <ellipse cx="557" cy="120" rx="8" ry="14" fill="url(#smokeGradient)"/>
  <ellipse cx="708" cy="90" rx="10" ry="16" fill="url(#smokeGradient)"/>
  
  <!-- Windows -->
  <rect x="70" y="220" width="8" height="12" fill="#fbbf24"/>
  <rect x="85" y="220" width="8" height="12" fill="#fbbf24"/>
  <rect x="100" y="220" width="8" height="12" fill="#fbbf24"/>
  <rect x="115" y="220" width="8" height="12" fill="#fbbf24"/>
  <rect x="130" y="220" width="8" height="12" fill="#fbbf24"/>
  <rect x="145" y="220" width="8" height="12" fill="#fbbf24"/>
  
  <rect x="70" y="250" width="8" height="12" fill="#fbbf24"/>
  <rect x="85" y="250" width="8" height="12" fill="#fbbf24"/>
  <rect x="100" y="250" width="8" height="12" fill="#fbbf24"/>
  <rect x="115" y="250" width="8" height="12" fill="#fbbf24"/>
  <rect x="130" y="250" width="8" height="12" fill="#fbbf24"/>
  <rect x="145" y="250" width="8" height="12" fill="#fbbf24"/>
  
  <!-- More windows for other buildings -->
  <rect x="220" y="200" width="6" height="10" fill="#fbbf24"/>
  <rect x="235" y="200" width="6" height="10" fill="#fbbf24"/>
  <rect x="250" y="200" width="6" height="10" fill="#fbbf24"/>
  <rect x="265" y="200" width="6" height="10" fill="#fbbf24"/>
  <rect x="280" y="200" width="6" height="10" fill="#fbbf24"/>
  
  <rect x="350" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="365" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="380" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="395" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="410" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="425" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="440" y="180" width="8" height="12" fill="#fbbf24"/>
  <rect x="455" y="180" width="8" height="12" fill="#fbbf24"/>
  
  <!-- Conveyor belts and industrial elements -->
  <rect x="0" y="380" width="800" height="20" fill="#6b7280"/>
  <rect x="100" y="370" width="200" height="10" fill="#9ca3af"/>
  <rect x="400" y="375" width="300" height="8" fill="#9ca3af"/>
  
  <!-- Industrial cranes -->
  <line x1="180" y1="160" x2="180" y2="50" stroke="#374151" stroke-width="4"/>
  <line x1="180" y1="50" x2="250" y2="50" stroke="#374151" stroke-width="3"/>
  <line x1="250" y1="50" x2="250" y2="80" stroke="#374151" stroke-width="2"/>
  <!-- Crane hook -->
  <rect x="248" y="78" width="4" height="8" fill="#6b7280"/>
  <circle cx="250" cy="90" r="3" fill="#374151"/>

  <line x1="480" y1="170" x2="480" y2="40" stroke="#374151" stroke-width="4"/>
  <line x1="480" y1="40" x2="580" y2="40" stroke="#374151" stroke-width="3"/>
  <line x1="580" y1="40" x2="580" y2="70" stroke="#374151" stroke-width="2"/>
  <!-- Crane hook -->
  <rect x="578" y="68" width="4" height="8" fill="#6b7280"/>
  <circle cx="580" cy="80" r="3" fill="#374151"/>

  <!-- Robotic arms and automation -->
  <g transform="translate(320, 300)">
    <!-- Robotic arm base -->
    <circle cx="0" cy="0" r="15" fill="#374151"/>
    <!-- Arm segments -->
    <rect x="-2" y="-40" width="4" height="40" fill="#4b5563" rx="2"/>
    <rect x="-25" y="-45" width="50" height="4" fill="#4b5563" rx="2"/>
    <!-- End effector -->
    <rect x="-30" y="-50" width="8" height="12" fill="#6b7280" rx="1"/>
    <rect x="22" y="-50" width="8" height="12" fill="#6b7280" rx="1"/>
    <!-- Joints -->
    <circle cx="0" cy="-40" r="4" fill="#374151"/>
    <circle cx="-25" cy="-43" r="3" fill="#374151"/>
    <circle cx="25" cy="-43" r="3" fill="#374151"/>
    <!-- Status LED -->
    <circle cx="0" cy="-10" r="2" fill="#10b981">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Smart sensors and IoT devices -->
  <g transform="translate(150, 250)">
    <rect x="0" y="0" width="20" height="15" fill="#1e293b" rx="2"/>
    <circle cx="10" cy="7" r="3" fill="#3b82f6"/>
    <circle cx="5" cy="3" r="1" fill="#10b981">
      <animate attributeName="opacity" values="1;0.5;1" dur="0.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="3" r="1" fill="#f59e0b">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    <!-- Wireless signals -->
    <circle cx="10" cy="7" r="8" fill="none" stroke="#3b82f6" stroke-width="1" opacity="0.5">
      <animate attributeName="r" values="8;15;8" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0;0.5" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Additional IoT sensors -->
  <g transform="translate(450, 280)">
    <rect x="0" y="0" width="15" height="12" fill="#1e293b" rx="2"/>
    <circle cx="7" cy="6" r="2" fill="#3b82f6"/>
    <circle cx="3" cy="3" r="0.8" fill="#10b981">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="11" cy="3" r="0.8" fill="#ef4444">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Digital displays and control panels -->
  <rect x="600" y="250" width="80" height="50" fill="#1e293b" rx="3" stroke="#374151" stroke-width="1"/>
  <rect x="610" y="260" width="60" height="30" fill="#0f172a" rx="2"/>
  <text x="640" y="275" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="8">Production</text>
  <text x="640" y="285" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="6">Status: Online</text>
  <!-- Status bars -->
  <rect x="615" y="290" width="20" height="3" fill="#10b981"/>
  <rect x="640" y="290" width="15" height="3" fill="#f59e0b"/>
  <rect x="660" y="290" width="10" height="3" fill="#ef4444"/>

  <!-- Automated guided vehicles (AGV) -->
  <g transform="translate(280, 370)">
    <rect x="0" y="0" width="40" height="20" fill="#374151" rx="3"/>
    <circle cx="8" cy="22" r="4" fill="#1f2937"/>
    <circle cx="32" cy="22" r="4" fill="#1f2937"/>
    <!-- AGV sensors -->
    <circle cx="20" cy="5" r="2" fill="#3b82f6"/>
    <rect x="15" y="8" width="10" height="4" fill="#1e293b"/>
    <!-- Navigation LED -->
    <circle cx="20" cy="10" r="1" fill="#10b981">
      <animate attributeName="opacity" values="1;0.2;1" dur="0.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Quality control stations -->
  <g transform="translate(520, 320)">
    <rect x="0" y="0" width="30" height="25" fill="#374151" rx="2"/>
    <rect x="5" y="5" width="20" height="15" fill="#1e293b" rx="1"/>
    <!-- Scanner beam -->
    <line x1="15" y1="25" x2="15" y2="35" stroke="#ef4444" stroke-width="2" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="1s" repeatCount="indefinite"/>
    </line>
    <!-- Status indicators -->
    <circle cx="8" cy="8" r="1" fill="#10b981"/>
    <circle cx="12" cy="8" r="1" fill="#f59e0b"/>
    <circle cx="16" cy="8" r="1" fill="#3b82f6"/>
    <circle cx="20" cy="8" r="1" fill="#ef4444"/>
  </g>

  <!-- Environmental monitoring -->
  <g transform="translate(80, 180)">
    <rect x="0" y="0" width="25" height="20" fill="#1e293b" rx="2"/>
    <circle cx="12" cy="10" r="6" fill="#374151"/>
    <text x="12" y="13" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="6">ENV</text>
    <!-- Data transmission -->
    <circle cx="12" cy="10" r="10" fill="none" stroke="#3b82f6" stroke-width="0.5" opacity="0.6">
      <animate attributeName="r" values="10;18;10" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Network infrastructure -->
  <g opacity="0.7">
    <!-- Data cables -->
    <path d="M 150 265 Q 200 270 320 315" stroke="#3b82f6" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 450 295 Q 500 300 520 335" stroke="#10b981" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 600 275 Q 550 280 320 315" stroke="#f59e0b" stroke-width="2" fill="none" opacity="0.6"/>

    <!-- Data flow indicators -->
    <circle cx="200" cy="270" r="2" fill="#3b82f6">
      <animate attributeName="cx" values="150;320" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="300" r="2" fill="#10b981">
      <animate attributeName="cx" values="450;520" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
