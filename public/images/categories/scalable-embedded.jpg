<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bg1)"/>
  <g transform="translate(200,150)">
    <!-- Server/Computer Icon -->
    <rect x="-60" y="-40" width="120" height="80" rx="8" fill="white" opacity="0.9"/>
    <rect x="-50" y="-30" width="100" height="8" rx="2" fill="#3B82F6"/>
    <rect x="-50" y="-15" width="100" height="8" rx="2" fill="#60A5FA"/>
    <rect x="-50" y="0" width="100" height="8" rx="2" fill="#93C5FD"/>
    <rect x="-50" y="15" width="100" height="8" rx="2" fill="#DBEAFE"/>
    <!-- Connecting lines -->
    <line x1="-80" y1="0" x2="-60" y2="0" stroke="white" stroke-width="3" opacity="0.8"/>
    <line x1="60" y1="0" x2="80" y2="0" stroke="white" stroke-width="3" opacity="0.8"/>
    <!-- Side nodes -->
    <circle cx="-90" cy="0" r="8" fill="white" opacity="0.9"/>
    <circle cx="90" cy="0" r="8" fill="white" opacity="0.9"/>
  </g>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Scalable Embedded Series</text>
</svg>