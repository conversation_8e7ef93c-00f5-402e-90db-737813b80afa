<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bg3)"/>
  <g transform="translate(200,150)">
    <!-- Circuit Board -->
    <rect x="-70" y="-45" width="140" height="90" rx="4" fill="#065F46" opacity="0.9"/>
    <!-- CPU -->
    <rect x="-25" y="-20" width="50" height="40" rx="2" fill="white" opacity="0.9"/>
    <rect x="-20" y="-15" width="40" height="30" rx="1" fill="#F59E0B"/>
    <!-- Memory slots -->
    <rect x="-60" y="-35" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <rect x="-60" y="-20" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <!-- Connectors -->
    <rect x="35" y="-35" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <rect x="35" y="-20" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <rect x="35" y="-5" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <rect x="35" y="10" width="25" height="8" rx="1" fill="white" opacity="0.8"/>
    <!-- Circuit traces -->
    <line x1="-45" y1="-30" x2="30" y2="-30" stroke="#FCD34D" stroke-width="2" opacity="0.7"/>
    <line x1="-45" y1="-15" x2="30" y2="-15" stroke="#FCD34D" stroke-width="2" opacity="0.7"/>
    <line x1="-45" y1="0" x2="30" y2="0" stroke="#FCD34D" stroke-width="2" opacity="0.7"/>
    <!-- Components -->
    <circle cx="-50" cy="25" r="4" fill="white" opacity="0.8"/>
    <circle cx="-35" cy="25" r="4" fill="white" opacity="0.8"/>
    <circle cx="-20" cy="25" r="4" fill="white" opacity="0.8"/>
  </g>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Single Board Computer</text>
</svg>