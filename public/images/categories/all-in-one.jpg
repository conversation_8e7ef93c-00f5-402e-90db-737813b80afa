<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bg5)"/>
  <g transform="translate(200,150)">
    <!-- Monitor/Display -->
    <rect x="-90" y="-60" width="180" height="120" rx="8" fill="white" opacity="0.9"/>
    <!-- Screen -->
    <rect x="-80" y="-50" width="160" height="90" rx="4" fill="#1F2937"/>
    <!-- Screen content -->
    <rect x="-70" y="-40" width="140" height="8" rx="2" fill="#EF4444"/>
    <rect x="-70" y="-25" width="140" height="6" rx="1" fill="#F87171"/>
    <rect x="-70" y="-15" width="140" height="6" rx="1" fill="#FCA5A5"/>
    <rect x="-70" y="-5" width="140" height="6" rx="1" fill="#FECACA"/>
    <!-- Chart/Graph visualization -->
    <rect x="-60" y="5" width="20" height="25" fill="#EF4444" opacity="0.8"/>
    <rect x="-35" y="15" width="20" height="15" fill="#F87171" opacity="0.8"/>
    <rect x="-10" y="10" width="20" height="20" fill="#FCA5A5" opacity="0.8"/>
    <rect x="15" y="5" width="20" height="25" fill="#FECACA" opacity="0.8"/>
    <rect x="40" y="20" width="20" height="10" fill="#FEE2E2" opacity="0.8"/>
    <!-- Stand/Base -->
    <rect x="-30" y="60" width="60" height="15" rx="4" fill="white" opacity="0.8"/>
    <rect x="-10" y="45" width="20" height="20" rx="2" fill="white" opacity="0.8"/>
    <!-- Touch interface indicators -->
    <circle cx="75" cy="-45" r="3" fill="white" opacity="0.9"/>
    <circle cx="75" cy="-30" r="3" fill="white" opacity="0.9"/>
    <circle cx="75" cy="-15" r="3" fill="white" opacity="0.9"/>
    <circle cx="75" cy="0" r="3" fill="white" opacity="0.9"/>
  </g>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">All In One IPC</text>
</svg>