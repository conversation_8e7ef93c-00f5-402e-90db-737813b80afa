<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgDefault" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6B7280;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4B5563;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bgDefault)"/>
  <g transform="translate(200,150)">
    <!-- Generic Tech Icon -->
    <rect x="-60" y="-40" width="120" height="80" rx="8" fill="white" opacity="0.9"/>
    <!-- Circuit pattern -->
    <line x1="-40" y1="-20" x2="40" y2="-20" stroke="#6B7280" stroke-width="2"/>
    <line x1="-40" y1="0" x2="40" y2="0" stroke="#6B7280" stroke-width="2"/>
    <line x1="-40" y1="20" x2="40" y2="20" stroke="#6B7280" stroke-width="2"/>
    <line x1="-20" y1="-30" x2="-20" y2="30" stroke="#6B7280" stroke-width="2"/>
    <line x1="0" y1="-30" x2="0" y2="30" stroke="#6B7280" stroke-width="2"/>
    <line x1="20" y1="-30" x2="20" y2="30" stroke="#6B7280" stroke-width="2"/>
    <!-- Connection points -->
    <circle cx="-20" cy="-20" r="3" fill="#6B7280"/>
    <circle cx="0" cy="-20" r="3" fill="#6B7280"/>
    <circle cx="20" cy="-20" r="3" fill="#6B7280"/>
    <circle cx="-20" cy="0" r="3" fill="#6B7280"/>
    <circle cx="0" cy="0" r="3" fill="#6B7280"/>
    <circle cx="20" cy="0" r="3" fill="#6B7280"/>
    <circle cx="-20" cy="20" r="3" fill="#6B7280"/>
    <circle cx="0" cy="20" r="3" fill="#6B7280"/>
    <circle cx="20" cy="20" r="3" fill="#6B7280"/>
  </g>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Product Category</text>
</svg>