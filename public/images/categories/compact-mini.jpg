<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bg2)"/>
  <g transform="translate(200,150)">
    <!-- Mini Computer Icon -->
    <rect x="-40" y="-30" width="80" height="60" rx="6" fill="white" opacity="0.9"/>
    <rect x="-35" y="-25" width="70" height="6" rx="1" fill="#10B981"/>
    <rect x="-35" y="-15" width="70" height="6" rx="1" fill="#34D399"/>
    <rect x="-35" y="-5" width="70" height="6" rx="1" fill="#6EE7B7"/>
    <rect x="-35" y="5" width="70" height="6" rx="1" fill="#A7F3D0"/>
    <!-- Ports -->
    <rect x="-45" y="-10" width="5" height="8" fill="white" opacity="0.8"/>
    <rect x="-45" y="2" width="5" height="8" fill="white" opacity="0.8"/>
    <rect x="40" y="-10" width="5" height="8" fill="white" opacity="0.8"/>
    <rect x="40" y="2" width="5" height="8" fill="white" opacity="0.8"/>
    <!-- Size indicator -->
    <circle cx="0" cy="0" r="50" fill="none" stroke="white" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"/>
  </g>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Compact Mini Size Series</text>
</svg>