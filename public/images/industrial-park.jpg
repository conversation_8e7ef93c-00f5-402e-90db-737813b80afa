<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="skyGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGrad1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5A67D8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4C51BF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="smokeGrad" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:#E2E8F0;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#F7FAFC;stop-opacity:0.2" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1200" height="800" fill="url(#skyGrad)"/>
  
  <!-- Ground -->
  <rect x="0" y="600" width="1200" height="200" fill="#68D391" opacity="0.3"/>
  
  <!-- Factory Building 1 -->
  <rect x="100" y="300" width="200" height="300" fill="url(#buildingGrad1)"/>
  <rect x="120" y="320" width="30" height="40" fill="#FFF" opacity="0.8"/>
  <rect x="170" y="320" width="30" height="40" fill="#FFF" opacity="0.8"/>
  <rect x="220" y="320" width="30" height="40" fill="#FFF" opacity="0.8"/>
  <rect x="120" y="380" width="30" height="40" fill="#FFF" opacity="0.8"/>
  <rect x="170" y="380" width="30" height="40" fill="#FFF" opacity="0.8"/>
  <rect x="220" y="380" width="30" height="40" fill="#FFF" opacity="0.8"/>
  
  <!-- Chimney 1 -->
  <rect x="280" y="150" width="25" height="150" fill="#4A5568"/>
  <ellipse cx="292.5" cy="150" rx="15" ry="8" fill="#4A5568"/>
  
  <!-- Smoke from chimney 1 -->
  <ellipse cx="292" cy="140" rx="8" ry="15" fill="url(#smokeGrad)">
    <animateTransform attributeName="transform" type="translate" values="0,0; -5,-20; 5,-40; -3,-60" dur="4s" repeatCount="indefinite"/>
  </ellipse>
  <ellipse cx="295" cy="120" rx="12" ry="20" fill="url(#smokeGrad)" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" values="0,0; 3,-25; -7,-50; 4,-75" dur="5s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- Factory Building 2 -->
  <rect x="400" y="250" width="250" height="350" fill="url(#buildingGrad2)"/>
  <rect x="420" y="270" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="480" y="270" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="540" y="270" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="600" y="270" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="420" y="340" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="480" y="340" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="540" y="340" width="35" height="45" fill="#FFF" opacity="0.8"/>
  <rect x="600" y="340" width="35" height="45" fill="#FFF" opacity="0.8"/>
  
  <!-- Large Industrial Door -->
  <rect x="450" y="500" width="80" height="100" fill="#2D3748" stroke="#4A5568" stroke-width="2"/>
  <rect x="460" y="510" width="60" height="80" fill="#4A5568"/>
  
  <!-- Chimney 2 -->
  <rect x="620" y="180" width="30" height="70" fill="#4A5568"/>
  <ellipse cx="635" cy="180" rx="18" ry="10" fill="#4A5568"/>
  
  <!-- Warehouse Building -->
  <rect x="750" y="350" width="300" height="250" fill="#E2E8F0"/>
  <polygon points="750,350 900,280 1050,350" fill="#CBD5E0"/>
  <rect x="800" y="450" width="60" height="80" fill="#4A5568"/>
  <rect x="900" y="450" width="60" height="80" fill="#4A5568"/>
  
  <!-- Solar Panels on Warehouse Roof -->
  <rect x="770" y="290" width="40" height="25" fill="#1A365D" transform="rotate(-15 790 302)"/>
  <rect x="820" y="285" width="40" height="25" fill="#1A365D" transform="rotate(-15 840 297)"/>
  <rect x="870" y="280" width="40" height="25" fill="#1A365D" transform="rotate(-15 890 292)"/>
  <rect x="920" y="275" width="40" height="25" fill="#1A365D" transform="rotate(-15 940 287)"/>
  
  <!-- Grid lines on solar panels -->
  <g stroke="#2D5A87" stroke-width="1" opacity="0.7">
    <line x1="775" y1="295" x2="805" y2="305" transform="rotate(-15 790 302)"/>
    <line x1="785" y1="290" x2="815" y2="300" transform="rotate(-15 790 302)"/>
    <line x1="795" y1="285" x2="825" y2="295" transform="rotate(-15 790 302)"/>
  </g>
  
  <!-- Power Lines -->
  <g stroke="#333" stroke-width="2" fill="none">
    <line x1="50" y1="200" x2="1150" y2="200"/>
    <line x1="150" y1="200" x2="150" y2="180"/>
    <line x1="350" y1="200" x2="350" y2="180"/>
    <line x1="550" y1="200" x2="550" y2="180"/>
    <line x1="750" y1="200" x2="750" y2="180"/>
    <line x1="950" y1="200" x2="950" y2="180"/>
  </g>
  
  <!-- Power Transmission Tower -->
  <g transform="translate(1100,150)">
    <g stroke="#333" stroke-width="3" fill="none">
      <line x1="0" y1="0" x2="0" y2="200"/>
      <line x1="-20" y1="30" x2="20" y2="30"/>
      <line x1="-15" y1="60" x2="15" y2="60"/>
      <line x1="-10" y1="90" x2="10" y2="90"/>
      <line x1="-20" y1="30" x2="0" y2="0"/>
      <line x1="20" y1="30" x2="0" y2="0"/>
    </g>
  </g>
  
  <!-- Trucks and Vehicles -->
  <g transform="translate(200,550)">
    <!-- Truck 1 -->
    <rect x="0" y="0" width="60" height="25" fill="#E53E3E" rx="3"/>
    <rect x="-15" y="5" width="20" height="15" fill="#E53E3E" rx="2"/>
    <circle cx="15" cy="30" r="8" fill="#2D3748"/>
    <circle cx="45" cy="30" r="8" fill="#2D3748"/>
    <circle cx="15" cy="30" r="5" fill="#4A5568"/>
    <circle cx="45" cy="30" r="5" fill="#4A5568"/>
  </g>
  
  <g transform="translate(500,560)">
    <!-- Truck 2 -->
    <rect x="0" y="0" width="50" height="20" fill="#3182CE" rx="2"/>
    <rect x="-12" y="3" width="15" height="14" fill="#3182CE" rx="2"/>
    <circle cx="12" cy="25" r="6" fill="#2D3748"/>
    <circle cx="38" cy="25" r="6" fill="#2D3748"/>
    <circle cx="12" cy="25" r="4" fill="#4A5568"/>
    <circle cx="38" cy="25" r="4" fill="#4A5568"/>
  </g>
  
  <!-- Energy Flow Indicators -->
  <g opacity="0.6">
    <circle cx="200" cy="400" r="4" fill="#48BB78">
      <animate attributeName="r" values="4;10;4" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="525" cy="350" r="4" fill="#4299E1">
      <animate attributeName="r" values="4;10;4" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="900" cy="400" r="4" fill="#ED8936">
      <animate attributeName="r" values="4;10;4" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Trees for environmental touch -->
  <g transform="translate(50,500)">
    <rect x="0" y="40" width="8" height="60" fill="#8B4513"/>
    <circle cx="4" cy="40" r="25" fill="#228B22"/>
  </g>
  
  <g transform="translate(1050,480)">
    <rect x="0" y="40" width="8" height="80" fill="#8B4513"/>
    <circle cx="4" cy="40" r="30" fill="#228B22"/>
  </g>
  
  <!-- Road -->
  <rect x="0" y="580" width="1200" height="20" fill="#4A5568" opacity="0.8"/>
  <rect x="0" y="585" width="1200" height="2" fill="#FFF" opacity="0.6"/>
  <rect x="0" y="592" width="1200" height="2" fill="#FFF" opacity="0.6"/>
</svg>