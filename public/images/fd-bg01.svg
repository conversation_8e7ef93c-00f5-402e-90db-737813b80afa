<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cameraGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="lensGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="scanBeam" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>
  
  <!-- Production line conveyor -->
  <rect x="0" y="300" width="800" height="40" fill="#374151"/>
  <rect x="0" y="310" width="800" height="20" fill="#4b5563"/>
  
  <!-- Conveyor belt pattern -->
  <rect x="0" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="40" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="80" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="120" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="160" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="200" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="240" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="280" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="320" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="360" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="400" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="440" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="480" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="520" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="560" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="600" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="640" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="680" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="720" y="315" width="20" height="10" fill="#6b7280"/>
  <rect x="760" y="315" width="20" height="10" fill="#6b7280"/>
  
  <!-- Machine vision camera system -->
  <g transform="translate(400, 150)">
    <!-- Camera mount -->
    <rect x="-15" y="-80" width="30" height="60" fill="url(#cameraGradient)" rx="5"/>
    <!-- Camera body -->
    <rect x="-25" y="-40" width="50" height="30" fill="url(#cameraGradient)" rx="3"/>
    <!-- Lens -->
    <circle cx="0" cy="-25" r="15" fill="url(#lensGradient)"/>
    <circle cx="0" cy="-25" r="10" fill="#1e40af"/>
    <circle cx="0" cy="-25" r="5" fill="#3b82f6"/>
    <!-- LED indicators -->
    <circle cx="-15" cy="-35" r="2" fill="#10b981"/>
    <circle cx="-8" cy="-35" r="2" fill="#f59e0b"/>
    <circle cx="-1" cy="-35" r="2" fill="#ef4444"/>
    <!-- Cables -->
    <rect x="20" y="-30" width="40" height="3" fill="#6b7280"/>
    <rect x="20" y="-25" width="40" height="3" fill="#6b7280"/>
  </g>
  
  <!-- Scanning beam -->
  <polygon points="385,110 415,110 450,300 350,300" fill="url(#scanBeam)" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Products on conveyor belt -->
  <!-- Product 1 - Good -->
  <rect x="150" y="280" width="40" height="20" fill="#10b981" rx="2"/>
  <text x="170" y="295" text-anchor="middle" fill="white" font-family="Arial" font-size="8">OK</text>
  
  <!-- Product 2 - Being scanned -->
  <rect x="380" y="280" width="40" height="20" fill="#3b82f6" rx="2"/>
  <text x="400" y="295" text-anchor="middle" fill="white" font-family="Arial" font-size="8">SCAN</text>
  
  <!-- Product 3 - Defective -->
  <rect x="600" y="280" width="40" height="20" fill="#ef4444" rx="2"/>
  <text x="620" y="295" text-anchor="middle" fill="white" font-family="Arial" font-size="8">NG</text>
  
  <!-- Detection grid overlay on scanned product -->
  <g opacity="0.8">
    <line x1="380" y1="280" x2="420" y2="280" stroke="#3b82f6" stroke-width="1"/>
    <line x1="380" y1="290" x2="420" y2="290" stroke="#3b82f6" stroke-width="1"/>
    <line x1="380" y1="300" x2="420" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="380" y1="280" x2="380" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="390" y1="280" x2="390" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="400" y1="280" x2="400" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="410" y1="280" x2="410" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="420" y1="280" x2="420" y2="300" stroke="#3b82f6" stroke-width="1"/>
  </g>
  
  <!-- Control panel -->
  <rect x="50" y="50" width="120" height="180" fill="url(#cameraGradient)" rx="5"/>
  <rect x="60" y="60" width="100" height="60" fill="#1e293b" rx="3"/>
  
  <!-- Screen content -->
  <text x="110" y="80" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Vision System</text>
  <text x="110" y="95" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8">Status: Active</text>
  <text x="110" y="110" text-anchor="middle" fill="#fbbf24" font-family="Arial" font-size="8">Products: 1247</text>
  
  <!-- Status indicators -->
  <circle cx="70" cy="140" r="5" fill="#10b981"/>
  <text x="85" y="145" fill="#e5e7eb" font-family="Arial" font-size="8">System OK</text>
  
  <circle cx="70" cy="160" r="5" fill="#3b82f6"/>
  <text x="85" y="165" fill="#e5e7eb" font-family="Arial" font-size="8">Scanning</text>
  
  <circle cx="70" cy="180" r="5" fill="#f59e0b"/>
  <text x="85" y="185" fill="#e5e7eb" font-family="Arial" font-size="8">Processing</text>
  
  <circle cx="70" cy="200" r="5" fill="#ef4444"/>
  <text x="85" y="205" fill="#e5e7eb" font-family="Arial" font-size="8">Defect Alert</text>
  
  <!-- Quality statistics -->
  <rect x="650" y="50" width="130" height="120" fill="url(#cameraGradient)" rx="5"/>
  <text x="715" y="75" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Quality Stats</text>
  
  <text x="665" y="95" fill="#10b981" font-family="Arial" font-size="8">Pass Rate: 98.5%</text>
  <text x="665" y="110" fill="#ef4444" font-family="Arial" font-size="8">Defects: 1.5%</text>
  <text x="665" y="125" fill="#fbbf24" font-family="Arial" font-size="8">Speed: 120/min</text>
  <text x="665" y="140" fill="#3b82f6" font-family="Arial" font-size="8">Accuracy: 99.8%</text>
  <text x="665" y="155" fill="#9ca3af" font-family="Arial" font-size="8">Uptime: 99.2%</text>
  
  <!-- Robotic arm for defect removal -->
  <g transform="translate(650, 200)">
    <!-- Base -->
    <rect x="-10" y="80" width="20" height="20" fill="url(#cameraGradient)" rx="2"/>
    <!-- Arm segments -->
    <rect x="-3" y="40" width="6" height="40" fill="url(#cameraGradient)" rx="1"/>
    <rect x="-15" y="35" width="30" height="6" fill="url(#cameraGradient)" rx="1"/>
    <!-- Gripper -->
    <rect x="-18" y="32" width="6" height="8" fill="#6b7280"/>
    <rect x="12" y="32" width="6" height="8" fill="#6b7280"/>
    <!-- Joint -->
    <circle cx="0" cy="40" r="3" fill="#4b5563"/>
    <circle cx="0" cy="80" r="3" fill="#4b5563"/>
  </g>
  
  <!-- Data flow visualization -->
  <g opacity="0.7">
    <!-- Data packets -->
    <rect x="200" y="200" width="8" height="4" fill="#3b82f6">
      <animate attributeName="x" values="200;580" dur="3s" repeatCount="indefinite"/>
    </rect>
    <rect x="220" y="210" width="8" height="4" fill="#10b981">
      <animate attributeName="x" values="220;600" dur="3.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="240" y="220" width="8" height="4" fill="#f59e0b">
      <animate attributeName="x" values="240;620" dur="4s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Network connections -->
  <line x1="170" y1="140" x2="350" y2="140" stroke="#3b82f6" stroke-width="2" opacity="0.6"/>
  <line x1="450" y1="140" x2="650" y2="140" stroke="#3b82f6" stroke-width="2" opacity="0.6"/>
  
  <!-- AI processing indicator -->
  <rect x="300" y="50" width="200" height="60" fill="url(#cameraGradient)" rx="5" opacity="0.9"/>
  <text x="400" y="75" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="12">AI Processing</text>
  <text x="400" y="90" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="8">Deep Learning Model</text>
  <text x="400" y="105" text-anchor="middle" fill="#fbbf24" font-family="Arial" font-size="8">Confidence: 99.7%</text>

  <!-- Multi-camera system -->
  <g transform="translate(200, 120)">
    <!-- Camera 2 -->
    <rect x="-20" y="-30" width="40" height="25" fill="url(#cameraGradient)" rx="3"/>
    <circle cx="0" cy="-17" r="12" fill="url(#lensGradient)"/>
    <circle cx="0" cy="-17" r="8" fill="#1e40af"/>
    <circle cx="0" cy="-17" r="4" fill="#3b82f6"/>
    <circle cx="-12" cy="-25" r="1.5" fill="#10b981"/>
    <circle cx="-6" cy="-25" r="1.5" fill="#f59e0b"/>
    <!-- Laser line projector -->
    <rect x="-25" y="-20" width="8" height="4" fill="#ef4444"/>
    <line x1="-25" y1="-18" x2="-50" y2="50" stroke="#ef4444" stroke-width="1" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1s" repeatCount="indefinite"/>
    </line>
  </g>

  <g transform="translate(600, 140)">
    <!-- Camera 3 -->
    <rect x="-20" y="-30" width="40" height="25" fill="url(#cameraGradient)" rx="3"/>
    <circle cx="0" cy="-17" r="12" fill="url(#lensGradient)"/>
    <circle cx="0" cy="-17" r="8" fill="#1e40af"/>
    <circle cx="0" cy="-17" r="4" fill="#3b82f6"/>
    <circle cx="-12" cy="-25" r="1.5" fill="#10b981"/>
    <circle cx="-6" cy="-25" r="1.5" fill="#f59e0b"/>
    <!-- Structured light projector -->
    <rect x="20" y="-20" width="10" height="6" fill="#3b82f6"/>
    <g opacity="0.5">
      <line x1="30" y1="-17" x2="60" y2="20" stroke="#3b82f6" stroke-width="0.5"/>
      <line x1="30" y1="-17" x2="65" y2="25" stroke="#3b82f6" stroke-width="0.5"/>
      <line x1="30" y1="-17" x2="70" y2="30" stroke="#3b82f6" stroke-width="0.5"/>
      <line x1="30" y1="-17" x2="75" y2="35" stroke="#3b82f6" stroke-width="0.5"/>
    </g>
  </g>

  <!-- Advanced lighting system -->
  <g transform="translate(400, 200)">
    <!-- Ring light -->
    <circle cx="0" cy="0" r="25" fill="none" stroke="#fbbf24" stroke-width="3" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="20" fill="none" stroke="#fbbf24" stroke-width="2" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0.3;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    <!-- LED segments -->
    <circle cx="0" cy="-25" r="2" fill="#fbbf24"/>
    <circle cx="18" cy="-18" r="2" fill="#fbbf24"/>
    <circle cx="25" cy="0" r="2" fill="#fbbf24"/>
    <circle cx="18" cy="18" r="2" fill="#fbbf24"/>
    <circle cx="0" cy="25" r="2" fill="#fbbf24"/>
    <circle cx="-18" cy="18" r="2" fill="#fbbf24"/>
    <circle cx="-25" cy="0" r="2" fill="#fbbf24"/>
    <circle cx="-18" cy="-18" r="2" fill="#fbbf24"/>
  </g>

  <!-- 3D depth sensing -->
  <g transform="translate(300, 250)" opacity="0.7">
    <rect x="0" y="0" width="200" height="50" fill="none" stroke="#3b82f6" stroke-width="1" stroke-dasharray="5,5"/>
    <text x="100" y="25" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="8">3D Depth Analysis</text>
    <text x="100" y="40" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="6">Point Cloud: 2.3M points</text>
    <!-- Depth visualization -->
    <g opacity="0.5">
      <rect x="20" y="10" width="4" height="8" fill="#3b82f6"/>
      <rect x="30" y="12" width="4" height="6" fill="#10b981"/>
      <rect x="40" y="8" width="4" height="12" fill="#f59e0b"/>
      <rect x="50" y="14" width="4" height="4" fill="#ef4444"/>
      <rect x="60" y="10" width="4" height="8" fill="#3b82f6"/>
      <rect x="70" y="11" width="4" height="7" fill="#10b981"/>
      <rect x="80" y="9" width="4" height="10" fill="#f59e0b"/>
      <rect x="90" y="13" width="4" height="5" fill="#ef4444"/>
      <rect x="100" y="10" width="4" height="8" fill="#3b82f6"/>
      <rect x="110" y="12" width="4" height="6" fill="#10b981"/>
      <rect x="120" y="8" width="4" height="12" fill="#f59e0b"/>
      <rect x="130" y="14" width="4" height="4" fill="#ef4444"/>
      <rect x="140" y="10" width="4" height="8" fill="#3b82f6"/>
      <rect x="150" y="11" width="4" height="7" fill="#10b981"/>
      <rect x="160" y="9" width="4" height="10" fill="#f59e0b"/>
      <rect x="170" y="13" width="4" height="5" fill="#ef4444"/>
    </g>
  </g>

  <!-- Neural network visualization -->
  <g transform="translate(100, 120)" opacity="0.8">
    <rect x="0" y="0" width="80" height="100" fill="url(#cameraGradient)" rx="5"/>
    <text x="40" y="20" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="8">Neural Net</text>

    <!-- Network layers -->
    <g fill="#3b82f6" opacity="0.7">
      <!-- Input layer -->
      <circle cx="15" cy="35" r="3"/>
      <circle cx="15" cy="45" r="3"/>
      <circle cx="15" cy="55" r="3"/>
      <circle cx="15" cy="65" r="3"/>

      <!-- Hidden layer 1 -->
      <circle cx="35" cy="30" r="2"/>
      <circle cx="35" cy="40" r="2"/>
      <circle cx="35" cy="50" r="2"/>
      <circle cx="35" cy="60" r="2"/>
      <circle cx="35" cy="70" r="2"/>

      <!-- Hidden layer 2 -->
      <circle cx="55" cy="35" r="2"/>
      <circle cx="55" cy="45" r="2"/>
      <circle cx="55" cy="55" r="2"/>
      <circle cx="55" cy="65" r="2"/>

      <!-- Output layer -->
      <circle cx="70" cy="45" r="3" fill="#10b981"/>
      <circle cx="70" cy="55" r="3" fill="#ef4444"/>
    </g>

    <!-- Connections -->
    <g stroke="#3b82f6" stroke-width="0.5" opacity="0.4">
      <line x1="18" y1="35" x2="32" y2="30"/>
      <line x1="18" y1="45" x2="32" y2="40"/>
      <line x1="18" y1="55" x2="32" y2="50"/>
      <line x1="18" y1="65" x2="32" y2="60"/>
      <line x1="38" y1="30" x2="52" y2="35"/>
      <line x1="38" y1="40" x2="52" y2="45"/>
      <line x1="38" y1="50" x2="52" y2="55"/>
      <line x1="38" y1="60" x2="52" y2="65"/>
      <line x1="58" y1="35" x2="67" y2="45"/>
      <line x1="58" y1="45" x2="67" y2="55"/>
    </g>

    <text x="40" y="85" text-anchor="middle" fill="#10b981" font-family="Arial" font-size="6">Accuracy: 99.8%</text>
    <text x="40" y="95" text-anchor="middle" fill="#f59e0b" font-family="Arial" font-size="6">Speed: 120ms</text>
  </g>

  <!-- Defect classification -->
  <g transform="translate(680, 200)">
    <rect x="0" y="0" width="100" height="120" fill="url(#cameraGradient)" rx="5"/>
    <text x="50" y="20" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Defect Types</text>

    <!-- Defect examples -->
    <rect x="10" y="30" width="15" height="15" fill="#ef4444" rx="2"/>
    <text x="30" y="42" fill="#e5e7eb" font-family="Arial" font-size="6">Scratch</text>

    <rect x="10" y="50" width="15" height="15" fill="#f59e0b" rx="2"/>
    <text x="30" y="62" fill="#e5e7eb" font-family="Arial" font-size="6">Dent</text>

    <rect x="10" y="70" width="15" height="15" fill="#3b82f6" rx="2"/>
    <text x="30" y="82" fill="#e5e7eb" font-family="Arial" font-size="6">Discolor</text>

    <rect x="10" y="90" width="15" height="15" fill="#10b981" rx="2"/>
    <text x="30" y="102" fill="#e5e7eb" font-family="Arial" font-size="6">Good</text>

    <!-- Detection confidence bars -->
    <rect x="60" y="35" width="30" height="5" fill="#1f2937"/>
    <rect x="60" y="35" width="25" height="5" fill="#ef4444"/>
    <text x="95" y="42" fill="#e5e7eb" font-family="Arial" font-size="5">83%</text>

    <rect x="60" y="55" width="30" height="5" fill="#1f2937"/>
    <rect x="60" y="55" width="18" height="5" fill="#f59e0b"/>
    <text x="95" y="62" fill="#e5e7eb" font-family="Arial" font-size="5">60%</text>

    <rect x="60" y="75" width="30" height="5" fill="#1f2937"/>
    <rect x="60" y="75" width="22" height="5" fill="#3b82f6"/>
    <text x="95" y="82" fill="#e5e7eb" font-family="Arial" font-size="5">73%</text>

    <rect x="60" y="95" width="30" height="5" fill="#1f2937"/>
    <rect x="60" y="95" width="29" height="5" fill="#10b981"/>
    <text x="95" y="102" fill="#e5e7eb" font-family="Arial" font-size="5">97%</text>
  </g>

  <!-- Real-time analytics dashboard -->
  <g transform="translate(520, 50)">
    <rect x="0" y="0" width="120" height="80" fill="url(#cameraGradient)" rx="5"/>
    <text x="60" y="20" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Live Analytics</text>

    <!-- Performance metrics -->
    <text x="10" y="35" fill="#10b981" font-family="Arial" font-size="7">Throughput: 240/min</text>
    <text x="10" y="45" fill="#f59e0b" font-family="Arial" font-size="7">Cycle Time: 250ms</text>
    <text x="10" y="55" fill="#3b82f6" font-family="Arial" font-size="7">Uptime: 99.7%</text>
    <text x="10" y="65" fill="#ef4444" font-family="Arial" font-size="7">Rejects: 0.3%</text>

    <!-- Real-time graph -->
    <g stroke="#10b981" stroke-width="1" fill="none" opacity="0.8">
      <polyline points="10,75 20,73 30,71 40,74 50,70 60,72 70,69 80,71 90,68 100,70 110,67"/>
    </g>
  </g>

  <!-- Temperature and environmental monitoring -->
  <g transform="translate(50, 320)">
    <rect x="0" y="0" width="60" height="40" fill="url(#cameraGradient)" rx="3"/>
    <circle cx="30" cy="20" r="12" fill="#1e293b"/>
    <text x="30" y="25" text-anchor="middle" fill="#f59e0b" font-family="Arial" font-size="8">23°C</text>
    <circle cx="15" cy="10" r="2" fill="#10b981">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="45" cy="10" r="2" fill="#3b82f6">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
