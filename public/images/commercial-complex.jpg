<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="skyGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mallGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F7FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E2E8F0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="officeGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4299E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182CE;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hotelGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ED8936;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DD6B20;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="glassReflection" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1200" height="800" fill="url(#skyGrad)"/>
  
  <!-- Ground -->
  <rect x="0" y="650" width="1200" height="150" fill="#A0AEC0"/>
  
  <!-- Main Plaza -->
  <ellipse cx="600" cy="680" rx="400" ry="80" fill="#CBD5E0"/>
  
  <!-- Central Shopping Mall -->
  <rect x="300" y="400" width="600" height="250" fill="url(#mallGrad)" stroke="#CBD5E0" stroke-width="3" rx="10"/>
  
  <!-- Mall Entrance -->
  <rect x="550" y="500" width="100" height="150" fill="url(#mallGrad)" stroke="#CBD5E0" stroke-width="2" rx="5"/>
  <rect x="570" y="520" width="60" height="130" fill="#E2E8F0" rx="3"/>
  
  <!-- Mall Glass Facade -->
  <rect x="320" y="420" width="560" height="200" fill="url(#glassReflection)" rx="8"/>
  
  <!-- Mall Signage -->
  <rect x="450" y="380" width="300" height="40" fill="#2D3748" rx="5"/>
  <text x="600" y="405" font-family="Arial" font-size="24" fill="#FFF" text-anchor="middle">SHOPPING CENTER</text>
  
  <!-- Office Tower 1 -->
  <rect x="100" y="100" width="150" height="550" fill="url(#officeGrad)" stroke="#2B6CB0" stroke-width="3" rx="8"/>
  
  <!-- Office Windows Pattern -->
  <g>
    <!-- Floor 1 -->
    <rect x="120" y="140" width="25" height="30" fill="#E2E8F0" opacity="0.8" rx="2"/>
    <rect x="155" y="140" width="25" height="30" fill="#4A90E2" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4s" repeatCount="indefinite"/>
    </rect>
    <rect x="190" y="140" width="25" height="30" fill="#E2E8F0" opacity="0.8" rx="2"/>
    <rect x="225" y="140" width="15" height="30" fill="#48BB78" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Floor 2 -->
    <rect x="120" y="180" width="25" height="30" fill="#48BB78" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="155" y="180" width="25" height="30" fill="#E2E8F0" opacity="0.8" rx="2"/>
    <rect x="190" y="180" width="25" height="30" fill="#ED8936" opacity="0.8" rx="2">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="225" y="180" width="15" height="30" fill="#E2E8F0" opacity="0.8" rx="2"/>
    
    <!-- Repeat pattern for more floors -->
    <g transform="translate(0,50)">
      <rect x="120" y="180" width="25" height="30" fill="#4A90E2" opacity="0.8" rx="2">
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2.8s" repeatCount="indefinite"/>
      </rect>
      <rect x="155" y="180" width="25" height="30" fill="#48BB78" opacity="0.8" rx="2">
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4.2s" repeatCount="indefinite"/>
      </rect>
      <rect x="190" y="180" width="25" height="30" fill="#E2E8F0" opacity="0.8" rx="2"/>
      <rect x="225" y="180" width="15" height="30" fill="#ED8936" opacity="0.8" rx="2">
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3.2s" repeatCount="indefinite"/>
      </rect>
    </g>
  </g>
  
  <!-- Hotel Tower -->
  <rect x="950" y="150" width="180" height="500" fill="url(#hotelGrad)" stroke="#C05621" stroke-width="3" rx="8"/>
  
  <!-- Hotel Balconies -->
  <g>
    <rect x="970" y="200" width="140" height="8" fill="#2D3748" rx="2"/>
    <rect x="970" y="240" width="140" height="8" fill="#2D3748" rx="2"/>
    <rect x="970" y="280" width="140" height="8" fill="#2D3748" rx="2"/>
    <rect x="970" y="320" width="140" height="8" fill="#2D3748" rx="2"/>
    <rect x="970" y="360" width="140" height="8" fill="#2D3748" rx="2"/>
  </g>
  
  <!-- Hotel Windows -->
  <g>
    <rect x="980" y="210" width="20" height="25" fill="#FFF" opacity="0.9" rx="2"/>
    <rect x="1010" y="210" width="20" height="25" fill="#F7FAFC" opacity="0.7" rx="2"/>
    <rect x="1040" y="210" width="20" height="25" fill="#FFF" opacity="0.9" rx="2"/>
    <rect x="1070" y="210" width="20" height="25" fill="#F7FAFC" opacity="0.7" rx="2"/>
    <rect x="1100" y="210" width="20" height="25" fill="#FFF" opacity="0.9" rx="2"/>
    
    <rect x="980" y="250" width="20" height="25" fill="#F7FAFC" opacity="0.7" rx="2"/>
    <rect x="1010" y="250" width="20" height="25" fill="#FFF" opacity="0.9" rx="2"/>
    <rect x="1040" y="250" width="20" height="25" fill="#F7FAFC" opacity="0.7" rx="2"/>
    <rect x="1070" y="250" width="20" height="25" fill="#FFF" opacity="0.9" rx="2"/>
    <rect x="1100" y="250" width="20" height="25" fill="#F7FAFC" opacity="0.7" rx="2"/>
  </g>
  
  <!-- Hotel Sign -->
  <rect x="980" y="120" width="140" height="30" fill="#2D3748" rx="5"/>
  <text x="1050" y="140" font-family="Arial" font-size="16" fill="#FFF" text-anchor="middle">LUXURY HOTEL</text>
  
  <!-- Office Tower 2 -->
  <rect x="50" y="200" width="120" height="450" fill="url(#officeGrad)" stroke="#2B6CB0" stroke-width="2" rx="6"/>
  
  <!-- Parking Garage -->
  <rect x="700" y="500" width="200" height="150" fill="#718096" stroke="#4A5568" stroke-width="2" rx="5"/>
  
  <!-- Parking Levels -->
  <g stroke="#2D3748" stroke-width="1">
    <line x1="700" y1="530" x2="900" y2="530"/>
    <line x1="700" y1="560" x2="900" y2="560"/>
    <line x1="700" y1="590" x2="900" y2="590"/>
    <line x1="700" y1="620" x2="900" y2="620"/>
  </g>
  
  <!-- Parking Sign -->
  <rect x="750" y="480" width="100" height="20" fill="#2D3748" rx="3"/>
  <text x="800" y="495" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">PARKING</text>
  
  <!-- Cars in Parking -->
  <g>
    <rect x="720" y="540" width="25" height="15" fill="#E53E3E" rx="3"/>
    <rect x="750" y="540" width="25" height="15" fill="#4299E1" rx="3"/>
    <rect x="780" y="540" width="25" height="15" fill="#48BB78" rx="3"/>
    
    <rect x="720" y="570" width="25" height="15" fill="#ED8936" rx="3"/>
    <rect x="750" y="570" width="25" height="15" fill="#9F7AEA" rx="3"/>
    <rect x="780" y="570" width="25" height="15" fill="#F56565" rx="3"/>
  </g>
  
  <!-- Restaurant/Food Court -->
  <rect x="300" y="350" width="200" height="50" fill="#F7FAFC" stroke="#E2E8F0" stroke-width="2" rx="5"/>
  <text x="400" y="380" font-family="Arial" font-size="14" fill="#2D3748" text-anchor="middle">FOOD COURT</text>
  
  <!-- Restaurant Umbrellas -->
  <g>
    <circle cx="330" cy="330" r="15" fill="#E53E3E" opacity="0.8"/>
    <rect x="328" y="330" width="4" height="20" fill="#4A5568"/>
    
    <circle cx="370" cy="330" r="15" fill="#48BB78" opacity="0.8"/>
    <rect x="368" y="330" width="4" height="20" fill="#4A5568"/>
    
    <circle cx="410" cy="330" r="15" fill="#4299E1" opacity="0.8"/>
    <rect x="408" y="330" width="4" height="20" fill="#4A5568"/>
    
    <circle cx="450" cy="330" r="15" fill="#ED8936" opacity="0.8"/>
    <rect x="448" y="330" width="4" height="20" fill="#4A5568"/>
  </g>
  
  <!-- Cinema -->
  <rect x="520" y="350" width="180" height="50" fill="#2D3748" stroke="#4A5568" stroke-width="2" rx="5"/>
  <text x="610" y="380" font-family="Arial" font-size="14" fill="#FFF" text-anchor="middle">CINEMA COMPLEX</text>
  
  <!-- Cinema Marquee -->
  <rect x="540" y="320" width="140" height="30" fill="#000" rx="3"/>
  <text x="610" y="340" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">NOW SHOWING</text>
  
  <!-- Marquee Lights -->
  <g>
    <circle cx="550" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="570" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="0.2s"/>
    </circle>
    <circle cx="590" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="0.4s"/>
    </circle>
    <circle cx="610" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
    <circle cx="630" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="0.8s"/>
    </circle>
    <circle cx="650" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="1s"/>
    </circle>
    <circle cx="670" cy="335" r="3" fill="#ED8936">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite" begin="1.2s"/>
    </circle>
  </g>
  
  <!-- Fountain in Plaza -->
  <circle cx="600" cy="680" r="40" fill="#4A90E2" opacity="0.6"/>
  <circle cx="600" cy="680" r="25" fill="#63B3ED" opacity="0.8"/>
  
  <!-- Water Jets -->
  <g>
    <circle cx="600" cy="680" r="3" fill="#FFF">
      <animate attributeName="r" values="3;8;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="590" cy="670" r="2" fill="#FFF">
      <animate attributeName="r" values="2;6;2" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="610" cy="690" r="2" fill="#FFF">
      <animate attributeName="r" values="2;6;2" dur="1.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="1.8s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Pedestrian Walkways -->
  <rect x="250" y="650" width="700" height="15" fill="#CBD5E0" rx="3"/>
  
  <!-- Walkway Pattern -->
  <g stroke="#A0AEC0" stroke-width="1">
    <line x1="280" y1="650" x2="280" y2="665"/>
    <line x1="320" y1="650" x2="320" y2="665"/>
    <line x1="360" y1="650" x2="360" y2="665"/>
    <line x1="400" y1="650" x2="400" y2="665"/>
    <line x1="440" y1="650" x2="440" y2="665"/>
    <line x1="480" y1="650" x2="480" y2="665"/>
    <line x1="520" y1="650" x2="520" y2="665"/>
    <line x1="560" y1="650" x2="560" y2="665"/>
    <line x1="640" y1="650" x2="640" y2="665"/>
    <line x1="680" y1="650" x2="680" y2="665"/>
    <line x1="720" y1="650" x2="720" y2="665"/>
    <line x1="760" y1="650" x2="760" y2="665"/>
    <line x1="800" y1="650" x2="800" y2="665"/>
    <line x1="840" y1="650" x2="840" y2="665"/>
    <line x1="880" y1="650" x2="880" y2="665"/>
    <line x1="920" y1="650" x2="920" y2="665"/>
  </g>
  
  <!-- People/Pedestrians -->
  <g>
    <!-- Person 1 -->
    <circle cx="350" cy="640" r="4" fill="#F7FAFC"/>
    <rect x="348" y="644" width="4" height="12" fill="#4299E1" rx="1"/>
    <rect x="346" y="656" width="8" height="8" fill="#2D3748" rx="1"/>
    
    <!-- Person 2 -->
    <circle cx="450" cy="640" r="4" fill="#F7FAFC"/>
    <rect x="448" y="644" width="4" height="12" fill="#E53E3E" rx="1"/>
    <rect x="446" y="656" width="8" height="8" fill="#2D3748" rx="1"/>
    
    <!-- Person 3 -->
    <circle cx="750" cy="640" r="4" fill="#F7FAFC"/>
    <rect x="748" y="644" width="4" height="12" fill="#48BB78" rx="1"/>
    <rect x="746" y="656" width="8" height="8" fill="#2D3748" rx="1"/>
  </g>
  
  <!-- Street Lamps -->
  <g>
    <rect x="200" y="600" width="6" height="50" fill="#4A5568" rx="2"/>
    <circle cx="203" cy="595" r="12" fill="#F7FAFC" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <rect x="400" y="600" width="6" height="50" fill="#4A5568" rx="2"/>
    <circle cx="403" cy="595" r="12" fill="#F7FAFC" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="4s" repeatCount="indefinite" begin="2s"/>
    </circle>
    
    <rect x="800" y="600" width="6" height="50" fill="#4A5568" rx="2"/>
    <circle cx="803" cy="595" r="12" fill="#F7FAFC" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="4s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
  
  <!-- Trees/Landscaping -->
  <g>
    <!-- Tree 1 -->
    <rect x="150" y="620" width="8" height="30" fill="#8B4513" rx="2"/>
    <circle cx="154" cy="610" r="20" fill="#48BB78" opacity="0.8"/>
    
    <!-- Tree 2 -->
    <rect x="850" y="620" width="8" height="30" fill="#8B4513" rx="2"/>
    <circle cx="854" cy="610" r="20" fill="#48BB78" opacity="0.8"/>
    
    <!-- Tree 3 -->
    <rect x="1150" y="620" width="8" height="30" fill="#8B4513" rx="2"/>
    <circle cx="1154" cy="610" r="20" fill="#48BB78" opacity="0.8"/>
  </g>
  
  <!-- Digital Billboards -->
  <rect x="20" y="300" width="60" height="40" fill="#000" rx="3"/>
  <rect x="25" y="305" width="50" height="30" fill="#4299E1" rx="2">
    <animate attributeName="fill" values="#4299E1;#48BB78;#ED8936;#E53E3E;#4299E1" dur="8s" repeatCount="indefinite"/>
  </rect>
  <text x="50" y="325" font-family="Arial" font-size="8" fill="#FFF" text-anchor="middle">AD</text>
  
  <!-- Skybridge -->
  <rect x="250" y="300" width="100" height="15" fill="#E2E8F0" stroke="#CBD5E0" stroke-width="2" rx="5"/>
  <rect x="260" y="305" width="80" height="5" fill="#4A90E2" opacity="0.6" rx="2"/>
</svg>