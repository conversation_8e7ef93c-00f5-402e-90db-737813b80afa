<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="skyBg" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="terminalGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F7FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E2E8F0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="trainGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4299E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182CE;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="busGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#48BB78;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38A169;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="roadGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1200" height="800" fill="url(#skyBg)"/>
  
  <!-- Ground -->
  <rect x="0" y="650" width="1200" height="150" fill="#A0AEC0"/>
  
  <!-- Main Terminal Building -->
  <rect x="300" y="200" width="600" height="300" fill="url(#terminalGrad)" stroke="#CBD5E0" stroke-width="3" rx="15"/>
  
  <!-- Terminal Roof -->
  <ellipse cx="600" cy="200" rx="320" ry="40" fill="#718096"/>
  
  <!-- Terminal Glass Facade -->
  <rect x="320" y="220" width="560" height="200" fill="#4A90E2" opacity="0.3" rx="10"/>
  
  <!-- Terminal Entrance -->
  <rect x="550" y="400" width="100" height="100" fill="url(#terminalGrad)" stroke="#CBD5E0" stroke-width="2" rx="5"/>
  <rect x="570" y="420" width="60" height="80" fill="#E2E8F0" rx="3"/>
  
  <!-- Terminal Signage -->
  <rect x="450" y="160" width="300" height="40" fill="#2D3748" rx="5"/>
  <text x="600" y="185" font-family="Arial" font-size="20" fill="#FFF" text-anchor="middle">TRANSPORTATION HUB</text>
  
  <!-- Control Tower -->
  <rect x="950" y="100" width="80" height="400" fill="#4A5568" stroke="#2D3748" stroke-width="2" rx="5"/>
  <rect x="940" y="80" width="100" height="40" fill="#2D3748" rx="8"/>
  
  <!-- Control Tower Windows -->
  <rect x="960" y="90" width="80" height="25" fill="#4A90E2" opacity="0.8" rx="3"/>
  <rect x="965" y="95" width="15" height="15" fill="#48BB78" rx="1">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </rect>
  <rect x="985" y="95" width="15" height="15" fill="#E53E3E" rx="1">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="1005" y="95" width="15" height="15" fill="#ED8936" rx="1">
    <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
  </rect>
  <rect x="1025" y="95" width="15" height="15" fill="#4299E1" rx="1">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.8s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Railway Tracks -->
  <g>
    <!-- Track 1 -->
    <rect x="0" y="580" width="1200" height="8" fill="#4A5568"/>
    <rect x="0" y="584" width="1200" height="2" fill="#2D3748"/>
    <!-- Railway Ties -->
    <g fill="#8B4513">
      <rect x="50" y="575" width="15" height="18" rx="2"/>
      <rect x="100" y="575" width="15" height="18" rx="2"/>
      <rect x="150" y="575" width="15" height="18" rx="2"/>
      <rect x="200" y="575" width="15" height="18" rx="2"/>
      <rect x="250" y="575" width="15" height="18" rx="2"/>
      <rect x="300" y="575" width="15" height="18" rx="2"/>
      <rect x="350" y="575" width="15" height="18" rx="2"/>
      <rect x="400" y="575" width="15" height="18" rx="2"/>
      <rect x="450" y="575" width="15" height="18" rx="2"/>
      <rect x="500" y="575" width="15" height="18" rx="2"/>
      <rect x="550" y="575" width="15" height="18" rx="2"/>
      <rect x="600" y="575" width="15" height="18" rx="2"/>
      <rect x="650" y="575" width="15" height="18" rx="2"/>
      <rect x="700" y="575" width="15" height="18" rx="2"/>
      <rect x="750" y="575" width="15" height="18" rx="2"/>
      <rect x="800" y="575" width="15" height="18" rx="2"/>
      <rect x="850" y="575" width="15" height="18" rx="2"/>
      <rect x="900" y="575" width="15" height="18" rx="2"/>
      <rect x="950" y="575" width="15" height="18" rx="2"/>
      <rect x="1000" y="575" width="15" height="18" rx="2"/>
      <rect x="1050" y="575" width="15" height="18" rx="2"/>
      <rect x="1100" y="575" width="15" height="18" rx="2"/>
      <rect x="1150" y="575" width="15" height="18" rx="2"/>
    </g>
    
    <!-- Track 2 -->
    <rect x="0" y="620" width="1200" height="8" fill="#4A5568"/>
    <rect x="0" y="624" width="1200" height="2" fill="#2D3748"/>
  </g>
  
  <!-- High-Speed Train -->
  <g transform="translate(100,540)">
    <!-- Train Body -->
    <rect x="0" y="0" width="200" height="35" fill="url(#trainGrad)" rx="8"/>
    <!-- Train Nose -->
    <polygon points="200,0 250,17.5 200,35" fill="#2B6CB0"/>
    <!-- Train Windows -->
    <rect x="20" y="8" width="25" height="15" fill="#E2E8F0" rx="2"/>
    <rect x="55" y="8" width="25" height="15" fill="#E2E8F0" rx="2"/>
    <rect x="90" y="8" width="25" height="15" fill="#E2E8F0" rx="2"/>
    <rect x="125" y="8" width="25" height="15" fill="#E2E8F0" rx="2"/>
    <rect x="160" y="8" width="25" height="15" fill="#E2E8F0" rx="2"/>
    <!-- Train Wheels -->
    <circle cx="30" cy="40" r="8" fill="#2D3748"/>
    <circle cx="70" cy="40" r="8" fill="#2D3748"/>
    <circle cx="130" cy="40" r="8" fill="#2D3748"/>
    <circle cx="170" cy="40" r="8" fill="#2D3748"/>
    <circle cx="220" cy="40" r="8" fill="#2D3748"/>
    <!-- Pantograph -->
    <rect x="100" y="-15" width="3" height="15" fill="#4A5568"/>
    <rect x="90" y="-18" width="23" height="3" fill="#4A5568"/>
    <!-- Movement Animation -->
    <animateTransform attributeName="transform" type="translate" values="100,540; 800,540; 100,540" dur="20s" repeatCount="indefinite"/>
  </g>
  
  <!-- Subway Train -->
  <g transform="translate(200,580)">
    <rect x="0" y="0" width="150" height="30" fill="#9F7AEA" rx="6"/>
    <rect x="10" y="6" width="20" height="12" fill="#E2E8F0" rx="1"/>
    <rect x="35" y="6" width="20" height="12" fill="#E2E8F0" rx="1"/>
    <rect x="60" y="6" width="20" height="12" fill="#E2E8F0" rx="1"/>
    <rect x="85" y="6" width="20" height="12" fill="#E2E8F0" rx="1"/>
    <rect x="110" y="6" width="20" height="12" fill="#E2E8F0" rx="1"/>
    <circle cx="25" cy="35" r="6" fill="#2D3748"/>
    <circle cx="75" cy="35" r="6" fill="#2D3748"/>
    <circle cx="125" cy="35" r="6" fill="#2D3748"/>
    <animateTransform attributeName="transform" type="translate" values="200,580; 900,580; 200,580" dur="25s" repeatCount="indefinite"/>
  </g>
  
  <!-- Bus Terminal -->
  <rect x="50" y="450" width="200" height="100" fill="#E2E8F0" stroke="#CBD5E0" stroke-width="2" rx="8"/>
  <text x="150" y="430" font-family="Arial" font-size="14" fill="#2D3748" text-anchor="middle">BUS TERMINAL</text>
  
  <!-- Bus Bays -->
  <g>
    <rect x="70" y="470" width="40" height="60" fill="#CBD5E0" stroke="#A0AEC0" stroke-width="1" rx="3"/>
    <text x="90" y="505" font-family="Arial" font-size="10" fill="#2D3748" text-anchor="middle">1</text>
    
    <rect x="120" y="470" width="40" height="60" fill="#CBD5E0" stroke="#A0AEC0" stroke-width="1" rx="3"/>
    <text x="140" y="505" font-family="Arial" font-size="10" fill="#2D3748" text-anchor="middle">2</text>
    
    <rect x="170" y="470" width="40" height="60" fill="#CBD5E0" stroke="#A0AEC0" stroke-width="1" rx="3"/>
    <text x="190" y="505" font-family="Arial" font-size="10" fill="#2D3748" text-anchor="middle">3</text>
  </g>
  
  <!-- Buses -->
  <g transform="translate(65,460)">
    <rect x="0" y="0" width="50" height="20" fill="url(#busGrad)" rx="5"/>
    <rect x="5" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="15" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="25" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="35" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <circle cx="12" cy="25" r="5" fill="#2D3748"/>
    <circle cx="38" cy="25" r="5" fill="#2D3748"/>
    <rect x="45" y="8" width="8" height="4" fill="#E53E3E" rx="1"/>
  </g>
  
  <g transform="translate(165,460)">
    <rect x="0" y="0" width="50" height="20" fill="#ED8936" rx="5"/>
    <rect x="5" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="15" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="25" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <rect x="35" y="3" width="8" height="6" fill="#E2E8F0" rx="1"/>
    <circle cx="12" cy="25" r="5" fill="#2D3748"/>
    <circle cx="38" cy="25" r="5" fill="#2D3748"/>
  </g>
  
  <!-- Airport Section -->
  <rect x="1000" y="300" width="180" height="200" fill="url(#terminalGrad)" stroke="#CBD5E0" stroke-width="2" rx="10"/>
  <text x="1090" y="280" font-family="Arial" font-size="12" fill="#2D3748" text-anchor="middle">AIRPORT</text>
  
  <!-- Airplane -->
  <g transform="translate(1050,350)">
    <ellipse cx="0" cy="0" rx="40" ry="8" fill="#E2E8F0"/>
    <ellipse cx="-15" cy="0" rx="25" ry="3" fill="#E2E8F0"/>
    <ellipse cx="15" cy="0" rx="25" ry="3" fill="#E2E8F0"/>
    <rect x="-5" y="-2" width="10" height="4" fill="#4299E1"/>
    <circle cx="-30" cy="0" r="3" fill="#2D3748"/>
    <circle cx="30" cy="0" r="3" fill="#2D3748"/>
  </g>
  
  <!-- Runway -->
  <rect x="1020" y="520" width="160" height="20" fill="#4A5568" rx="2"/>
  <rect x="1025" y="525" width="150" height="10" fill="#2D3748" rx="1"/>
  
  <!-- Runway Markings -->
  <g stroke="#FFF" stroke-width="2">
    <line x1="1040" y1="525" x2="1040" y2="535"/>
    <line x1="1060" y1="525" x2="1060" y2="535"/>
    <line x1="1080" y1="525" x2="1080" y2="535"/>
    <line x1="1100" y1="525" x2="1100" y2="535"/>
    <line x1="1120" y1="525" x2="1120" y2="535"/>
    <line x1="1140" y1="525" x2="1140" y2="535"/>
    <line x1="1160" y1="525" x2="1160" y2="535"/>
  </g>
  
  <!-- Taxi/Ride Share Area -->
  <rect x="300" y="550" width="150" height="80" fill="#F7FAFC" stroke="#E2E8F0" stroke-width="2" rx="5"/>
  <text x="375" y="535" font-family="Arial" font-size="12" fill="#2D3748" text-anchor="middle">TAXI STAND</text>
  
  <!-- Taxis -->
  <g>
    <rect x="320" y="570" width="35" height="15" fill="#F1C40F" rx="3"/>
    <rect x="325" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <rect x="333" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <rect x="341" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <circle cx="330" cy="590" r="4" fill="#2D3748"/>
    <circle cx="345" cy="590" r="4" fill="#2D3748"/>
    <rect x="350" y="575" width="3" height="2" fill="#E53E3E"/>
    
    <rect x="370" y="570" width="35" height="15" fill="#E53E3E" rx="3"/>
    <rect x="375" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <rect x="383" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <rect x="391" y="573" width="6" height="4" fill="#E2E8F0" rx="1"/>
    <circle cx="380" cy="590" r="4" fill="#2D3748"/>
    <circle cx="395" cy="590" r="4" fill="#2D3748"/>
    
    <rect x="320" y="600" width="35" height="15" fill="#4299E1" rx="3"/>
    <circle cx="330" cy="620" r="4" fill="#2D3748"/>
    <circle cx="345" cy="620" r="4" fill="#2D3748"/>
  </g>
  
  <!-- Parking Structure -->
  <rect x="500" y="550" width="120" height="80" fill="#718096" stroke="#4A5568" stroke-width="2" rx="5"/>
  <text x="560" y="535" font-family="Arial" font-size="12" fill="#2D3748" text-anchor="middle">PARKING</text>
  
  <!-- Parking Levels -->
  <g stroke="#2D3748" stroke-width="1">
    <line x1="500" y1="570" x2="620" y2="570"/>
    <line x1="500" y1="590" x2="620" y2="590"/>
    <line x1="500" y1="610" x2="620" y2="610"/>
  </g>
  
  <!-- Information Displays -->
  <rect x="650" y="300" width="80" height="60" fill="#2D3748" rx="5"/>
  <rect x="660" y="310" width="60" height="40" fill="#4299E1" rx="3">
    <animate attributeName="fill" values="#4299E1;#48BB78;#ED8936;#4299E1" dur="6s" repeatCount="indefinite"/>
  </rect>
  <text x="690" y="335" font-family="Arial" font-size="10" fill="#FFF" text-anchor="middle">ARRIVALS</text>
  <text x="690" y="345" font-family="Arial" font-size="8" fill="#FFF" text-anchor="middle">15:30</text>
  
  <rect x="750" y="300" width="80" height="60" fill="#2D3748" rx="5"/>
  <rect x="760" y="310" width="60" height="40" fill="#E53E3E" rx="3">
    <animate attributeName="fill" values="#E53E3E;#ED8936;#48BB78;#E53E3E" dur="5s" repeatCount="indefinite"/>
  </rect>
  <text x="790" y="335" font-family="Arial" font-size="10" fill="#FFF" text-anchor="middle">DEPARTURES</text>
  <text x="790" y="345" font-family="Arial" font-size="8" fill="#FFF" text-anchor="middle">16:45</text>
  
  <!-- Traffic Lights -->
  <g transform="translate(280,500)">
    <rect x="0" y="0" width="12" height="45" fill="#2D3748" rx="3"/>
    <circle cx="6" cy="10" r="4" fill="#E53E3E">
      <animate attributeName="fill" values="#E53E3E;#4A5568;#E53E3E" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="6" cy="22" r="4" fill="#ED8936">
      <animate attributeName="fill" values="#4A5568;#ED8936;#4A5568" dur="6s" repeatCount="indefinite" begin="2s"/>
    </circle>
    <circle cx="6" cy="34" r="4" fill="#48BB78">
      <animate attributeName="fill" values="#4A5568;#48BB78;#4A5568" dur="6s" repeatCount="indefinite" begin="4s"/>
    </circle>
    <rect x="-3" y="45" width="18" height="25" fill="#4A5568" rx="2"/>
  </g>
  
  <!-- Pedestrian Crosswalk -->
  <g>
    <rect x="280" y="630" width="400" height="20" fill="#CBD5E0" rx="2"/>
    <g stroke="#FFF" stroke-width="3">
      <line x1="290" y1="630" x2="290" y2="650"/>
      <line x1="310" y1="630" x2="310" y2="650"/>
      <line x1="330" y1="630" x2="330" y2="650"/>
      <line x1="350" y1="630" x2="350" y2="650"/>
      <line x1="370" y1="630" x2="370" y2="650"/>
      <line x1="390" y1="630" x2="390" y2="650"/>
      <line x1="410" y1="630" x2="410" y2="650"/>
      <line x1="430" y1="630" x2="430" y2="650"/>
      <line x1="450" y1="630" x2="450" y2="650"/>
      <line x1="470" y1="630" x2="470" y2="650"/>
      <line x1="490" y1="630" x2="490" y2="650"/>
      <line x1="510" y1="630" x2="510" y2="650"/>
      <line x1="530" y1="630" x2="530" y2="650"/>
      <line x1="550" y1="630" x2="550" y2="650"/>
      <line x1="570" y1="630" x2="570" y2="650"/>
      <line x1="590" y1="630" x2="590" y2="650"/>
      <line x1="610" y1="630" x2="610" y2="650"/>
      <line x1="630" y1="630" x2="630" y2="650"/>
      <line x1="650" y1="630" x2="650" y2="650"/>
      <line x1="670" y1="630" x2="670" y2="650"/>
    </g>
  </g>
  
  <!-- Security Checkpoint -->
  <rect x="400" y="380" width="100" height="20" fill="#E53E3E" opacity="0.7" rx="3"/>
  <text x="450" y="395" font-family="Arial" font-size="10" fill="#FFF" text-anchor="middle">SECURITY</text>
  
  <!-- People/Passengers -->
  <g>
    <!-- Person 1 -->
    <circle cx="350" cy="520" r="3" fill="#F7FAFC"/>
    <rect x="348" y="523" width="4" height="10" fill="#4299E1" rx="1"/>
    <rect x="346" y="533" width="8" height="6" fill="#2D3748" rx="1"/>
    
    <!-- Person 2 -->
    <circle cx="450" cy="520" r="3" fill="#F7FAFC"/>
    <rect x="448" y="523" width="4" height="10" fill="#E53E3E" rx="1"/>
    <rect x="446" y="533" width="8" height="6" fill="#2D3748" rx="1"/>
    
    <!-- Person 3 -->
    <circle cx="550" cy="520" r="3" fill="#F7FAFC"/>
    <rect x="548" y="523" width="4" height="10" fill="#48BB78" rx="1"/>
    <rect x="546" y="533" width="8" height="6" fill="#2D3748" rx="1"/>
    
    <!-- Person 4 -->
    <circle cx="650" cy="520" r="3" fill="#F7FAFC"/>
    <rect x="648" y="523" width="4" height="10" fill="#ED8936" rx="1"/>
    <rect x="646" y="533" width="8" height="6" fill="#2D3748" rx="1"/>
  </g>
  
  <!-- Status Indicators -->
  <text x="50" y="50" font-family="monospace" font-size="14" fill="#2D3748">TRANSPORTATION HUB STATUS</text>
  <text x="50" y="70" font-family="monospace" font-size="12" fill="#48BB78">TRAINS: ON TIME | BUSES: OPERATIONAL</text>
  <text x="50" y="90" font-family="monospace" font-size="12" fill="#4299E1">FLIGHTS: SCHEDULED | TRAFFIC: MODERATE</text>
</svg>