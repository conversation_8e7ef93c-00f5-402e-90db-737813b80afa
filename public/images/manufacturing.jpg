<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" class="w-full h-full">
  <defs>
    <linearGradient id="skyBg" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="factoryWall" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E2E8F0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CBD5E0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="machineGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4299E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182CE;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="conveyorGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1200" height="800" fill="url(#skyBg)"/>
  
  <!-- Factory Floor -->
  <rect x="0" y="600" width="1200" height="200" fill="#A0AEC0"/>
  
  <!-- Main Factory Building -->
  <rect x="50" y="200" width="1100" height="400" fill="url(#factoryWall)"/>
  
  <!-- Factory Roof -->
  <polygon points="50,200 600,100 1150,200" fill="#718096"/>
  
  <!-- Large Factory Windows -->
  <rect x="100" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="220" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="340" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="460" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="580" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="700" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="820" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  <rect x="940" y="250" width="80" height="60" fill="#4A90E2" opacity="0.7"/>
  
  <!-- Window frames -->
  <g stroke="#2D3748" stroke-width="2" fill="none">
    <rect x="100" y="250" width="80" height="60"/>
    <line x1="140" y1="250" x2="140" y2="310"/>
    <line x1="100" y1="280" x2="180" y2="280"/>
    
    <rect x="220" y="250" width="80" height="60"/>
    <line x1="260" y1="250" x2="260" y2="310"/>
    <line x1="220" y1="280" x2="300" y2="280"/>
    
    <rect x="340" y="250" width="80" height="60"/>
    <line x1="380" y1="250" x2="380" y2="310"/>
    <line x1="340" y1="280" x2="420" y2="280"/>
  </g>
  
  <!-- Production Line Machines -->
  <!-- Machine 1 -->
  <rect x="150" y="450" width="100" height="80" fill="url(#machineGrad)" rx="5"/>
  <rect x="160" y="460" width="20" height="15" fill="#E53E3E"/>
  <rect x="190" y="460" width="20" height="15" fill="#48BB78"/>
  <rect x="220" y="460" width="20" height="15" fill="#ED8936"/>
  <circle cx="200" cy="500" r="15" fill="#2D3748"/>
  <circle cx="200" cy="500" r="8" fill="#4A5568"/>
  
  <!-- Machine 2 -->
  <rect x="300" y="430" width="120" height="100" fill="url(#machineGrad)" rx="5"/>
  <rect x="310" y="440" width="25" height="20" fill="#E53E3E"/>
  <rect x="345" y="440" width="25" height="20" fill="#48BB78"/>
  <rect x="380" y="440" width="25" height="20" fill="#ED8936"/>
  <rect x="320" y="480" width="80" height="30" fill="#2D3748" rx="3"/>
  <circle cx="360" cy="520" r="8" fill="#FFF">
    <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Machine 3 -->
  <rect x="480" y="440" width="110" height="90" fill="url(#machineGrad)" rx="5"/>
  <rect x="490" y="450" width="90" height="40" fill="#2D3748" rx="3"/>
  <rect x="500" y="460" width="15" height="10" fill="#E53E3E"/>
  <rect x="525" y="460" width="15" height="10" fill="#48BB78"/>
  <rect x="550" y="460" width="15" height="10" fill="#ED8936"/>
  
  <!-- Robotic Arm -->
  <g transform="translate(650,480)">
    <rect x="0" y="0" width="15" height="60" fill="#4A5568" rx="3"/>
    <rect x="-5" y="-10" width="25" height="15" fill="#4A5568" rx="3"/>
    <g transform="rotate(30)">
      <rect x="0" y="-5" width="40" height="10" fill="#4A5568" rx="2"/>
      <circle cx="40" cy="0" r="8" fill="#E53E3E"/>
      <animateTransform attributeName="transform" type="rotate" values="30;-30;30" dur="4s" repeatCount="indefinite"/>
    </g>
  </g>
  
  <!-- Conveyor Belt -->
  <rect x="100" y="550" width="600" height="20" fill="url(#conveyorGrad)"/>
  <rect x="100" y="555" width="600" height="10" fill="#2D3748"/>
  
  <!-- Conveyor Belt Movement Lines -->
  <g stroke="#FFF" stroke-width="2" opacity="0.6">
    <line x1="120" y1="560" x2="140" y2="560">
      <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="3s" repeatCount="indefinite"/>
    </line>
    <line x1="180" y1="560" x2="200" y2="560">
      <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    </line>
    <line x1="240" y1="560" x2="260" y2="560">
      <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="3s" repeatCount="indefinite" begin="1s"/>
    </line>
  </g>
  
  <!-- Products on Conveyor -->
  <rect x="130" y="545" width="15" height="15" fill="#ED8936" rx="2">
    <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="6s" repeatCount="indefinite"/>
  </rect>
  <rect x="200" y="545" width="15" height="15" fill="#48BB78" rx="2">
    <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="6s" repeatCount="indefinite" begin="1s"/>
  </rect>
  <rect x="270" y="545" width="15" height="15" fill="#4299E1" rx="2">
    <animateTransform attributeName="transform" type="translate" values="0,0; 580,0; 0,0" dur="6s" repeatCount="indefinite" begin="2s"/>
  </rect>
  
  <!-- Quality Control Station -->
  <rect x="750" y="420" width="80" height="110" fill="#E2E8F0" stroke="#4A5568" stroke-width="2" rx="5"/>
  <rect x="760" y="430" width="60" height="40" fill="#2D3748"/>
  <rect x="770" y="440" width="40" height="20" fill="#4A90E2"/>
  <text x="790" y="455" font-family="Arial" font-size="8" fill="#FFF" text-anchor="middle">QC</text>
  <circle cx="790" cy="500" r="12" fill="#48BB78">
    <animate attributeName="fill" values="#48BB78;#E53E3E;#48BB78" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Overhead Crane -->
  <g transform="translate(0,-50)">
    <rect x="200" y="300" width="400" height="8" fill="#4A5568"/>
    <rect x="350" y="300" width="8" height="40" fill="#4A5568"/>
    <rect x="340" y="340" width="28" height="15" fill="#E53E3E" rx="3"/>
    <rect x="345" y="355" width="18" height="30" fill="#2D3748"/>
    <animateTransform attributeName="transform" type="translate" values="0,-50; 200,-50; 0,-50" dur="8s" repeatCount="indefinite"/>
  </g>
  
  <!-- Control Panel -->
  <rect x="900" y="400" width="60" height="80" fill="#2D3748" rx="5"/>
  <rect x="910" y="410" width="40" height="30" fill="#4A90E2"/>
  <circle cx="920" cy="450" r="5" fill="#E53E3E"/>
  <circle cx="935" cy="450" r="5" fill="#48BB78"/>
  <circle cx="950" cy="450" r="5" fill="#ED8936"/>
  <rect x="915" y="465" width="30" height="10" fill="#4A5568" rx="2"/>
  
  <!-- Forklift -->
  <g transform="translate(1000,520)">
    <rect x="0" y="0" width="50" height="25" fill="#ED8936" rx="3"/>
    <rect x="-5" y="-15" width="15" height="20" fill="#ED8936" rx="2"/>
    <rect x="45" y="-10" width="8" height="35" fill="#4A5568"/>
    <rect x="47" y="-25" width="4" height="15" fill="#4A5568"/>
    <circle cx="12" cy="30" r="8" fill="#2D3748"/>
    <circle cx="38" cy="30" r="8" fill="#2D3748"/>
    <circle cx="12" cy="30" r="5" fill="#4A5568"/>
    <circle cx="38" cy="30" r="5" fill="#4A5568"/>
  </g>
  
  <!-- Ventilation System -->
  <rect x="500" y="150" width="200" height="30" fill="#718096" rx="15"/>
  <rect x="520" y="160" width="20" height="10" fill="#2D3748" rx="2"/>
  <rect x="560" y="160" width="20" height="10" fill="#2D3748" rx="2"/>
  <rect x="600" y="160" width="20" height="10" fill="#2D3748" rx="2"/>
  <rect x="640" y="160" width="20" height="10" fill="#2D3748" rx="2"/>
  <rect x="680" y="160" width="20" height="10" fill="#2D3748" rx="2"/>
  
  <!-- Air Flow Indicators -->
  <g opacity="0.5">
    <path d="M 530,170 Q 535,165 540,170" stroke="#87CEEB" stroke-width="2" fill="none">
      <animate attributeName="d" values="M 530,170 Q 535,165 540,170; M 530,170 Q 535,175 540,170; M 530,170 Q 535,165 540,170" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M 570,170 Q 575,165 580,170" stroke="#87CEEB" stroke-width="2" fill="none">
      <animate attributeName="d" values="M 570,170 Q 575,165 580,170; M 570,170 Q 575,175 580,170; M 570,170 Q 575,165 580,170" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </path>
  </g>
  
  <!-- Energy Monitoring Displays -->
  <g opacity="0.8">
    <circle cx="200" cy="380" r="5" fill="#48BB78">
      <animate attributeName="r" values="5;10;5" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="360" r="5" fill="#4299E1">
      <animate attributeName="r" values="5;10;5" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="370" r="5" fill="#ED8936">
      <animate attributeName="r" values="5;10;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Safety Signs -->
  <rect x="80" y="350" width="30" height="30" fill="#E53E3E" rx="3"/>
  <text x="95" y="370" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">!</text>
  
  <rect x="1050" y="350" width="30" height="30" fill="#48BB78" rx="3"/>
  <text x="1065" y="370" font-family="Arial" font-size="12" fill="#FFF" text-anchor="middle">✓</text>
</svg>