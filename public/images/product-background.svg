<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="techBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3730a3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="boardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chipGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1f2937;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowEffect" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="url(#techBg)"/>
  
  <!-- Circuit board pattern -->
  <g opacity="0.3">
    <!-- Horizontal traces -->
    <line x1="0" y1="50" x2="800" y2="50" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="100" x2="800" y2="100" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="150" x2="800" y2="150" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="200" x2="800" y2="200" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="250" x2="800" y2="250" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="300" x2="800" y2="300" stroke="#3b82f6" stroke-width="1"/>
    <line x1="0" y1="350" x2="800" y2="350" stroke="#3b82f6" stroke-width="1"/>
    
    <!-- Vertical traces -->
    <line x1="100" y1="0" x2="100" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="200" y1="0" x2="200" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="300" y1="0" x2="300" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="400" y1="0" x2="400" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="500" y1="0" x2="500" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="600" y1="0" x2="600" y2="400" stroke="#3b82f6" stroke-width="1"/>
    <line x1="700" y1="0" x2="700" y2="400" stroke="#3b82f6" stroke-width="1"/>
  </g>
  
  <!-- Main circuit board -->
  <rect x="250" y="150" width="300" height="200" fill="url(#boardGradient)" rx="10" stroke="#047857" stroke-width="2"/>
  
  <!-- CPU/Main processor -->
  <rect x="350" y="200" width="100" height="100" fill="url(#chipGradient)" rx="5" stroke="#6b7280" stroke-width="1"/>
  <rect x="360" y="210" width="80" height="80" fill="#1f2937" rx="3"/>
  
  <!-- CPU pins -->
  <g fill="#9ca3af">
    <!-- Top pins -->
    <rect x="365" y="205" width="2" height="5"/>
    <rect x="370" y="205" width="2" height="5"/>
    <rect x="375" y="205" width="2" height="5"/>
    <rect x="380" y="205" width="2" height="5"/>
    <rect x="385" y="205" width="2" height="5"/>
    <rect x="390" y="205" width="2" height="5"/>
    <rect x="395" y="205" width="2" height="5"/>
    <rect x="400" y="205" width="2" height="5"/>
    <rect x="405" y="205" width="2" height="5"/>
    <rect x="410" y="205" width="2" height="5"/>
    <rect x="415" y="205" width="2" height="5"/>
    <rect x="420" y="205" width="2" height="5"/>
    <rect x="425" y="205" width="2" height="5"/>
    <rect x="430" y="205" width="2" height="5"/>
    <rect x="435" y="205" width="2" height="5"/>
    
    <!-- Bottom pins -->
    <rect x="365" y="290" width="2" height="5"/>
    <rect x="370" y="290" width="2" height="5"/>
    <rect x="375" y="290" width="2" height="5"/>
    <rect x="380" y="290" width="2" height="5"/>
    <rect x="385" y="290" width="2" height="5"/>
    <rect x="390" y="290" width="2" height="5"/>
    <rect x="395" y="290" width="2" height="5"/>
    <rect x="400" y="290" width="2" height="5"/>
    <rect x="405" y="290" width="2" height="5"/>
    <rect x="410" y="290" width="2" height="5"/>
    <rect x="415" y="290" width="2" height="5"/>
    <rect x="420" y="290" width="2" height="5"/>
    <rect x="425" y="290" width="2" height="5"/>
    <rect x="430" y="290" width="2" height="5"/>
    <rect x="435" y="290" width="2" height="5"/>
    
    <!-- Left pins -->
    <rect x="345" y="215" width="5" height="2"/>
    <rect x="345" y="220" width="5" height="2"/>
    <rect x="345" y="225" width="5" height="2"/>
    <rect x="345" y="230" width="5" height="2"/>
    <rect x="345" y="235" width="5" height="2"/>
    <rect x="345" y="240" width="5" height="2"/>
    <rect x="345" y="245" width="5" height="2"/>
    <rect x="345" y="250" width="5" height="2"/>
    <rect x="345" y="255" width="5" height="2"/>
    <rect x="345" y="260" width="5" height="2"/>
    <rect x="345" y="265" width="5" height="2"/>
    <rect x="345" y="270" width="5" height="2"/>
    <rect x="345" y="275" width="5" height="2"/>
    <rect x="345" y="280" width="5" height="2"/>
    <rect x="345" y="285" width="5" height="2"/>
    
    <!-- Right pins -->
    <rect x="450" y="215" width="5" height="2"/>
    <rect x="450" y="220" width="5" height="2"/>
    <rect x="450" y="225" width="5" height="2"/>
    <rect x="450" y="230" width="5" height="2"/>
    <rect x="450" y="235" width="5" height="2"/>
    <rect x="450" y="240" width="5" height="2"/>
    <rect x="450" y="245" width="5" height="2"/>
    <rect x="450" y="250" width="5" height="2"/>
    <rect x="450" y="255" width="5" height="2"/>
    <rect x="450" y="260" width="5" height="2"/>
    <rect x="450" y="265" width="5" height="2"/>
    <rect x="450" y="270" width="5" height="2"/>
    <rect x="450" y="275" width="5" height="2"/>
    <rect x="450" y="280" width="5" height="2"/>
    <rect x="450" y="285" width="5" height="2"/>
  </g>
  
  <!-- CPU label -->
  <text x="400" y="255" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="10">ARM Cortex</text>
  
  <!-- Memory chips -->
  <rect x="270" y="170" width="60" height="30" fill="url(#chipGradient)" rx="3"/>
  <text x="300" y="190" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="8">DDR4</text>
  
  <rect x="470" y="170" width="60" height="30" fill="url(#chipGradient)" rx="3"/>
  <text x="500" y="190" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="8">eMMC</text>
  
  <!-- Connectors -->
  <!-- USB connectors -->
  <rect x="260" y="220" width="20" height="15" fill="#374151" rx="2"/>
  <rect x="263" y="223" width="14" height="9" fill="#1f2937"/>
  <text x="270" y="245" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">USB</text>
  
  <rect x="260" y="250" width="20" height="15" fill="#374151" rx="2"/>
  <rect x="263" y="253" width="14" height="9" fill="#1f2937"/>
  <text x="270" y="275" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">USB</text>
  
  <!-- Ethernet connector -->
  <rect x="520" y="220" width="25" height="20" fill="#374151" rx="2"/>
  <rect x="523" y="223" width="19" height="14" fill="#1f2937"/>
  <text x="532" y="250" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">ETH</text>
  
  <!-- HDMI connector -->
  <rect x="520" y="250" width="25" height="15" fill="#374151" rx="2"/>
  <rect x="523" y="253" width="19" height="9" fill="#1f2937"/>
  <text x="532" y="275" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">HDMI</text>
  
  <!-- GPIO pins -->
  <g fill="#fbbf24">
    <circle cx="280" cy="320" r="2"/>
    <circle cx="290" cy="320" r="2"/>
    <circle cx="300" cy="320" r="2"/>
    <circle cx="310" cy="320" r="2"/>
    <circle cx="320" cy="320" r="2"/>
    <circle cx="330" cy="320" r="2"/>
    <circle cx="340" cy="320" r="2"/>
    <circle cx="350" cy="320" r="2"/>
    <circle cx="360" cy="320" r="2"/>
    <circle cx="370" cy="320" r="2"/>
    <circle cx="380" cy="320" r="2"/>
    <circle cx="390" cy="320" r="2"/>
    <circle cx="400" cy="320" r="2"/>
    <circle cx="410" cy="320" r="2"/>
    <circle cx="420" cy="320" r="2"/>
    <circle cx="430" cy="320" r="2"/>
    <circle cx="440" cy="320" r="2"/>
    <circle cx="450" cy="320" r="2"/>
    <circle cx="460" cy="320" r="2"/>
    <circle cx="470" cy="320" r="2"/>
    <circle cx="480" cy="320" r="2"/>
    <circle cx="490" cy="320" r="2"/>
    <circle cx="500" cy="320" r="2"/>
    <circle cx="510" cy="320" r="2"/>
    <circle cx="520" cy="320" r="2"/>
  </g>
  <text x="400" y="340" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="8">GPIO Pins</text>
  
  <!-- Power LED -->
  <circle cx="530" cy="160" r="3" fill="#10b981">
    <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  <text x="530" y="175" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">PWR</text>
  
  <!-- Status LEDs -->
  <circle cx="270" cy="160" r="2" fill="#3b82f6">
    <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="280" cy="160" r="2" fill="#f59e0b">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Glow effects -->
  <circle cx="400" cy="250" r="120" fill="url(#glowEffect)" opacity="0.4"/>
  
  <!-- Data flow lines -->
  <g opacity="0.6">
    <line x1="300" y1="185" x2="360" y2="210" stroke="#3b82f6" stroke-width="2"/>
    <line x1="500" y1="185" x2="440" y2="210" stroke="#3b82f6" stroke-width="2"/>
    <line x1="400" y1="300" x2="400" y2="320" stroke="#fbbf24" stroke-width="2"/>
  </g>
  
  <!-- Advanced peripheral components -->
  <!-- WiFi/Bluetooth module -->
  <g transform="translate(280, 120)">
    <rect x="0" y="0" width="30" height="20" fill="url(#chipGradient)" rx="2"/>
    <text x="15" y="15" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">WiFi</text>
    <!-- Antenna traces -->
    <path d="M 5 5 Q 10 2 15 5 Q 20 2 25 5" stroke="#fbbf24" stroke-width="1" fill="none"/>
    <circle cx="15" cy="5" r="1" fill="#10b981">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Real-time clock -->
  <g transform="translate(480, 120)">
    <rect x="0" y="0" width="25" height="15" fill="url(#chipGradient)" rx="2"/>
    <text x="12" y="12" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="5">RTC</text>
    <circle cx="20" cy="5" r="1" fill="#3b82f6">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Security chip -->
  <g transform="translate(320, 120)">
    <rect x="0" y="0" width="25" height="15" fill="url(#chipGradient)" rx="2"/>
    <text x="12" y="12" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="5">SEC</text>
    <circle cx="5" cy="5" r="1" fill="#ef4444"/>
    <circle cx="20" cy="5" r="1" fill="#10b981"/>
  </g>

  <!-- Advanced I/O expansion -->
  <!-- SPI interface -->
  <g transform="translate(260, 280)">
    <rect x="0" y="0" width="40" height="8" fill="#374151" rx="1"/>
    <text x="20" y="15" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">SPI</text>
    <g fill="#fbbf24">
      <circle cx="5" cy="4" r="1"/>
      <circle cx="12" cy="4" r="1"/>
      <circle cx="19" cy="4" r="1"/>
      <circle cx="26" cy="4" r="1"/>
      <circle cx="33" cy="4" r="1"/>
    </g>
  </g>

  <!-- I2C interface -->
  <g transform="translate(460, 280)">
    <rect x="0" y="0" width="30" height="8" fill="#374151" rx="1"/>
    <text x="15" y="15" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">I2C</text>
    <g fill="#3b82f6">
      <circle cx="8" cy="4" r="1"/>
      <circle cx="15" cy="4" r="1"/>
      <circle cx="22" cy="4" r="1"/>
    </g>
  </g>

  <!-- CAN bus interface -->
  <g transform="translate(360, 280)">
    <rect x="0" y="0" width="35" height="8" fill="#374151" rx="1"/>
    <text x="17" y="15" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="6">CAN</text>
    <g fill="#10b981">
      <circle cx="8" cy="4" r="1"/>
      <circle cx="17" cy="4" r="1"/>
      <circle cx="26" cy="4" r="1"/>
    </g>
  </g>

  <!-- Heat dissipation elements -->
  <!-- Heat sink -->
  <g transform="translate(380, 180)">
    <rect x="0" y="0" width="40" height="15" fill="#9ca3af" rx="1"/>
    <!-- Fins -->
    <rect x="5" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="10" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="15" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="20" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="25" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="30" y="-5" width="2" height="25" fill="#6b7280"/>
    <rect x="35" y="-5" width="2" height="25" fill="#6b7280"/>
  </g>

  <!-- Thermal pads -->
  <rect x="365" y="195" width="15" height="10" fill="#f59e0b" opacity="0.7"/>
  <rect x="435" y="195" width="15" height="10" fill="#f59e0b" opacity="0.7"/>

  <!-- Advanced power management -->
  <!-- Power regulators -->
  <g transform="translate(270, 200)">
    <rect x="0" y="0" width="15" height="10" fill="url(#chipGradient)" rx="1"/>
    <text x="7" y="8" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="4">VR1</text>
    <circle cx="3" cy="3" r="0.8" fill="#10b981"/>
  </g>

  <g transform="translate(270, 220)">
    <rect x="0" y="0" width="15" height="10" fill="url(#chipGradient)" rx="1"/>
    <text x="7" y="8" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="4">VR2</text>
    <circle cx="3" cy="3" r="0.8" fill="#10b981"/>
  </g>

  <!-- Power monitoring IC -->
  <g transform="translate(270, 240)">
    <rect x="0" y="0" width="20" height="12" fill="url(#chipGradient)" rx="1"/>
    <text x="10" y="9" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="4">PWR</text>
    <circle cx="4" cy="4" r="0.8" fill="#3b82f6">
      <animate attributeName="opacity" values="1;0.5;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Crystal oscillators -->
  <g transform="translate(450, 200)">
    <rect x="0" y="0" width="12" height="8" fill="#d1d5db" rx="1"/>
    <text x="6" y="6" text-anchor="middle" fill="#374151" font-family="Arial" font-size="3">25M</text>
  </g>

  <g transform="translate(450, 220)">
    <rect x="0" y="0" width="10" height="6" fill="#d1d5db" rx="1"/>
    <text x="5" y="5" text-anchor="middle" fill="#374151" font-family="Arial" font-size="3">32K</text>
  </g>

  <!-- Advanced connectivity indicators -->
  <!-- 5G/LTE module -->
  <g transform="translate(520, 280)">
    <rect x="0" y="0" width="25" height="15" fill="url(#chipGradient)" rx="2"/>
    <text x="12" y="12" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="5">5G</text>
    <!-- Signal strength -->
    <rect x="3" y="10" width="1" height="2" fill="#10b981"/>
    <rect x="5" y="8" width="1" height="4" fill="#10b981"/>
    <rect x="7" y="6" width="1" height="6" fill="#10b981"/>
    <rect x="9" y="4" width="1" height="8" fill="#f59e0b"/>
    <rect x="11" y="2" width="1" height="10" fill="#ef4444"/>
  </g>

  <!-- Debug interfaces -->
  <!-- JTAG connector -->
  <g transform="translate(520, 200)">
    <rect x="0" y="0" width="20" height="10" fill="#374151" rx="1"/>
    <text x="10" y="18" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="5">JTAG</text>
    <g fill="#fbbf24">
      <circle cx="3" cy="3" r="0.8"/>
      <circle cx="7" cy="3" r="0.8"/>
      <circle cx="11" cy="3" r="0.8"/>
      <circle cx="15" cy="3" r="0.8"/>
      <circle cx="3" cy="7" r="0.8"/>
      <circle cx="7" cy="7" r="0.8"/>
      <circle cx="11" cy="7" r="0.8"/>
      <circle cx="15" cy="7" r="0.8"/>
    </g>
  </g>

  <!-- Environmental sensors -->
  <g transform="translate(520, 160)">
    <rect x="0" y="0" width="15" height="10" fill="url(#chipGradient)" rx="1"/>
    <text x="7" y="8" text-anchor="middle" fill="#e5e7eb" font-family="Arial" font-size="4">ENV</text>
    <circle cx="3" cy="3" r="0.8" fill="#3b82f6">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Advanced data flow visualization -->
  <g opacity="0.6">
    <!-- High-speed data buses -->
    <path d="M 300 185 Q 350 180 400 185" stroke="#3b82f6" stroke-width="3" fill="none"/>
    <path d="M 400 265 Q 450 260 500 265" stroke="#10b981" stroke-width="3" fill="none"/>

    <!-- Data packets -->
    <circle cx="320" cy="183" r="2" fill="#3b82f6">
      <animate attributeName="cx" values="300;400" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="420" cy="263" r="2" fill="#10b981">
      <animate attributeName="cx" values="400;500" dur="1.2s" repeatCount="indefinite"/>
    </circle>

    <!-- Clock signals -->
    <g stroke="#fbbf24" stroke-width="1" opacity="0.8">
      <line x1="450" y1="205" x2="480" y2="205">
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="0.5s" repeatCount="indefinite"/>
      </line>
      <line x1="450" y1="225" x2="480" y2="225">
        <animate attributeName="opacity" values="0.3;0.8;0.3" dur="0.5s" repeatCount="indefinite"/>
      </line>
    </g>
  </g>

  <!-- Floating tech elements -->
  <g opacity="0.4">
    <rect x="100" y="80" width="40" height="20" fill="#3b82f6" rx="2"/>
    <text x="120" y="95" text-anchor="middle" fill="white" font-family="Arial" font-size="8">IoT</text>

    <rect x="650" y="120" width="50" height="20" fill="#10b981" rx="2"/>
    <text x="675" y="135" text-anchor="middle" fill="white" font-family="Arial" font-size="8">AI/ML</text>

    <rect x="80" y="300" width="60" height="20" fill="#f59e0b" rx="2"/>
    <text x="110" y="315" text-anchor="middle" fill="white" font-family="Arial" font-size="8">Industry 4.0</text>

    <rect x="620" y="320" width="50" height="20" fill="#ef4444" rx="2"/>
    <text x="645" y="335" text-anchor="middle" fill="white" font-family="Arial" font-size="8">Edge</text>

    <!-- Additional tech badges -->
    <rect x="50" y="150" width="45" height="18" fill="#7c3aed" rx="2"/>
    <text x="72" y="164" text-anchor="middle" fill="white" font-family="Arial" font-size="7">Neural</text>

    <rect x="680" y="280" width="40" height="18" fill="#059669" rx="2"/>
    <text x="700" y="294" text-anchor="middle" fill="white" font-family="Arial" font-size="7">5G</text>

    <rect x="150" y="350" width="50" height="18" fill="#dc2626" rx="2"/>
    <text x="175" y="364" text-anchor="middle" fill="white" font-family="Arial" font-size="7">Security</text>

    <rect x="600" y="50" width="45" height="18" fill="#0891b2" rx="2"/>
    <text x="622" y="64" text-anchor="middle" fill="white" font-family="Arial" font-size="7">Cloud</text>
  </g>

  <!-- Performance indicators -->
  <g transform="translate(50, 50)" opacity="0.8">
    <rect x="0" y="0" width="100" height="60" fill="url(#cameraGradient)" rx="5"/>
    <text x="50" y="20" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="10">Performance</text>
    <text x="10" y="35" fill="#10b981" font-family="Arial" font-size="7">CPU: ARM Cortex-A78</text>
    <text x="10" y="45" fill="#f59e0b" font-family="Arial" font-size="7">Freq: 2.4GHz</text>
    <text x="10" y="55" fill="#ef4444" font-family="Arial" font-size="7">Cores: 8 (4+4)</text>
  </g>
</svg>
