<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="sunsetSky" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c2d12;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="sunGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f97316;stop-opacity:0" />
    </radialGradient>
    <linearGradient id="towerGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#111827;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="800" height="400" fill="url(#sunsetSky)"/>
  
  <!-- Sun -->
  <circle cx="650" cy="120" r="60" fill="url(#sunGlow)"/>
  <circle cx="650" cy="120" r="40" fill="#fbbf24"/>
  
  <!-- Ground -->
  <rect x="0" y="350" width="800" height="50" fill="#1f2937"/>
  
  <!-- Power transmission towers -->
  <!-- Tower 1 -->
  <g transform="translate(150, 200)">
    <!-- Main structure -->
    <polygon points="0,150 -15,0 15,0" fill="url(#towerGradient)" stroke="#4b5563" stroke-width="1"/>
    <!-- Cross beams -->
    <line x1="-12" y1="30" x2="12" y2="30" stroke="#4b5563" stroke-width="2"/>
    <line x1="-10" y1="60" x2="10" y2="60" stroke="#4b5563" stroke-width="2"/>
    <line x1="-8" y1="90" x2="8" y2="90" stroke="#4b5563" stroke-width="2"/>
    <line x1="-6" y1="120" x2="6" y2="120" stroke="#4b5563" stroke-width="2"/>
    <!-- Arms -->
    <line x1="-25" y1="40" x2="25" y2="40" stroke="#4b5563" stroke-width="3"/>
    <line x1="-20" y1="80" x2="20" y2="80" stroke="#4b5563" stroke-width="3"/>
    <!-- Insulators -->
    <circle cx="-25" cy="40" r="2" fill="#6b7280"/>
    <circle cx="25" cy="40" r="2" fill="#6b7280"/>
    <circle cx="-20" cy="80" r="2" fill="#6b7280"/>
    <circle cx="20" cy="80" r="2" fill="#6b7280"/>
  </g>
  
  <!-- Tower 2 -->
  <g transform="translate(350, 180)">
    <polygon points="0,170 -18,0 18,0" fill="url(#towerGradient)" stroke="#4b5563" stroke-width="1"/>
    <line x1="-15" y1="35" x2="15" y2="35" stroke="#4b5563" stroke-width="2"/>
    <line x1="-12" y1="70" x2="12" y2="70" stroke="#4b5563" stroke-width="2"/>
    <line x1="-10" y1="105" x2="10" y2="105" stroke="#4b5563" stroke-width="2"/>
    <line x1="-8" y1="140" x2="8" y2="140" stroke="#4b5563" stroke-width="2"/>
    <line x1="-30" y1="50" x2="30" y2="50" stroke="#4b5563" stroke-width="3"/>
    <line x1="-25" y1="90" x2="25" y2="90" stroke="#4b5563" stroke-width="3"/>
    <line x1="-20" y1="130" x2="20" y2="130" stroke="#4b5563" stroke-width="3"/>
    <circle cx="-30" cy="50" r="2" fill="#6b7280"/>
    <circle cx="30" cy="50" r="2" fill="#6b7280"/>
    <circle cx="-25" cy="90" r="2" fill="#6b7280"/>
    <circle cx="25" cy="90" r="2" fill="#6b7280"/>
    <circle cx="-20" cy="130" r="2" fill="#6b7280"/>
    <circle cx="20" cy="130" r="2" fill="#6b7280"/>
  </g>
  
  <!-- Tower 3 -->
  <g transform="translate(550, 220)">
    <polygon points="0,130 -12,0 12,0" fill="url(#towerGradient)" stroke="#4b5563" stroke-width="1"/>
    <line x1="-10" y1="25" x2="10" y2="25" stroke="#4b5563" stroke-width="2"/>
    <line x1="-8" y1="50" x2="8" y2="50" stroke="#4b5563" stroke-width="2"/>
    <line x1="-6" y1="75" x2="6" y2="75" stroke="#4b5563" stroke-width="2"/>
    <line x1="-4" y1="100" x2="4" y2="100" stroke="#4b5563" stroke-width="2"/>
    <line x1="-20" y1="35" x2="20" y2="35" stroke="#4b5563" stroke-width="3"/>
    <line x1="-15" y1="65" x2="15" y2="65" stroke="#4b5563" stroke-width="3"/>
    <circle cx="-20" cy="35" r="2" fill="#6b7280"/>
    <circle cx="20" cy="35" r="2" fill="#6b7280"/>
    <circle cx="-15" cy="65" r="2" fill="#6b7280"/>
    <circle cx="15" cy="65" r="2" fill="#6b7280"/>
  </g>
  
  <!-- Power lines -->
  <path d="M 125 240 Q 250 250 320 230" stroke="#374151" stroke-width="2" fill="none"/>
  <path d="M 125 280 Q 250 290 320 270" stroke="#374151" stroke-width="2" fill="none"/>
  <path d="M 125 320 Q 250 330 320 310" stroke="#374151" stroke-width="2" fill="none"/>
  
  <path d="M 320 230 Q 450 240 530 255" stroke="#374151" stroke-width="2" fill="none"/>
  <path d="M 320 270 Q 450 280 530 285" stroke="#374151" stroke-width="2" fill="none"/>
  <path d="M 320 310 Q 450 320 530 315" stroke="#374151" stroke-width="2" fill="none"/>
  
  <path d="M 530 255 Q 650 265 750 270" stroke="#374151" stroke-width="2" fill="none"/>
  <path d="M 530 285 Q 650 295 750 300" stroke="#374151" stroke-width="2" fill="none"/>
  
  <!-- Electrical sparks/energy effects -->
  <circle cx="125" cy="240" r="1" fill="#fbbf24" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="230" r="1" fill="#fbbf24" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="530" cy="255" r="1" fill="#fbbf24" opacity="0.7">
    <animate attributeName="opacity" values="0.7;0.3;0.7" dur="0.8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Clouds -->
  <ellipse cx="200" cy="80" rx="40" ry="15" fill="#f97316" opacity="0.6"/>
  <ellipse cx="450" cy="60" rx="60" ry="20" fill="#f97316" opacity="0.5"/>
  <ellipse cx="700" cy="90" rx="35" ry="12" fill="#f97316" opacity="0.4"/>
  
  <!-- Birds -->
  <path d="M 100 100 Q 105 95 110 100 Q 115 95 120 100" stroke="#374151" stroke-width="1" fill="none"/>
  <path d="M 180 120 Q 185 115 190 120 Q 195 115 200 120" stroke="#374151" stroke-width="1" fill="none"/>
  <path d="M 250 90 Q 255 85 260 90 Q 265 85 270 90" stroke="#374151" stroke-width="1" fill="none"/>
</svg>
