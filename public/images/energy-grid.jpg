<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080" class="w-full h-full">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#50C878;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1920" height="1080" fill="url(#skyGradient)"/>
  
  <!-- Mountains in background -->
  <polygon points="0,800 300,600 600,700 900,550 1200,650 1500,500 1920,600 1920,1080 0,1080" fill="#2C5F2D" opacity="0.3"/>
  
  <!-- Power transmission towers -->
  <g transform="translate(200,400)">
    <!-- Tower 1 -->
    <g stroke="#333" stroke-width="3" fill="none">
      <line x1="0" y1="0" x2="0" y2="300"/>
      <line x1="-30" y1="50" x2="30" y2="50"/>
      <line x1="-25" y1="100" x2="25" y2="100"/>
      <line x1="-20" y1="150" x2="20" y2="150"/>
      <line x1="-15" y1="200" x2="15" y2="200"/>
      <line x1="-10" y1="250" x2="10" y2="250"/>
      <!-- Cross beams -->
      <line x1="-30" y1="50" x2="0" y2="0"/>
      <line x1="30" y1="50" x2="0" y2="0"/>
      <line x1="-25" y1="100" x2="-30" y2="50"/>
      <line x1="25" y1="100" x2="30" y2="50"/>
    </g>
  </g>
  
  <g transform="translate(600,350)">
    <!-- Tower 2 -->
    <g stroke="#333" stroke-width="3" fill="none">
      <line x1="0" y1="0" x2="0" y2="350"/>
      <line x1="-35" y1="60" x2="35" y2="60"/>
      <line x1="-30" y1="120" x2="30" y2="120"/>
      <line x1="-25" y1="180" x2="25" y2="180"/>
      <line x1="-20" y1="240" x2="20" y2="240"/>
      <line x1="-15" y1="300" x2="15" y2="300"/>
      <!-- Cross beams -->
      <line x1="-35" y1="60" x2="0" y2="0"/>
      <line x1="35" y1="60" x2="0" y2="0"/>
    </g>
  </g>
  
  <g transform="translate(1000,380)">
    <!-- Tower 3 -->
    <g stroke="#333" stroke-width="3" fill="none">
      <line x1="0" y1="0" x2="0" y2="320"/>
      <line x1="-32" y1="55" x2="32" y2="55"/>
      <line x1="-27" y1="110" x2="27" y2="110"/>
      <line x1="-22" y1="165" x2="22" y2="165"/>
      <line x1="-17" y1="220" x2="17" y2="220"/>
      <line x1="-12" y1="275" x2="12" y2="275"/>
    </g>
  </g>
  
  <!-- Power lines connecting towers -->
  <g stroke="#222" stroke-width="2" fill="none">
    <!-- High voltage lines -->
    <path d="M 170,450 Q 400,470 630,410" opacity="0.8"/>
    <path d="M 170,460 Q 400,480 630,420" opacity="0.8"/>
    <path d="M 170,470 Q 400,490 630,430" opacity="0.8"/>
    
    <path d="M 630,410 Q 800,430 1000,435" opacity="0.8"/>
    <path d="M 630,420 Q 800,440 1000,445" opacity="0.8"/>
    <path d="M 630,430 Q 800,450 1000,455" opacity="0.8"/>
    
    <path d="M 1000,435 Q 1200,440 1400,420" opacity="0.8"/>
    <path d="M 1000,445 Q 1200,450 1400,430" opacity="0.8"/>
    <path d="M 1000,455 Q 1200,460 1400,440" opacity="0.8"/>
  </g>
  
  <!-- Solar panels in foreground -->
  <g transform="translate(100,750)">
    <rect x="0" y="0" width="150" height="80" fill="#1a365d" rx="5"/>
    <g stroke="#2d5a87" stroke-width="1">
      <line x1="25" y1="0" x2="25" y2="80"/>
      <line x1="50" y1="0" x2="50" y2="80"/>
      <line x1="75" y1="0" x2="75" y2="80"/>
      <line x1="100" y1="0" x2="100" y2="80"/>
      <line x1="125" y1="0" x2="125" y2="80"/>
      <line x1="0" y1="20" x2="150" y2="20"/>
      <line x1="0" y1="40" x2="150" y2="40"/>
      <line x1="0" y1="60" x2="150" y2="60"/>
    </g>
  </g>
  
  <g transform="translate(300,770)">
    <rect x="0" y="0" width="150" height="80" fill="#1a365d" rx="5"/>
    <g stroke="#2d5a87" stroke-width="1">
      <line x1="25" y1="0" x2="25" y2="80"/>
      <line x1="50" y1="0" x2="50" y2="80"/>
      <line x1="75" y1="0" x2="75" y2="80"/>
      <line x1="100" y1="0" x2="100" y2="80"/>
      <line x1="125" y1="0" x2="125" y2="80"/>
      <line x1="0" y1="20" x2="150" y2="20"/>
      <line x1="0" y1="40" x2="150" y2="40"/>
      <line x1="0" y1="60" x2="150" y2="60"/>
    </g>
  </g>
  
  <!-- Wind turbines in distance -->
  <g transform="translate(1400,300)" opacity="0.6">
    <!-- Turbine 1 -->
    <line x1="0" y1="0" x2="0" y2="200" stroke="#666" stroke-width="4"/>
    <g transform="translate(0,50)">
      <circle cx="0" cy="0" r="8" fill="#444"/>
      <line x1="0" y1="0" x2="0" y2="-60" stroke="#fff" stroke-width="3"/>
      <line x1="0" y1="0" x2="52" y2="30" stroke="#fff" stroke-width="3"/>
      <line x1="0" y1="0" x2="-52" y2="30" stroke="#fff" stroke-width="3"/>
    </g>
  </g>
  
  <g transform="translate(1600,320)" opacity="0.5">
    <!-- Turbine 2 -->
    <line x1="0" y1="0" x2="0" y2="180" stroke="#666" stroke-width="3"/>
    <g transform="translate(0,40)">
      <circle cx="0" cy="0" r="6" fill="#444"/>
      <line x1="0" y1="0" x2="-30" y2="-45" stroke="#fff" stroke-width="2"/>
      <line x1="0" y1="0" x2="45" y2="15" stroke="#fff" stroke-width="2"/>
      <line x1="0" y1="0" x2="-15" y2="45" stroke="#fff" stroke-width="2"/>
    </g>
  </g>
  
  <!-- Energy flow visualization -->
  <g opacity="0.4">
    <circle cx="200" cy="500" r="3" fill="#FFD700">
      <animate attributeName="r" values="3;8;3" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="450" r="3" fill="#50C878">
      <animate attributeName="r" values="3;8;3" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1000" cy="480" r="3" fill="#4A90E2">
      <animate attributeName="r" values="3;8;3" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Clouds -->
  <g opacity="0.3">
    <ellipse cx="300" cy="150" rx="60" ry="30" fill="white"/>
    <ellipse cx="280" cy="140" rx="40" ry="25" fill="white"/>
    <ellipse cx="320" cy="140" rx="45" ry="28" fill="white"/>
    
    <ellipse cx="800" cy="100" rx="70" ry="35" fill="white"/>
    <ellipse cx="775" cy="90" rx="45" ry="30" fill="white"/>
    <ellipse cx="825" cy="90" rx="50" ry="32" fill="white"/>
    
    <ellipse cx="1500" cy="120" rx="55" ry="28" fill="white"/>
    <ellipse cx="1480" cy="110" rx="35" ry="22" fill="white"/>
    <ellipse cx="1520" cy="110" rx="40" ry="25" fill="white"/>
  </g>
</svg>