// Service Worker for Image Caching
const CACHE_NAME = 'Linnuo-images-v1'
const IMAGE_CACHE_NAME = 'Linnuo-images-cache-v1'

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
}

// Image file extensions to cache
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.svg', '.gif']

// Critical images to precache
const CRITICAL_IMAGES = [
  // '/images/products/sigma-main.jpg',
  // '/images/products/mu-main.jpg',
  // '/images/products/delta3-main.jpg'
]

// Install event - precache critical images
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(IMAGE_CACHE_NAME).then((cache) => {
      console.log('Precaching critical images')
      return cache.addAll(CRITICAL_IMAGES.map(url => new Request(url, { mode: 'no-cors' })))
    }).catch((error) => {
      console.warn('Failed to precache some images:', error)
    })
  )
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== IMAGE_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// Fetch event - handle image requests
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Only handle image requests
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request))
  }
})

// Check if request is for an image
function isImageRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname.toLowerCase()
  
  // Check for image extensions
  const hasImageExtension = IMAGE_EXTENSIONS.some(ext => pathname.endsWith(ext))
  
  // Check for Next.js optimized images
  const isNextImage = pathname.startsWith('/_next/image')
  
  // Check for image MIME types
  const acceptHeader = request.headers.get('accept') || ''
  const acceptsImages = acceptHeader.includes('image/')
  
  return hasImageExtension || isNextImage || acceptsImages
}

// Handle image requests with caching strategy
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE_NAME)
  const cachedResponse = await cache.match(request)

  // For critical images, use cache-first strategy
  if (isCriticalImage(request.url)) {
    if (cachedResponse) {
      return cachedResponse
    }
    
    try {
      const networkResponse = await fetch(request)
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone())
      }
      return networkResponse
    } catch (error) {
      console.warn('Failed to fetch critical image:', request.url)
      return cachedResponse || createFallbackResponse()
    }
  }

  // For other images, use stale-while-revalidate strategy
  if (cachedResponse) {
    // Serve from cache immediately
    fetch(request).then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone())
      }
    }).catch(() => {
      // Silently fail background updates
    })
    
    return cachedResponse
  }

  // No cache, fetch from network
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      // Cache successful responses
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.warn('Failed to fetch image:', request.url)
    return createFallbackResponse()
  }
}

// Check if image is critical
function isCriticalImage(url) {
  return CRITICAL_IMAGES.some(criticalUrl => url.includes(criticalUrl))
}

// Create fallback response for failed image loads
function createFallbackResponse() {
  // Return a simple 1x1 transparent pixel
  const fallbackImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+'
  
  return new Response(fallbackImage, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'no-cache'
    }
  })
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, payload } = event.data

  switch (type) {
    case 'PRELOAD_IMAGES':
      preloadImages(payload.urls)
      break
    case 'CLEAR_IMAGE_CACHE':
      clearImageCache()
      break
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage(status)
      })
      break
  }
})

// Preload images into cache
async function preloadImages(urls) {
  const cache = await caches.open(IMAGE_CACHE_NAME)
  
  const preloadPromises = urls.map(async (url) => {
    try {
      const response = await fetch(url, { mode: 'no-cors' })
      if (response.ok) {
        await cache.put(url, response)
      }
    } catch (error) {
      console.warn('Failed to preload image:', url)
    }
  })

  await Promise.allSettled(preloadPromises)
}

// Clear image cache
async function clearImageCache() {
  await caches.delete(IMAGE_CACHE_NAME)
  console.log('Image cache cleared')
}

// Get cache status
async function getCacheStatus() {
  const cache = await caches.open(IMAGE_CACHE_NAME)
  const keys = await cache.keys()
  
  return {
    cacheSize: keys.length,
    cachedUrls: keys.map(request => request.url)
  }
}
