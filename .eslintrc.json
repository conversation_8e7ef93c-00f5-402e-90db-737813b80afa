{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": null}, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:testing-library/react", "plugin:jest-dom/recommended"], "plugins": ["@typescript-eslint", "testing-library", "jest-dom"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["error", "always"], "curly": ["error", "all"]}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*"], "env": {"jest": true}}]}