/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基础配置
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // 构建配置 - 禁用可能导致问题的功能
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // 完全禁用实验性功能以避免构建问题
  experimental: {},

  // 禁用输出文件追踪
  output: undefined,

  // 简化的图片配置
  images: {
    unoptimized: false,
    domains: [
      'vivid-pleasure-04cb3dbd82.strapiapp.com',
      'vivid-pleasure-04cb3dbd82.media.strapiapp.com',
    ],
    formats: ['image/webp'],
  },

  // 基础优化
  compress: true,
  poweredByHeader: false,
  reactStrictMode: true,

  // 简化的 webpack 配置
  webpack: (config, { isServer }) => {
    // 最小化配置以避免构建问题
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        path: false,
      }
    }
    
    // 禁用可能导致问题的优化
    config.optimization = {
      ...config.optimization,
      splitChunks: false,
    }
    
    return config
  },
}

module.exports = nextConfig
