// 数据源配置
export const dataConfig = {
  // 是否使用本地模拟数据（true）还是Strapi API（false）
  useLocalData: false,

  // Strapi配置
  strapi: {
    url: 'http://38.175.108.33:1337',
    enableCache: true,
    cacheTimeout: 5 * 60 * 1000, // 5分钟
  },
  
  // 本地数据配置
  local: {
    // 数据文件路径
    dataPath: './src/data',
    // 是否启用调试日志
    debug: true
  }
}

// 获取当前数据源类型
export function getDataSourceType(): 'local' | 'strapi' {
  return dataConfig.useLocalData ? 'local' : 'strapi'
}

// 切换数据源
export function switchDataSource(useLocal: boolean) {
  dataConfig.useLocalData = useLocal
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 数据源已切换到: ${useLocal ? '本地数据' : 'Strapi API'}`)
  }
}

export default dataConfig
