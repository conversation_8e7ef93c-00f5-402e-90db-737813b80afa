// 统一数据获取器 - API-Only模式，只有轮播图有默认后备
import { getProducts as getApiProducts } from '@/lib/api/products'
import { getNewsArticles as getApiNewsArticles, getNewsArticleBySlug as getApiNewsArticleBySlug } from '@/lib/api/news'
import { getHeroSlides as getApiHeroSlides } from '@/lib/api/hero-slides'
import { getProductCategories as getApiProductCategories } from '@/lib/api/product-categories'
import type { Language } from '@/lib/api-with-locale'
import type { Product } from '@/types/product'
import type { NewsArticle } from '@/lib/api/news'
import type { HeroSlide } from '@/lib/api/hero-slides'
import type { ProductCategory } from '@/lib/api/product-categories'

// 重新导出类型以便其他文件使用
export type { HeroSlide } from '@/lib/api/hero-slides'
export type { ProductCategory } from '@/lib/api/product-categories'

// 产品数据获取 - 只使用API，失败时返回空数组
export async function getProducts(language: Language = 'en'): Promise<Product[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取产品数据，语言:', language)
      console.log('🔧 API配置检查...')
    }
    
    // 检查API配置
    const { getApiConfig } = await import('@/lib/api-config')
    const config = getApiConfig()
    if (process.env.NODE_ENV === 'development') {
      console.log('📋 API配置:', {
        enableStrapi: config.enableStrapi,
        strapiUrl: config.strapiUrl,
        hasApiToken: !!config.apiToken,
        deploymentTarget: config.deploymentTarget
      })
    }
    
    // 使用正确的语言参数获取产品数据
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 尝试获取产品数据，语言:', language)
    }
    const products = await getApiProducts({ language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 产品数据获取成功:', products.length, '个产品')
      console.log('📦 产品详情:', products)
    }
    return products
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 产品数据获取失败:', error)
      console.error('❌ 错误详情:', error.message, error.stack)
    }
    return []
  }
}

// 根据slug获取产品详情 - 只使用API，失败时返回null
export async function getProductBySlug(slug: string, language: Language = 'en'): Promise<Product | null> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取产品详情:', slug, '语言:', language)
    }
    const { getProductBySlug: getApiProductBySlug } = await import('@/lib/api/products')
    const product = await getApiProductBySlug(slug, language)
    if (product) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 产品详情获取成功:', product.name)
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ 产品未找到:', slug)
      }
    }
    return product
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 产品详情获取失败:', error)
    }
    return null
  }
}

// 新闻数据获取 - 只使用API，失败时返回空数组
export async function getNewsArticles(language: Language = 'en'): Promise<NewsArticle[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取新闻数据，语言:', language)
    }
    const articles = await getApiNewsArticles({ language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 新闻数据获取成功:', articles.length, '篇文章')
    }
    return articles
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 新闻数据获取失败:', error)
    }
    return []
  }
}

// 根据slug获取新闻详情 - 只使用API，失败时返回null
export async function getNewsArticleBySlug(slug: string, language: Language = 'en'): Promise<NewsArticle | null> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取新闻详情:', slug, '语言:', language)
    }
    const article = await getApiNewsArticleBySlug(slug, language)
    if (article) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 新闻详情获取成功:', article.title)
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ 新闻未找到:', slug)
      }
    }
    return article
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 新闻详情获取失败:', error)
    }
    return null
  }
}

// 轮播图数据获取 - 优先使用API，失败时使用默认图片
export async function getHeroSlides(language: Language = 'en'): Promise<HeroSlide[]> {
  try {
    // 确保传递language参数给API调用
    const slides = await getApiHeroSlides(language)
    
    if (slides && slides.length > 0) {
      return slides
    } else {
      const defaultSlides = getDefaultHeroSlides(language)
      return defaultSlides
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('轮播图API获取失败，使用默认图片:', error)
    }
    const defaultSlides = getDefaultHeroSlides(language)
    return defaultSlides
  }
}

// 获取默认轮播图 - 轮播图的特殊后备方案
export function getDefaultHeroSlides(language: Language = 'en'): HeroSlide[] {
  const isEnglish = language === 'en'
  const imagePath = `/images/hero-slide/${language}`
  
  if (isEnglish) {
    return [
      {
        id: 'default-1',
        title: 'Linnuo Product Showcase',
        subtitle: 'Powerful Single Board Computer Solutions',
        description: 'Explore our innovative product series and provide powerful computing capabilities for your projects',
        image: `${imagePath}/lbt01-en.jpg`,
        ctaText: 'Learn More',
        ctaLink: '/products',
        order: 1,
        active: true
      },
      {
        id: 'default-2',
        title: 'Linnuo Technology Innovation',
        subtitle: 'Leading Future Computing Technology',
        description: 'Experience the latest technological innovations and create next-generation smart devices',
        image: `${imagePath}/lbt02-en.jpg`,
        ctaText: 'View Products',
        ctaLink: '/products',
        order: 2,
        active: true
      }
    ]
  } else {
    return [
      {
        id: 'default-1',
        title: 'Linnuo 产品展示',
        subtitle: '强大的单板计算机解决方案',
        description: '探索我们的创新产品系列，为您的项目提供强大的计算能力',
        image: `${imagePath}/lbt01.jpg`,
        ctaText: '了解更多',
        ctaLink: '/products',
        order: 1,
        active: true
      },
      {
        id: 'default-2',
        title: 'Linnuo 技术创新',
        subtitle: '引领未来的计算技术',
        description: '体验最新的技术创新，打造下一代智能设备',
        image: `${imagePath}/lbt02.jpg`,
        ctaText: '查看产品',
        ctaLink: '/products',
        order: 2,
        active: true
      }
    ]
  }
}

// 获取特色产品 - 只使用API，失败时返回空数组
export async function getFeaturedProducts(language: Language = 'en'): Promise<Product[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取特色产品，语言:', language)
    }
    const products = await getApiProducts({ featured: true, limit: 6, language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 特色产品获取成功:', products.length, '个产品')
    }
    return products
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 特色产品获取失败:', error)
    }
    return []
  }
}

// 获取特色新闻 - 只使用API，失败时返回空数组
export async function getFeaturedNews(language: Language = 'en'): Promise<NewsArticle[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取特色新闻，语言:', language)
    }
    const articles = await getApiNewsArticles({ featured: true, limit: 3, language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 特色新闻获取成功:', articles.length, '篇文章')
    }
    return articles
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 特色新闻获取失败:', error)
    }
    return []
  }
}

// 根据分类获取产品 - 只使用API，失败时返回空数组
export async function getProductsByCategory(category: string, language: Language = 'en'): Promise<Product[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取分类产品，分类:', category, '语言:', language)
    }
    const products = await getApiProducts({ category, language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 分类产品获取成功:', products.length, '个产品')
    }
    return products
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 分类产品获取失败:', error)
    }
    return []
  }
}

// 获取产品分类 - 只使用API，失败时返回空数组
export async function getProductCategories(language: Language = 'en'): Promise<ProductCategory[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 从Strapi API获取产品分类，语言:', language)
    }
    const categories = await getApiProductCategories(language)
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 产品分类获取成功:', categories.length, '个分类')
    }
    return categories
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 产品分类获取失败:', error)
    }
    return []
  }
}