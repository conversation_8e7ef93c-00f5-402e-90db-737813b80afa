import { useQuery } from '@tanstack/react-query'
import {
  getProducts,
  getProductBySlug,
  getProductsByCategory,
  getNewsArticles,
  getNewsArticleBySlug,
  getHeroSlides,
  getFeaturedProducts,
  getFeaturedNews
} from '@/data/unified-data-fetcher'
import { getCompanyInfo, getAllCompanies } from '@/lib/api/company'
import { useLanguage } from '@/contexts/simple-language-context'

// 产品相关 hooks - 优化缓存策略
export function useProducts() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['products', language],
    queryFn: () => getProducts(language),
    staleTime: 10 * 60 * 1000, // 10分钟缓存，产品数据变化不频繁
    gcTime: 60 * 60 * 1000, // 1小时垃圾回收
    refetchOnMount: false, // 如果有缓存数据，不重新获取
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取
  })
}

export function useFeaturedProducts() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['featured-products', language],
    queryFn: () => getFeaturedProducts(language),
    staleTime: 15 * 60 * 1000, // 15分钟缓存，特色产品更稳定
    gcTime: 60 * 60 * 1000, // 1小时垃圾回收
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

export function useProduct(slug: string) {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['products', slug, language],
    queryFn: () => getProductBySlug(slug, language),
    enabled: !!slug,
    staleTime: 20 * 60 * 1000, // 20分钟缓存，产品详情变化很少
    gcTime: 2 * 60 * 60 * 1000, // 2小时垃圾回收
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

export function useProductsByCategory(category: string) {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['products', 'category', category, language],
    queryFn: () => getProductsByCategory(category, language),
    enabled: !!category,
  })
}

// 轮播图相关 hooks
export function useHeroSlides() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['hero-slides', language],
    queryFn: () => getHeroSlides(language),
    staleTime: 1000 * 60 * 10, // 10分钟缓存
  })
}

export function useActiveSlides() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['hero-slides', 'active', language],
    queryFn: async () => {
      const slides = await getHeroSlides(language)
      return slides.filter(slide => slide.active)
    },
    staleTime: 1000 * 60 * 10, // 10分钟缓存
  })
}

// 新闻相关 hooks - 优化缓存策略
export function useNewsArticles(options?: { featured?: boolean; limit?: number }) {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['news', options, language],
    queryFn: () => getNewsArticles(language),
    staleTime: 5 * 60 * 1000, // 5分钟缓存，新闻更新较频繁
    gcTime: 30 * 60 * 1000, // 30分钟垃圾回收
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

export function useFeaturedNews() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['news', 'featured', language],
    queryFn: () => getFeaturedNews(language),
    staleTime: 10 * 60 * 1000, // 10分钟缓存，特色新闻相对稳定
    gcTime: 60 * 60 * 1000, // 1小时垃圾回收
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

export function useNewsArticle(slug: string) {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['news', slug, language],
    queryFn: () => getNewsArticleBySlug(slug, language),
    enabled: !!slug,
  })
}

// 公司信息相关 hooks
export function useCompanyInfo() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['company', 'info', language],
    queryFn: () => getCompanyInfo(language),
    staleTime: 1000 * 60 * 30, // 30分钟缓存
    gcTime: 1000 * 60 * 60, // 1小时垃圾回收
  })
}

export function useAllCompanies() {
  const { language } = useLanguage()

  return useQuery({
    queryKey: ['companies', language],
    queryFn: () => getAllCompanies(language),
    staleTime: 1000 * 60 * 30, // 30分钟缓存
  })
}
