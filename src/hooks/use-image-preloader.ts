'use client'

import { useEffect, useCallback, useRef } from 'react'

interface PreloadOptions {
  priority?: 'high' | 'low'
  crossOrigin?: 'anonymous' | 'use-credentials'
  timeout?: number
}

interface PreloadResult {
  preload: (src: string, options?: PreloadOptions) => Promise<void>
  preloadMultiple: (sources: string[], options?: PreloadOptions) => Promise<void>
  isPreloaded: (src: string) => boolean
  clearCache: () => void
}

export function useImagePreloader(): PreloadResult {
  const preloadedImages = useRef<Set<string>>(new Set())
  const preloadPromises = useRef<Map<string, Promise<void>>>(new Map())

  // Preload a single image
  const preload = useCallback(async (src: string, options: PreloadOptions = {}): Promise<void> => {
    if (preloadedImages.current.has(src)) {
      return Promise.resolve()
    }

    // Return existing promise if already preloading
    if (preloadPromises.current.has(src)) {
      return preloadPromises.current.get(src)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      const img = new Image()
      const { timeout = 10000, crossOrigin } = options

      // Set up timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Image preload timeout: ${src}`))
      }, timeout)

      img.onload = () => {
        clearTimeout(timeoutId)
        preloadedImages.current.add(src)
        preloadPromises.current.delete(src)
        resolve()
      }

      img.onerror = () => {
        clearTimeout(timeoutId)
        preloadPromises.current.delete(src)
        reject(new Error(`Failed to preload image: ${src}`))
      }

      if (crossOrigin) {
        img.crossOrigin = crossOrigin
      }

      img.src = src
    })

    preloadPromises.current.set(src, promise)
    return promise
  }, [])

  // Preload multiple images
  const preloadMultiple = useCallback(async (
    sources: string[], 
    options: PreloadOptions = {}
  ): Promise<void> => {
    const promises = sources.map(src => preload(src, options))
    await Promise.allSettled(promises)
  }, [preload])

  // Check if image is preloaded
  const isPreloaded = useCallback((src: string): boolean => {
    return preloadedImages.current.has(src)
  }, [])

  // Clear preload cache
  const clearCache = useCallback(() => {
    preloadedImages.current.clear()
    preloadPromises.current.clear()
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearCache()
    }
  }, [clearCache])

  return {
    preload,
    preloadMultiple,
    isPreloaded,
    clearCache
  }
}

// Hook for preloading critical images on page load
export function useCriticalImagePreloader(images: string[]) {
  const { preloadMultiple } = useImagePreloader()

  useEffect(() => {
    if (images.length > 0) {
      preloadMultiple(images, { priority: 'high' })
        .catch(error => {
          // Failed to preload critical images - continue without preloading
        })
    }
  }, [images, preloadMultiple])
}

// Hook for preloading images on hover
export function useHoverPreloader() {
  const { preload } = useImagePreloader()

  const preloadOnHover = useCallback((src: string) => {
    return {
      onMouseEnter: () => {
        preload(src, { priority: 'low' }).catch(() => {
          // Silently fail for hover preloads
        })
      }
    }
  }, [preload])

  return { preloadOnHover }
}
