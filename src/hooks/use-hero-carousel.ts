import { useState, useEffect, useCallback } from 'react'
import { getHeroSlides, type HeroSlide } from '@/data/unified-data-fetcher'

interface UseHeroCarouselOptions {
  autoPlayInterval?: number
  autoPlay?: boolean
}

interface UseHeroCarouselReturn {
  slides: HeroSlide[]
  currentSlide: number
  isAutoPlaying: boolean
  isLoading: boolean
  error: string | null
  nextSlide: () => void
  prevSlide: () => void
  goToSlide: (index: number) => void
  setAutoPlaying: (playing: boolean) => void
  currentSlideData: HeroSlide | null
}

export function useHeroCarousel(options: UseHeroCarouselOptions = {}): UseHeroCarouselReturn {
  const { autoPlayInterval = 5000, autoPlay = true } = options

  const [slides, setSlides] = useState<HeroSlide[]>([])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载幻灯片数据
  useEffect(() => {
    const loadSlides = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const allSlides = await getHeroSlides()
        const activeSlides = allSlides.filter(slide => slide.active !== false)
        if (activeSlides.length === 0) {
          throw new Error('No active slides found')
        }
        setSlides(activeSlides)
      } catch (err) {
        // Handle hero slides loading error
        setError(err instanceof Error ? err.message : 'Failed to load slides')
        // 使用默认数据作为后备
        const defaultSlides = await getHeroSlides()
        setSlides(defaultSlides)
      } finally {
        setIsLoading(false)
      }
    }

    loadSlides()
  }, [])

  // 自动播放功能
  useEffect(() => {
    if (!isAutoPlaying || slides.length === 0) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, slides.length, autoPlayInterval])

  // 重置当前幻灯片索引当幻灯片数据改变时
  useEffect(() => {
    if (slides.length > 0 && currentSlide >= slides.length) {
      setCurrentSlide(0)
    }
  }, [slides.length, currentSlide])

  // 切换到下一张
  const nextSlide = useCallback(() => {
    if (slides.length === 0) return
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [slides.length])

  // 切换到上一张
  const prevSlide = useCallback(() => {
    if (slides.length === 0) return
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }, [slides.length])

  // 切换到指定幻灯片
  const goToSlide = useCallback((index: number) => {
    if (index >= 0 && index < slides.length) {
      setCurrentSlide(index)
    }
  }, [slides.length])

  // 设置自动播放状态
  const setAutoPlaying = useCallback((playing: boolean) => {
    setIsAutoPlaying(playing)
  }, [])

  // 获取当前幻灯片数据
  const currentSlideData = slides.length > 0 ? slides[currentSlide] : null

  return {
    slides,
    currentSlide,
    isAutoPlaying,
    isLoading,
    error,
    nextSlide,
    prevSlide,
    goToSlide,
    setAutoPlaying,
    currentSlideData
  }
}

// 键盘导航Hook
export function useCarouselKeyboardNavigation(
  nextSlide: () => void,
  prevSlide: () => void,
  setAutoPlaying: (playing: boolean) => void,
  isAutoPlaying: boolean
) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          prevSlide()
          setAutoPlaying(false) // 用户交互时暂停自动播放
          break
        case 'ArrowRight':
          event.preventDefault()
          nextSlide()
          setAutoPlaying(false)
          break
        case ' ': // 空格键暂停/恢复自动播放
          event.preventDefault()
          setAutoPlaying(!isAutoPlaying)
          break
        case 'Escape':
          event.preventDefault()
          setAutoPlaying(false)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [nextSlide, prevSlide, setAutoPlaying, isAutoPlaying])
}

// 触摸手势Hook（用于移动设备）
export function useCarouselTouchGestures(
  nextSlide: () => void,
  prevSlide: () => void,
  setAutoPlaying: (playing: boolean) => void
) {
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const minSwipeDistance = 50

  const onTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }, [])

  const onTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }, [])

  const onTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      nextSlide()
      setAutoPlaying(false)
    } else if (isRightSwipe) {
      prevSlide()
      setAutoPlaying(false)
    }
  }, [touchStart, touchEnd, nextSlide, prevSlide, setAutoPlaying, minSwipeDistance])

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd
  }
}

// 可见性API Hook - 当页面不可见时暂停自动播放
export function useCarouselVisibility(setAutoPlaying: (playing: boolean) => void) {
  const [wasAutoPlaying, setWasAutoPlaying] = useState(true)

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        setWasAutoPlaying(true) // 假设之前是自动播放的
        setAutoPlaying(false)
      } else {
        if (wasAutoPlaying) {
          setAutoPlaying(true)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [setAutoPlaying, wasAutoPlaying])
}
