'use client'

import { useEffect, useCallback, useState } from 'react'

interface ServiceWorkerState {
  isSupported: boolean
  isRegistered: boolean
  isInstalling: boolean
  isWaiting: boolean
  isActive: boolean
  error: string | null
}

export function useServiceWorker() {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: false,
    isRegistered: false,
    isInstalling: false,
    isWaiting: false,
    isActive: false,
    error: null
  })

  // Register service worker
  const register = useCallback(async () => {
    if (!('serviceWorker' in navigator)) {
      setState(prev => ({ ...prev, error: 'Service Worker not supported' }))
      return
    }

    try {
      setState(prev => ({ ...prev, isSupported: true }))
      
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      setState(prev => ({ ...prev, isRegistered: true }))

      // Handle installation states
      if (registration.installing) {
        setState(prev => ({ ...prev, isInstalling: true }))
        registration.installing.addEventListener('statechange', () => {
          if (registration.installing?.state === 'installed') {
            setState(prev => ({ ...prev, isInstalling: false, isWaiting: true }))
          }
        })
      }

      if (registration.waiting) {
        setState(prev => ({ ...prev, isWaiting: true }))
      }

      if (registration.active) {
        setState(prev => ({ ...prev, isActive: true }))
      }

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          setState(prev => ({ ...prev, isInstalling: true }))
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed') {
              setState(prev => ({ ...prev, isInstalling: false, isWaiting: true }))
            }
          })
        }
      })

      if (process.env.NODE_ENV === 'development') {
        console.log('Service Worker registered successfully')
      }
      return registration

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Service Worker registration failed:', error)
      }
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Registration failed' 
      }))
    }
  }, [])

  // Preload images using service worker
  const preloadImages = useCallback(async (urls: string[]) => {
    if (!navigator.serviceWorker.controller) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Service Worker not active, cannot preload images')
      }
      return
    }

    navigator.serviceWorker.controller.postMessage({
      type: 'PRELOAD_IMAGES',
      payload: { urls }
    })
  }, [])

  // Clear image cache
  const clearImageCache = useCallback(async () => {
    if (!navigator.serviceWorker.controller) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Service Worker not active, cannot clear cache')
      }
      return
    }

    navigator.serviceWorker.controller.postMessage({
      type: 'CLEAR_IMAGE_CACHE'
    })
  }, [])

  // Get cache status
  const getCacheStatus = useCallback(async (): Promise<any> => {
    if (!navigator.serviceWorker.controller) {
      return { cacheSize: 0, cachedUrls: [] }
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel()
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data)
      }

      navigator.serviceWorker.controller.postMessage(
        { type: 'GET_CACHE_STATUS' },
        [messageChannel.port2]
      )
    })
  }, [])

  // Update service worker
  const update = useCallback(async () => {
    const registration = await navigator.serviceWorker.getRegistration()
    if (registration) {
      await registration.update()
    }
  }, [])

  // Skip waiting and activate new service worker
  const skipWaiting = useCallback(async () => {
    const registration = await navigator.serviceWorker.getRegistration()
    if (registration?.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }, [])

  // Auto-register on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      register()
    }
  }, [register])

  return {
    state,
    register,
    preloadImages,
    clearImageCache,
    getCacheStatus,
    update,
    skipWaiting
  }
}

// Hook for managing image cache
export function useImageCache() {
  const { preloadImages, clearImageCache, getCacheStatus } = useServiceWorker()
  const [cacheStatus, setCacheStatus] = useState({ cacheSize: 0, cachedUrls: [] })

  const refreshCacheStatus = useCallback(async () => {
    const status = await getCacheStatus()
    setCacheStatus(status)
  }, [getCacheStatus])

  useEffect(() => {
    refreshCacheStatus()
  }, [refreshCacheStatus])

  return {
    cacheStatus,
    preloadImages,
    clearImageCache,
    refreshCacheStatus
  }
}
