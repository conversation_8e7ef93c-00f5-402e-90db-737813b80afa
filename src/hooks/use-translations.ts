import { useLanguage } from '@/contexts/simple-language-context'
import { useMemo } from 'react'

// 专门的翻译Hook，支持模块化翻译
export function useTranslations(module?: string) {
  const { t, language, setLanguage } = useLanguage()

  // 使用 useMemo 优化模块翻译函数，避免每次渲染都创建新函数
  const moduleT = useMemo(() => {
    return (key: string, params?: Record<string, string | number>) => {
      const fullKey = module ? `${module}.${key}` : key
      return t(fullKey, params)
    }
  }, [t, module])

  // 使用 useMemo 优化返回值，避免不必要的重新渲染
  return useMemo(() => ({
    t: moduleT,
    language,
    setLanguage,
    // 原始翻译函数，用于跨模块翻译
    globalT: t
  }), [moduleT, language, setLanguage, t])
}

// 预定义的模块翻译Hook
export const useCommonTranslations = () => useTranslations('common')
export const useNavigationTranslations = () => useTranslations('nav')
export const useProductsTranslations = () => useTranslations('products')
export const useNewsTranslations = () => useTranslations('news')
export const useHeroTranslations = () => useTranslations('hero')
export const useFooterTranslations = () => useTranslations('footer')
export const useHomeTranslations = () => useTranslations('home')
export const useApplicationTranslations = () => useTranslations('application')
export const useAboutTranslations = () => useTranslations('about')
export const useContactTranslations = () => useTranslations('contact')
// 销售与支持相关页面的翻译钩子
export const useAfterSalesTranslations = () => useTranslations('after-sales')
export const useSampleApplicationTranslations = () => useTranslations('sample-application')
export const useCustomizedServiceTranslations = () => useTranslations('customized-service')
export const useDownloadsTranslations = () => useTranslations('downloads')
export const useSearchTranslations = () => useTranslations('search')
// 应用案例相关页面的翻译钩子
export const useIoTSmartHomeTranslations = () => useTranslations('iot-smart-home')
export const useAIMachineLearningTranslations = () => useTranslations('ai-machine-learning')
export const useIndustrialAutomationTranslations = () => useTranslations('industrial-automation')
export const useEducationResearchTranslations = () => useTranslations('education-research')
// 注意：gaming-entertainment 和 automotive-transportation 模块暂未创建

// 使用示例：
// const { t } = useNewsTranslations()
// const title = t('title') // 相当于 t('news.title')
// const readMore = t('readMore') // 相当于 t('news.readMore')
