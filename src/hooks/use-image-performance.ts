'use client'

import { useEffect, useCallback, useRef } from 'react'

interface ImagePerformanceMetrics {
  loadTime: number
  size: number
  format: string
  src: string
  timestamp: number
}

interface PerformanceData {
  lcp?: number
  cls?: number
  totalImages: number
  averageLoadTime: number
  slowestImage?: ImagePerformanceMetrics
  fastestImage?: ImagePerformanceMetrics
}

export function useImagePerformance() {
  const metrics = useRef<ImagePerformanceMetrics[]>([])
  const observers = useRef<{
    lcp?: PerformanceObserver
    cls?: PerformanceObserver
  }>({})

  // Track image load performance
  const trackImageLoad = useCallback((src: string, startTime: number) => {
    const endTime = performance.now()
    const loadTime = endTime - startTime

    // Try to get image size from performance entries
    const perfEntries = performance.getEntriesByName(src)
    const entry = perfEntries[perfEntries.length - 1] as PerformanceResourceTiming

    const metric: ImagePerformanceMetrics = {
      loadTime,
      size: entry?.transferSize || 0,
      format: src.split('.').pop()?.toLowerCase() || 'unknown',
      src,
      timestamp: Date.now()
    }

    metrics.current.push(metric)

    // Track slow images (> 2 seconds) for performance monitoring
    if (loadTime > 2000 && process.env.NODE_ENV === 'development') {
      console.warn(`Slow image load detected: ${src} took ${loadTime.toFixed(2)}ms`)
    }

    return metric
  }, [])

  // Get performance summary
  const getPerformanceData = useCallback((): PerformanceData => {
    const imageMetrics = metrics.current
    
    if (imageMetrics.length === 0) {
      return {
        totalImages: 0,
        averageLoadTime: 0
      }
    }

    const totalLoadTime = imageMetrics.reduce((sum, metric) => sum + metric.loadTime, 0)
    const averageLoadTime = totalLoadTime / imageMetrics.length

    const sortedByLoadTime = [...imageMetrics].sort((a, b) => a.loadTime - b.loadTime)
    const slowestImage = sortedByLoadTime[sortedByLoadTime.length - 1]
    const fastestImage = sortedByLoadTime[0]

    return {
      totalImages: imageMetrics.length,
      averageLoadTime,
      slowestImage,
      fastestImage
    }
  }, [])

  // Monitor Core Web Vitals
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Monitor Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as PerformanceEntry & {
            element?: Element
            url?: string
          }

          if (lastEntry && lastEntry.element?.tagName === 'IMG' && process.env.NODE_ENV === 'development') {
            console.log('LCP Image:', {
              url: lastEntry.url,
              loadTime: lastEntry.startTime,
              element: lastEntry.element
            })
          }
        })

        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        observers.current.lcp = lcpObserver

        // Monitor Cumulative Layout Shift (CLS)
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          const entries = list.getEntries()

          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })

          if (clsValue > 0.1 && process.env.NODE_ENV === 'development') {
            console.warn('High CLS detected:', clsValue)
          }
        })

        clsObserver.observe({ entryTypes: ['layout-shift'] })
        observers.current.cls = clsObserver

      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Performance monitoring not supported:', error)
        }
      }
    }

    return () => {
      observers.current.lcp?.disconnect()
      observers.current.cls?.disconnect()
    }
  }, [])

  // Clear metrics
  const clearMetrics = useCallback(() => {
    metrics.current = []
  }, [])

  // Export metrics for analysis
  const exportMetrics = useCallback(() => {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: metrics.current,
      summary: getPerformanceData()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `image-performance-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }, [getPerformanceData])

  return {
    trackImageLoad,
    getPerformanceData,
    clearMetrics,
    exportMetrics
  }
}

// Hook for tracking individual image performance
export function useImageLoadTracking(src: string) {
  const { trackImageLoad } = useImagePerformance()
  const startTime = useRef<number>()

  const onLoadStart = useCallback(() => {
    startTime.current = performance.now()
  }, [])

  const onLoadComplete = useCallback(() => {
    if (startTime.current) {
      trackImageLoad(src, startTime.current)
    }
  }, [src, trackImageLoad])

  return {
    onLoadStart,
    onLoadComplete
  }
}
