'use client'

import { useState, useEffect, useRef } from 'react'

interface UseCounterAnimationOptions {
  start?: number
  end: string | number
  duration?: number
  suffix?: string
  prefix?: string
  separator?: string
  decimals?: number
}

export function useCounterAnimation({
  start = 0,
  end,
  duration = 2000,
  suffix = '',
  prefix = '',
  separator = '',
  decimals = 0
}: UseCounterAnimationOptions) {
  const [count, setCount] = useState(start)
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  // 解析数字（处理 K+, M+ 等后缀）
  const parseNumber = (value: string | number): number => {
    if (typeof value === 'number') return value
    
    const numStr = value.toString().toLowerCase()
    const num = parseFloat(numStr.replace(/[^0-9.]/g, ''))
    
    if (numStr.includes('k')) return num * 1000
    if (numStr.includes('m')) return num * 1000000
    if (numStr.includes('b')) return num * 1000000000
    
    return num
  }

  // 格式化数字显示
  const formatNumber = (value: number): string => {
    if (end === undefined || end === null) {
      return value.toString()
    }

    let formattedValue = ''
    const endStr = end.toString()

    if (endStr.includes('K') && value >= 1000) {
      formattedValue = (value / 1000).toFixed(decimals) + 'K'
    } else if (endStr.includes('M') && value >= 1000000) {
      formattedValue = (value / 1000000).toFixed(decimals) + 'M'
    } else if (endStr.includes('B') && value >= 1000000000) {
      formattedValue = (value / 1000000000).toFixed(decimals) + 'B'
    } else {
      formattedValue = value.toFixed(decimals)
    }

    // 添加分隔符
    if (separator && !formattedValue.includes('K') && !formattedValue.includes('M') && !formattedValue.includes('B')) {
      formattedValue = formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, separator)
    }

    // 保留原始后缀（如 + 号）
    if (endStr.includes('+')) {
      formattedValue += '+'
    }

    return prefix + formattedValue + suffix
  }

  // Intersection Observer 用于检测元素是否进入视口
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true)
          setHasAnimated(true)
        }
      },
      {
        threshold: 0.3, // 当元素30%可见时触发
        rootMargin: '0px 0px -50px 0px' // 提前50px触发
      }
    )

    if (elementRef.current) {
      observer.observe(elementRef.current)
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current)
      }
    }
  }, [hasAnimated])

  // 数字递增动画
  useEffect(() => {
    if (!isVisible) return

    const targetValue = parseNumber(end)
    const startValue = parseNumber(start)
    const difference = targetValue - startValue

    if (difference === 0) {
      setCount(targetValue)
      return
    }

    const steps = Math.min(100, Math.abs(difference))
    const stepValue = difference / steps
    const stepTime = duration / steps

    let current = startValue
    let stepCount = 0

    const timer = setInterval(() => {
      stepCount++
      current = startValue + (stepValue * stepCount)

      if (stepCount >= steps) {
        current = targetValue
        clearInterval(timer)
      }

      setCount(Math.round(current))
    }, stepTime)

    return () => clearInterval(timer)
  }, [isVisible, start, end, duration])

  return {
    ref: elementRef,
    value: formatNumber(count),
    isAnimating: isVisible && count !== parseNumber(end)
  }
}
