'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useLanguage } from '@/contexts/simple-language-context'
import { detectPageType, getPageTitle, getPageDescription } from '@/config/page-titles'

interface UsePageMetadataOptions {
  customTitle?: string
  customDescription?: string
  updateDocumentTitle?: boolean
}

export function usePageMetadata(options: UsePageMetadataOptions = {}) {
  const { language } = useLanguage()
  const pathname = usePathname()
  const { customTitle, customDescription, updateDocumentTitle = true } = options

  useEffect(() => {
    if (!updateDocumentTitle || typeof document === 'undefined') return

    const pageType = detectPageType(pathname)
    const title = getPageTitle(pageType, language, customTitle)
    
    // 更新document.title
    document.title = title

    // 更新meta描述
    const description = getPageDescription(pageType, language, customDescription)
    let metaDescription = document.querySelector('meta[name="description"]')
    
    if (metaDescription) {
      metaDescription.setAttribute('content', description)
    } else {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      metaDescription.setAttribute('content', description)
      document.head.appendChild(metaDescription)
    }

    // 更新Open Graph标题
    let ogTitle = document.querySelector('meta[property="og:title"]')
    if (ogTitle) {
      ogTitle.setAttribute('content', title)
    } else {
      ogTitle = document.createElement('meta')
      ogTitle.setAttribute('property', 'og:title')
      ogTitle.setAttribute('content', title)
      document.head.appendChild(ogTitle)
    }

    // 更新Open Graph描述
    let ogDescription = document.querySelector('meta[property="og:description"]')
    if (ogDescription) {
      ogDescription.setAttribute('content', description)
    } else {
      ogDescription = document.createElement('meta')
      ogDescription.setAttribute('property', 'og:description')
      ogDescription.setAttribute('content', description)
      document.head.appendChild(ogDescription)
    }

    // 更新Twitter标题
    let twitterTitle = document.querySelector('meta[name="twitter:title"]')
    if (twitterTitle) {
      twitterTitle.setAttribute('content', title)
    } else {
      twitterTitle = document.createElement('meta')
      twitterTitle.setAttribute('name', 'twitter:title')
      twitterTitle.setAttribute('content', title)
      document.head.appendChild(twitterTitle)
    }

    // 更新Twitter描述
    let twitterDescription = document.querySelector('meta[name="twitter:description"]')
    if (twitterDescription) {
      twitterDescription.setAttribute('content', description)
    } else {
      twitterDescription = document.createElement('meta')
      twitterDescription.setAttribute('name', 'twitter:description')
      twitterDescription.setAttribute('content', description)
      document.head.appendChild(twitterDescription)
    }

  }, [pathname, language, customTitle, customDescription, updateDocumentTitle])

  const pageType = detectPageType(pathname)
  const title = getPageTitle(pageType, language, customTitle)
  const description = getPageDescription(pageType, language, customDescription)

  return {
    pageType,
    title,
    description,
    language,
    pathname
  }
}

// 专门用于产品页面的元数据Hook
export function useProductMetadata(productName?: string, productDescription?: string) {
  const { language } = useLanguage()
  
  const customTitle = productName ? `${productName} - 产品详情` : undefined
  const customDescription = productDescription

  return usePageMetadata({
    customTitle,
    customDescription
  })
}

// 专门用于新闻页面的元数据Hook
export function useNewsMetadata(newsTitle?: string, newsExcerpt?: string) {
  const { language } = useLanguage()
  
  const customTitle = newsTitle ? `${newsTitle} - 新闻详情` : undefined
  const customDescription = newsExcerpt

  return usePageMetadata({
    customTitle,
    customDescription
  })
}
