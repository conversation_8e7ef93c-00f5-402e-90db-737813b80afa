/**
 * 图片预加载配置
 * 定义需要预加载的关键图片
 */

// 关键图片列表（首屏和重要页面的图片）
export const criticalImages = [
  // 首页轮播图
  '/images/products/lattepanda-sigma-hero.jpg',
  '/images/hero/Linnuo-mu-hero.jpg',
  '/images/hero/Linnuo-delta-hero.jpg',
  
  // 主要产品图片
  '/images/products/Linnuo-sigma.jpg',
  '/images/products/Linnuo-mu.jpg',
  '/images/products/Linnuo-delta.jpg',
  '/images/products/Linnuo-alpha.jpg',
  
  // 品牌和Logo
  '/logo-top-bg-white.svg',
  
  // 常用图标和背景
  '/images/background/prod_bg.jpg',
  '/images/icons/iot-icon.svg',
  '/images/icons/ai-icon.svg',
]

// 按页面分组的图片预加载配置
export const pageImagePreloads = {
  // 首页
  home: [
    '/images/products/lattepanda-sigma-hero.jpg',
    '/images/products/Linnuo-sigma.jpg',
    '/images/products/Linnuo-mu.jpg',
    '/images/products/Linnuo-delta.jpg',
    '/images/backgrounds/features-bg.jpg',
  ],
  
  // 产品页面
  products: [
    '/images/products/Linnuo-sigma.jpg',
    '/images/products/Linnuo-mu.jpg',
    '/images/products/Linnuo-delta.jpg',
    '/images/products/Linnuo-alpha.jpg',
    '/images/products/Linnuo-3-delta.jpg',
  ],
  
  // 应用页面
  application: [
    '/images/applications/energy-power-solutions.jpg',
    '/images/applications/ai-machine-learning.jpg',
    '/images/applications/industrial-automation.jpg',
    '/images/applications/education-research.jpg',
  ],
  
  // 新闻页面
  news: [
    '/images/news/Linnuo-sigma-ces-2024.jpg',
    '/images/news/intel-partner-award.jpg',
    '/images/news/iot-solutions.jpg',
  ],
  
  // 关于页面
  about: [
    '/images/about/company-hero.jpg',
    '/images/about/team-photo.jpg',
    '/images/about/office-photo.jpg',
  ]
}

// 图片优化配置
export const imageOptimizationConfig = {
  // 默认质量设置
  quality: {
    high: 95,      // 用于重要的产品图片
    medium: 85,    // 用于一般内容图片
    low: 75,       // 用于缩略图和背景图片
  },
  
  // 响应式断点
  breakpoints: [320, 640, 768, 1024, 1280, 1920],
  
  // 支持的现代格式
  formats: {
    avif: true,
    webp: true,
    jpeg: true,
    png: true,
  },
  
  // 懒加载配置
  lazyLoading: {
    rootMargin: '100px',  // 提前100px开始加载
    threshold: 0.1,       // 10%可见时开始加载
  },
  
  // 预加载配置
  preloading: {
    critical: true,       // 启用关键图片预加载
    hover: true,          // 启用悬停预加载
    viewport: true,       // 启用视口预加载
  },
  
  // 性能监控
  monitoring: {
    enabled: process.env.NODE_ENV === 'development',
    logSlowImages: true,  // 记录加载慢的图片
    slowThreshold: 1000,  // 超过1秒算慢
  }
}

// 图片尺寸配置
export const imageSizes = {
  // 产品图片
  product: {
    thumbnail: { width: 300, height: 300 },
    card: { width: 400, height: 400 },
    detail: { width: 800, height: 800 },
    hero: { width: 1200, height: 800 },
  },
  
  // 新闻图片
  news: {
    thumbnail: { width: 300, height: 200 },
    card: { width: 600, height: 400 },
    hero: { width: 1200, height: 600 },
  },
  
  // 轮播图片
  carousel: {
    mobile: { width: 768, height: 400 },
    tablet: { width: 1024, height: 500 },
    desktop: { width: 1920, height: 800 },
  },
  
  // 头像和Logo
  avatar: {
    small: { width: 32, height: 32 },
    medium: { width: 64, height: 64 },
    large: { width: 128, height: 128 },
  },
  
  logo: {
    small: { width: 120, height: 40 },
    medium: { width: 180, height: 60 },
    large: { width: 240, height: 80 },
  }
}

// 图片CDN配置
export const cdnConfig = {
  // CDN域名
  domain: process.env.NEXT_PUBLIC_CDN_DOMAIN || '',
  
  // 图片变换参数
  transforms: {
    quality: (q: number) => `q_${q}`,
    width: (w: number) => `w_${w}`,
    height: (h: number) => `h_${h}`,
    format: (f: string) => `f_${f}`,
    crop: (mode: string) => `c_${mode}`,
  },
  
  // 自动优化
  autoOptimize: true,
  
  // 自动格式选择
  autoFormat: true,
}

// 获取优化后的图片URL
export function getOptimizedImageUrl(
  src: string, 
  options: {
    width?: number
    height?: number
    quality?: number
    format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png'
    crop?: 'fill' | 'fit' | 'crop'
  } = {}
): string {
  // 如果没有配置CDN，直接返回原始URL
  if (!cdnConfig.domain) {
    return src
  }
  
  const {
    width,
    height,
    quality = imageOptimizationConfig.quality.medium,
    format = 'auto',
    crop = 'fill'
  } = options
  
  const params = []
  
  if (width) params.push(cdnConfig.transforms.width(width))
  if (height) params.push(cdnConfig.transforms.height(height))
  if (quality) params.push(cdnConfig.transforms.quality(quality))
  if (format !== 'auto') params.push(cdnConfig.transforms.format(format))
  if (crop) params.push(cdnConfig.transforms.crop(crop))
  
  const transformString = params.join(',')
  
  // 构建CDN URL
  return `${cdnConfig.domain}/${transformString}/${src.replace(/^\//, '')}`
}

// 生成响应式图片的srcSet
export function generateResponsiveSrcSet(
  src: string,
  sizes: number[] = imageOptimizationConfig.breakpoints
): string {
  return sizes
    .map(size => {
      const optimizedUrl = getOptimizedImageUrl(src, { width: size })
      return `${optimizedUrl} ${size}w`
    })
    .join(', ')
}
