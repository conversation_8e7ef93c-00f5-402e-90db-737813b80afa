import { getApiConfig } from '@/lib/api-config'

// 获取API配置
const apiConfig = getApiConfig()

// 应用配置
export const appConfig = {
  // 是否启用Strapi后端 (使用新的配置管理器)
  enableStrapi: apiConfig.enableStrapi,

  // Strapi配置
  strapi: {
    url: apiConfig.strapiUrl,
    apiToken: apiConfig.apiToken || '',
  },

  // 部署配置
  deployment: {
    target: apiConfig.deploymentTarget,
    isCloudflarePages: apiConfig.deploymentTarget === 'cloudflare-pages',
    isProduction: process.env.NODE_ENV === 'production',
  },
  
  // 开发模式配置
  development: {
    // 在开发模式下是否显示API错误详情
    showApiErrors: process.env.NODE_ENV === 'development',
    // 是否使用模拟数据延迟
    useMockDelay: true,
    mockDelayMs: 500,
  },
  
  // 功能开关
  features: {
    // 是否启用新闻功能
    enableNews: true,
    // 是否启用产品比较功能
    enableProductComparison: true,
    // 是否启用搜索功能
    enableSearch: true,
  }
}

// 运行时检查函数
export const isBackendAvailable = () => {
  return appConfig.enableStrapi
}

// 获取API基础URL
export const getApiBaseUrl = () => {
  return appConfig.strapi.url
}

// 日志函数
export const logApiStatus = (message: string, type: 'info' | 'warn' | 'error' = 'info') => {
  if (appConfig.development.showApiErrors) {
    console[type](`[API] ${message}`)
  }
}
