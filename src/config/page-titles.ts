// 页面标题配置
export const pageTitles = {
  zh: {
    home: 'Linnuo - x86 Windows/Linux 单板计算机',
    products: 'Products - 产品中心',
    'products-detail': '产品详情',
    'products-compare': '产品对比',
    news: 'News - 新闻中心', 
    'news-detail': '新闻详情',
    about: 'About - 关于我们',
    contact: 'Contact - 联系我们',
    downloads: 'Downloads - 下载中心',
    application: 'Application - 应用案例',
    'application-industrial': '工业自动化应用',
    'application-ai': 'AI与机器学习应用',
    'application-energy': '能源电力解决方案',
    'application-education': '教育科研应用',
    support: 'Support - 技术支持',
    search: 'Search - 搜索结果',
    '404': '页面未找到 - 404',
  },
  en: {
    home: 'Linnuo - x86 Windows/Linux Single Board Computers',
    products: 'Products - Product Center',
    'products-detail': 'Product Details',
    'products-compare': 'Product Comparison',
    news: 'News - News Center',
    'news-detail': 'News Details', 
    about: 'About - About Us',
    contact: 'Contact - Contact Us',
    downloads: 'Downloads - Download Center',
    application: 'Application - Use Cases',
    'application-industrial': 'Industrial Automation Applications',
    'application-ai': 'AI & Machine Learning Applications',
    'application-energy': 'Energy & Power Solutions',
    'application-education': 'Education & Research Applications',
    support: 'Support - Technical Support',
    search: 'Search - Search Results',
    '404': 'Page Not Found - 404',
  }
}

// 页面描述配置
export const pageDescriptions = {
  zh: {
    home: 'Linnuo是一款完整的Windows/Linux设备，集成在单板计算机中，可以运行几乎任何x86软件。完美适用于物联网、机器人和嵌入式应用。',
    products: '探索Linnuo全系列单板计算机产品，专为全球创客、开发者和创新者设计的高性能x86计算解决方案。',
    news: '获取Linnuo最新产品发布、技术更新和行业动态，了解单板计算机领域的前沿资讯。',
    about: '了解Linnuo公司历史、团队介绍、企业文化和发展愿景，我们致力于推动嵌入式计算技术创新。',
    contact: '联系Linnuo技术团队，获取产品咨询、技术支持和商务合作信息。',
    downloads: '下载Linnuo产品相关的驱动程序、开发工具、技术文档和示例代码。',
    application: '探索Linnuo单板计算机在工业自动化、AI机器学习、能源电力、教育科研等领域的创新应用案例。',
    support: '获取Linnuo产品的技术支持、故障排除指南、常见问题解答和售后服务。',
  },
  en: {
    home: 'Linnuo is a complete Windows/Linux device in a single board computer that can run almost any x86 software. Perfect for IoT, robotics, and embedded applications.',
    products: 'Explore Linnuo\'s complete range of single board computer products, high-performance x86 computing solutions designed for makers, developers, and innovators worldwide.',
    news: 'Get the latest Linnuo product releases, technical updates, and industry news. Stay informed about cutting-edge developments in single board computing.',
    about: 'Learn about Linnuo\'s company history, team introduction, corporate culture, and development vision. We are committed to driving innovation in embedded computing technology.',
    contact: 'Contact the Linnuo technical team for product consultation, technical support, and business cooperation information.',
    downloads: 'Download Linnuo product-related drivers, development tools, technical documentation, and sample code.',
    application: 'Explore innovative application cases of Linnuo single board computers in industrial automation, AI machine learning, energy power, education and research fields.',
    support: 'Get technical support for Linnuo products, troubleshooting guides, frequently asked questions, and after-sales service.',
  }
}

// 获取页面标题
export function getPageTitle(pageKey: string, language: 'zh' | 'en' = 'zh', customTitle?: string): string {
  if (customTitle) {
    return `${customTitle} | Linnuo`
  }
  
  const titles = pageTitles[language]
  return titles[pageKey as keyof typeof titles] || titles.home
}

// 获取页面描述
export function getPageDescription(pageKey: string, language: 'zh' | 'en' = 'zh', customDescription?: string): string {
  if (customDescription) {
    return customDescription
  }
  
  const descriptions = pageDescriptions[language]
  return descriptions[pageKey as keyof typeof descriptions] || descriptions.home
}

// 根据路径自动检测页面类型
export function detectPageType(pathname: string): string {
  // 移除语言前缀
  const cleanPath = pathname.replace(/^\/(zh|en)/, '')
  
  if (cleanPath === '' || cleanPath === '/') return 'home'
  if (cleanPath.startsWith('/products/compare')) return 'products-compare'
  if (cleanPath.startsWith('/products/') && cleanPath.split('/').length > 2) return 'products-detail'
  if (cleanPath.startsWith('/products')) return 'products'
  if (cleanPath.startsWith('/news/') && cleanPath.split('/').length > 2) return 'news-detail'
  if (cleanPath.startsWith('/news')) return 'news'
  if (cleanPath.startsWith('/application/industrial-automation')) return 'application-industrial'
  if (cleanPath.startsWith('/application/ai-machine-learning')) return 'application-ai'
  if (cleanPath.startsWith('/application/energy-power-solutions')) return 'application-energy'
  if (cleanPath.startsWith('/application/education-research')) return 'application-education'
  if (cleanPath.startsWith('/application')) return 'application'
  if (cleanPath.startsWith('/about')) return 'about'
  if (cleanPath.startsWith('/contact')) return 'contact'
  if (cleanPath.startsWith('/downloads')) return 'downloads'
  if (cleanPath.startsWith('/support')) return 'support'
  if (cleanPath.startsWith('/search')) return 'search'
  
  return '404'
}

// 生成完整的页面元数据
export function generatePageMetadata(
  pageKey: string, 
  language: 'zh' | 'en' = 'zh',
  customTitle?: string,
  customDescription?: string,
  customKeywords?: string[]
) {
  const title = getPageTitle(pageKey, language, customTitle)
  const description = getPageDescription(pageKey, language, customDescription)
  
  // 基础关键词
  const baseKeywords = language === 'zh' 
    ? ['Linnuo', '单板计算机', 'SBC', 'x86', '嵌入式', '物联网', '工业计算']
    : ['Linnuo', 'single board computer', 'SBC', 'x86', 'embedded', 'IoT', 'industrial computing']
  
  const keywords = customKeywords ? [...baseKeywords, ...customKeywords] : baseKeywords
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: language === 'zh' ? 'zh_CN' : 'en_US',
      siteName: 'Linnuo',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      creator: '@LinnuoCN',
    },
  }
}
