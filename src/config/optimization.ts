// 性能优化配置
export const optimizationConfig = {
  // Core Web Vitals 阈值
  coreWebVitals: {
    lcp: {
      good: 2500,    // 毫秒
      poor: 4000,    // 毫秒
    },
    fid: {
      good: 100,     // 毫秒
      poor: 300,     // 毫秒
    },
    cls: {
      good: 0.1,     // 分数
      poor: 0.25,    // 分数
    },
    fcp: {
      good: 1800,    // 毫秒
      poor: 3000,    // 毫秒
    },
    ttfb: {
      good: 800,     // 毫秒
      poor: 1800,    // 毫秒
    }
  },

  // 图片优化配置
  images: {
    // 质量设置
    quality: {
      hero: 95,        // 英雄图片
      product: 90,     // 产品图片
      thumbnail: 75,   // 缩略图
      background: 80,  // 背景图片
    },
    
    // 格式优先级
    formats: ['avif', 'webp', 'jpeg', 'png'],
    
    // 响应式断点
    breakpoints: [320, 640, 768, 1024, 1280, 1920],
    
    // 懒加载配置
    lazyLoading: {
      rootMargin: '100px 0px',
      threshold: 0.01,
      enableIntersectionObserver: true,
    },
    
    // 预加载配置
    preloading: {
      critical: [
        '/images/background/about-bg.jpg',
        '/logo-top-bg-white.svg',
        '/images/products/sigma-main.jpg'
      ],
      hover: true,
      viewport: true,
    }
  },

  // 缓存策略
  caching: {
    // Service Worker 缓存
    serviceWorker: {
      enabled: true,
      version: 'v1',
      strategies: {
        images: 'stale-while-revalidate',
        api: 'network-first',
        static: 'cache-first',
      }
    },
    
    // 浏览器缓存
    browser: {
      static: '1y',      // 静态资源
      images: '30d',     // 图片
      api: '5m',         // API响应
      html: '0',         // HTML页面
    },
    
    // React Query 缓存
    reactQuery: {
      staleTime: 5 * 60 * 1000,      // 5分钟
      cacheTime: 30 * 60 * 1000,     // 30分钟
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  },

  // 代码分割配置
  codeSplitting: {
    // 路由级分割
    routes: true,
    
    // 组件级分割
    components: [
      'ProductDetail',
      'NewsDetail', 
      'SearchModal',
      'PerformanceDashboard'
    ],
    
    // 第三方库分割
    vendors: [
      'react-query',
      'framer-motion',
      'lucide-react'
    ]
  },

  // 预加载策略
  preloading: {
    // DNS 预解析
    dnsPrefetch: [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://vivid-pleasure-04cb3dbd82.strapiapp.com'
    ],
    
    // 预连接
    preconnect: [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ],
    
    // 关键资源预加载
    preload: [
      { href: '/fonts/inter.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
      { href: '/images/background/about-bg.jpg', as: 'image', type: 'image/jpeg' }
    ]
  },

  // 字体优化
  fonts: {
    // 字体显示策略
    display: 'swap',
    
    // 预加载字体
    preload: [
      '/fonts/inter-400.woff2',
      '/fonts/inter-600.woff2'
    ],
    
    // 字体子集
    subset: ['latin', 'latin-ext'],
    
    // 可变字体
    variable: true
  },

  // JavaScript 优化
  javascript: {
    // 压缩配置
    minification: {
      removeComments: true,
      removeConsoleLog: process.env.NODE_ENV === 'production',
      removeDebugger: true,
    },
    
    // Tree shaking
    treeShaking: true,
    
    // 模块联邦
    moduleFederation: false,
    
    // 动态导入
    dynamicImports: [
      'web-vitals',
      'intersection-observer'
    ]
  },

  // CSS 优化
  css: {
    // 关键CSS内联
    critical: {
      enabled: true,
      inline: true,
      minify: true,
    },
    
    // CSS压缩
    minification: true,
    
    // 未使用CSS移除
    purge: {
      enabled: process.env.NODE_ENV === 'production',
      safelist: ['dark', 'light', 'fonts-loaded']
    },
    
    // CSS-in-JS优化
    cssInJs: {
      extractStatic: true,
      optimizeAtRuntime: false,
    }
  },

  // 监控配置
  monitoring: {
    // 性能监控
    performance: {
      enabled: process.env.NODE_ENV === 'development',
      sampleRate: 0.1, // 10% 采样率
      reportInterval: 30000, // 30秒
    },
    
    // 错误监控
    errors: {
      enabled: true,
      captureConsoleErrors: true,
      captureUnhandledRejections: true,
    },
    
    // 用户体验监控
    userExperience: {
      trackClicks: true,
      trackScrollDepth: true,
      trackTimeOnPage: true,
    }
  },

  // 构建优化
  build: {
    // 并行构建
    parallel: true,
    
    // 增量构建
    incremental: true,
    
    // 构建缓存
    cache: {
      enabled: true,
      directory: '.next/cache',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    },
    
    // 输出优化
    output: {
      // 静态导出
      export: false,
      
      // 压缩
      compress: true,
      
      // 文件名哈希
      hash: true,
    }
  }
}

// 获取环境特定的配置
export function getOptimizationConfig(env: 'development' | 'production' | 'test') {
  const baseConfig = optimizationConfig

  switch (env) {
    case 'development':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          performance: {
            ...baseConfig.monitoring.performance,
            enabled: true,
          }
        },
        javascript: {
          ...baseConfig.javascript,
          minification: {
            ...baseConfig.javascript.minification,
            removeConsoleLog: false,
          }
        }
      }

    case 'production':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          performance: {
            ...baseConfig.monitoring.performance,
            enabled: false,
          }
        },
        css: {
          ...baseConfig.css,
          purge: {
            ...baseConfig.css.purge,
            enabled: true,
          }
        }
      }

    case 'test':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          performance: {
            ...baseConfig.monitoring.performance,
            enabled: false,
          }
        }
      }

    default:
      return baseConfig
  }
}

export default optimizationConfig
