// 安全配置
export const securityConfig = {
  // CSP (Content Security Policy) 配置
  csp: {
    enabled: process.env.NODE_ENV === 'production',
    directives: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'",
        "'unsafe-eval'",
        'https://www.googletagmanager.com',
        'https://www.google-analytics.com',
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'",
        'https://fonts.googleapis.com',
      ],
      'img-src': [
        "'self'",
        'data:',
        'https:',
        'http:',
        'blob:',
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
      ],
      'connect-src': [
        "'self'",
        'https://api.strapi.io',
        'https://www.google-analytics.com',
        process.env.NEXT_PUBLIC_STRAPI_URL || '',
      ].filter(Boolean),
      'frame-ancestors': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
    },
  },

  // 安全头配置
  headers: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  // API 安全配置
  api: {
    // 请求频率限制
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最多100个请求
    },
    
    // CORS 配置
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://your-domain.com'] 
        : ['http://localhost:3000'],
      credentials: true,
    },
    
    // 输入验证
    validation: {
      maxBodySize: '10mb',
      sanitizeInput: true,
    },
  },

  // 敏感数据处理
  dataProtection: {
    // 日志脱敏
    logSanitization: {
      enabled: true,
      fields: ['password', 'token', 'apiKey', 'email'],
    },
    
    // 数据加密
    encryption: {
      algorithm: 'aes-256-gcm',
      keyRotation: 30 * 24 * 60 * 60 * 1000, // 30天
    },
  },
}

// 生成 CSP 字符串
export function generateCSP(): string {
  if (!securityConfig.csp.enabled) return ''
  
  const directives = Object.entries(securityConfig.csp.directives)
    .map(([key, values]) => `${key} ${values.join(' ')}`)
    .join('; ')
  
  return directives
}

// 验证环境变量安全性
export function validateEnvironmentSecurity(): void {
  const warnings: string[] = []
  
  // 检查生产环境配置
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.NEXT_PUBLIC_STRAPI_API_TOKEN) {
      warnings.push('Missing STRAPI_API_TOKEN in production')
    }
    
    if (process.env.NEXT_PUBLIC_STRAPI_URL?.includes('localhost')) {
      warnings.push('Using localhost URL in production')
    }
  }
  
  // 检查敏感信息泄露
  const publicEnvVars = Object.keys(process.env).filter(key => 
    key.startsWith('NEXT_PUBLIC_')
  )
  
  const sensitivePatterns = ['password', 'secret', 'private', 'key']
  publicEnvVars.forEach(envVar => {
    if (sensitivePatterns.some(pattern => 
      envVar.toLowerCase().includes(pattern)
    )) {
      warnings.push(`Potentially sensitive env var exposed: ${envVar}`)
    }
  })
  
  if (warnings.length > 0) {
    console.warn('🔒 Security warnings:', warnings)
  }
}

// 初始化安全检查
if (typeof window === 'undefined') {
  validateEnvironmentSecurity()
}
