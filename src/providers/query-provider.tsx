'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useState } from 'react'

export function QueryProvider({ children }: { children: React.ReactNode }) {
  // 使用 useState 确保 QueryClient 只创建一次
  const [queryClient] = useState(() => {
    // console.log('🔧 创建新的 QueryClient 实例')
    return new QueryClient({
      defaultOptions: {
        queries: {
          // 优化缓存策略：增加数据缓存时间，减少API调用
          staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
          // 缓存保持时间：30分钟
          gcTime: 30 * 60 * 1000,
          // 减少重试次数，避免过多失败请求
          retry: (failureCount, error: any) => {
            // 对于4xx错误不重试
            if (error?.response?.status >= 400 && error?.response?.status < 500) {
              return false
            }
            // 最多重试2次
            return failureCount < 2
          },
          // 重试延迟：指数退避
          retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          // 重新获取数据的条件 - 减少不必要的重新获取
          refetchOnWindowFocus: false,
          refetchOnReconnect: true,
          refetchOnMount: true,
          // 确保查询默认启用
          enabled: true,
          // 网络模式：在线时才获取数据
          networkMode: 'online',
        },
      },
    })
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
