export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar?: string
    bio?: string
  }
  publishedAt: string
  updatedAt?: string
  category: string
  tags: string[]
  featuredImage?: {
    url: string
    alt: string
  }
  readTime: number // in minutes
  status: 'draft' | 'published' | 'archived'
  featured?: boolean
  views?: number
  likes?: number
}

export interface BlogCategory {
  id: string
  name: string
  slug: string
  description: string
  color: string
}

export interface BlogTag {
  id: string
  name: string
  slug: string
  count: number
}
