// 简化的翻译类型定义 - 临时修复构建问题
export interface Translations {
  common: {
    loading: string
    error: string
    success: string
    cancel: string
    confirm: string
    save: string
    edit: string
    delete: string
    search: string
    filter: string
    sort: string
    more: string
    less: string
    viewAll: string
    backToTop: string
  }
  navigation: {
    home: string
    products: string
    about: string
    news: string
    contact: string
    [key: string]: string
  }
  hero: {
    title: string
    subtitle: string
    description: string
    [key: string]: string
  }
  products: {
    title: string
    description: string
    features: string
    specifications: string
    [key: string]: string
  }
  news: {
    title: string
    readMore: string
    publishedAt: string
    [key: string]: string
  }
  footer: {
    company: string
    products: string
    support: string
    legal: string
    [key: string]: string
  }
  // 通用字符串索引，支持所有可能的翻译键
  [key: string]: any
}

// 为了兼容性，也导出原来的接口名
export type TranslationKeys = Translations
