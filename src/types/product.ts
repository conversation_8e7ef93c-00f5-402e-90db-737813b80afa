export interface Product {
  id: string
  name: string
  slug: string
  description: string
  shortDescription: string
  category: ProductCategory
  status: ProductStatus
  images: ProductImage[]
  specifications: ProductSpecification[]
  features: string[]
  tags: string[]
  releaseDate: string
  isNew?: boolean
  isFeatured?: boolean
  downloadLinks?: DownloadLink[]
  documentation?: DocumentationLink[]
  compatibility: string[]
  warranty: string
  support: string[]
  createdAt?: string
  updatedAt?: string
  featured?: boolean
  price?: number
  rating?: number
  reviewCount?: number
  // 技术规格字段（来自API）
  cpuLeiXing?: string // CPU类型
  neiCun?: string // 内存
  wangKa?: string // 网卡
  xianShiJieKou?: string // 显示接口
  powerType?: string // 电源类型
  operating_system?: string // 操作系统
  operating_temperature?: string // 工作温度
  // 详情页面标签内容
  detailTabs?: {
    description: string // 富文本 HTML 内容
    specifications: string // 富文本 HTML 内容
    compatibility: string // 富文本 HTML 内容
    downloads: ProductDownload[] // 多媒体下载内容
  }
}

export interface ProductImage {
  id: string
  url: string
  alt: string
  type: 'main' | 'gallery' | 'thumbnail'
  order: number
}

export interface ProductSpecification {
  category: string
  items: SpecificationItem[]
}

export interface SpecificationItem {
  label: string
  value: string
  unit?: string
  highlight?: boolean
}

export interface DownloadLink {
  name: string
  url: string
  type: 'driver' | 'software' | 'manual' | 'firmware'
  version?: string
  size?: string
  platform?: string[]
}

export interface DocumentationLink {
  title: string
  url: string
  type: 'guide' | 'tutorial' | 'api' | 'reference'
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
}

export interface ProductDownload {
  id: string
  title: string
  description: string
  type: 'manual' | 'driver' | 'software' | 'firmware' | 'schematic' | 'datasheet'
  fileUrl: string
  fileName: string
  fileSize: string
  version?: string
  releaseDate: string
  platform?: string[]
  thumbnail?: string
}

export type ProductCategory =
  | 'scalable-embedded-series'
  | 'mini-size-series'
  | 'universal-embedded-series'
  | 'all-in-one-ipc'
  | 'accessories'
  | 'development-kit'
  | 'expansion-board'
  | 'case'
  | 'power-supply'
  | 'storage'
  | 'display'
  | 'sensor'
  | 'other'

export type ProductStatus = 
  | 'available'
  | 'pre-order'
  | 'out-of-stock'
  | 'discontinued'
  | 'coming-soon'

export interface ProductFilter {
  category?: ProductCategory[]
  isNew?: boolean
  isFeatured?: boolean
  tags?: string[]
  search?: string
}

export interface ProductSort {
  field: 'name' | 'releaseDate' | 'popularity'
  direction: 'asc' | 'desc'
}

export interface ProductComparison {
  products: Product[]
  categories: string[]
}
