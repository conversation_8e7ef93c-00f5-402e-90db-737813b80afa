export interface Product {
  id: string
  name: string
  tagline: string
  description: string
  image: string
  badge?: string
  specs: ProductSpec[]
  features: string[]
  link: string
  category: ProductCategory
}

export interface ProductSpec {
  icon: any // Lucide icon component
  label: string
  value: string
}

export type ProductCategory = 
  | "single-board-computer"
  | "compute-module"
  | "accessory"
  | "development-kit"

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  image: string
  date: string
  author: Author
  category: BlogCategory
  tags: string[]
  link: string
  featured: boolean
  readingTime: number
}

export interface Author {
  name: string
  avatar?: string
  bio?: string
  social?: {
    twitter?: string
    github?: string
    linkedin?: string
  }
}

export type BlogCategory = 
  | "Product Launch"
  | "Tutorial"
  | "Community"
  | "News"
  | "Project Showcase"
  | "Technical"

export interface Project {
  id: string
  title: string
  description: string
  image: string
  author: string
  difficulty: ProjectDifficulty
  tags: string[]
  link: string
  github?: string
  featured: boolean
  category: ProjectCategory
  components: string[]
  estimatedTime?: string
}

export type ProjectDifficulty = 
  | "Beginner"
  | "Intermediate" 
  | "Advanced"
  | "Expert"

export type ProjectCategory =
  | "IoT"
  | "Robotics"
  | "Gaming"
  | "AI/ML"
  | "Industrial"
  | "Education"
  | "Home Automation"
  | "Entertainment"

export interface Accessory {
  id: string
  name: string
  description: string
  image: string
  link: string
  features: string[]
  compatibility: string[]
  category: AccessoryCategory
}

export type AccessoryCategory =
  | "Carrier Board"
  | "Display"
  | "Storage"
  | "Connectivity"
  | "Power"
  | "Case"
  | "Sensor"
  | "Development Tool"

export interface NavigationItem {
  name: string
  href: string
  children?: NavigationItem[]
  external?: boolean
}

export interface SocialLink {
  name: string
  href: string
  icon: any // Lucide icon component
}

export interface HeroSlide {
  id: number
  title: string
  subtitle: string
  description: string
  image: string
  ctaText: string
  ctaLink: string
  badge?: string
}

export interface NewsItem {
  id: number
  title: string
  excerpt: string
  image: string
  date: string
  category: string
  link: string
  featured: boolean
}

export interface DocumentationPage {
  id: string
  title: string
  content: string
  slug: string
  category: string
  subcategory?: string
  order: number
  lastUpdated: string
  tags: string[]
  toc: TableOfContentsItem[]
}

export interface TableOfContentsItem {
  id: string
  title: string
  level: number
  children?: TableOfContentsItem[]
}

export interface SearchResult {
  id: string
  title: string
  excerpt: string
  url: string
  type: "page" | "product" | "blog" | "docs"
  category?: string
}

export interface APIResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface FilterOptions {
  category?: string[]
  tags?: string[]
  difficulty?: ProjectDifficulty[]
  sortBy?: "name" | "date" | "popularity"
  sortOrder?: "asc" | "desc"
}

// Form types
export interface ContactForm {
  name: string
  email: string
  subject: string
  message: string
  company?: string
  phone?: string
}

export interface NewsletterForm {
  email: string
  preferences?: string[]
}

export interface ProjectSubmissionForm {
  title: string
  description: string
  author: string
  email: string
  github?: string
  images: File[]
  difficulty: ProjectDifficulty
  category: ProjectCategory
  tags: string[]
  components: string[]
  instructions: string
}

// SEO types
export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url?: string
  type?: "website" | "article" | "product"
  publishedTime?: string
  modifiedTime?: string
  author?: string
}

// Company types
export interface Company {
  id: string
  documentId: string
  companyName: string
  companySlogan: string
  companyDescription: string
  businessPhone: string
  businessHours: string
  businessEmail: string
  techPhone: string
  techHours: string
  techEmail: string
  address: string
  detailedAddress: string
  postalCode: string
  logo?: {
    url: string
    alternativeText?: string
  }
  wechatQR?: {
    url: string
    alternativeText?: string
  }
  contactQR?: {
    url: string
    alternativeText?: string
  }
  createdAt: string
  updatedAt: string
  publishedAt: string
}

// Theme types
export type Theme = "light" | "dark" | "system"

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
}
