import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'
  const isProduction = process.env.NODE_ENV === 'production'

  // 在生产环境中，允许所有爬虫访问，并指定禁止访问的路径
  const allowAll = {
    userAgent: '*',
    allow: '/',
    disallow: [
      '/dashboard/',
      '/auth/',
      '/api/',
      '/admin/',
      '/_next/',
      '/private/',
      '/test-*', // 禁止索引所有测试页面
    ],
  }

  // 在非生产环境中，禁止所有爬虫访问
  const disallowAll = {
    userAgent: '*',
    disallow: '/',
  }

  return {
    rules: isProduction ? allowAll : disallowAll,
    sitemap: `${siteUrl}/sitemap.xml`,
    host: new URL(siteUrl).host,
  }
}
