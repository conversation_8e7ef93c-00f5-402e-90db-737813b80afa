'use client'

import { useAboutTranslations } from '@/hooks/use-translations'
import { Shield, Settings, FileText, Clock, Users, Target, Lightbulb, Award, Globe, TrendingUp, Cpu, Zap } from 'lucide-react'
import { useCompanyInfo } from '@/hooks/use-api'
import { ScrollReveal } from '@/components/home/<USER>'
import { motion } from 'framer-motion'
import { usePageMetadata } from '@/hooks/use-page-metadata'


export default function AboutPage() {
  const { t } = useAboutTranslations()

  // 设置页面元数据
  usePageMetadata()

  // 获取动态公司信息
  const { data: companyInfo, isLoading: isCompanyLoading } = useCompanyInfo()

  // 使用动态数据或fallback到默认值
  const contactInfo = companyInfo ? {
    businessPhone: companyInfo.businessPhone,
    businessHours: companyInfo.businessHours,
    businessEmail: companyInfo.businessEmail,
    techPhone: companyInfo.techPhone,
    techHours: companyInfo.techHours,
    address: companyInfo.address,
    detailedAddress: companyInfo.detailedAddress,
    companyName: companyInfo.companyName,
    companySlogan: companyInfo.companySlogan,
  } : {
    // Fallback默认值
    businessPhone: "+86-152-7791-5606",
    businessHours: "(Mon-Fri 9:00-18:00)",
    businessEmail: "<EMAIL>",
    techPhone: "+86-755-8888-8888",
    techHours: "(Mon-Fri 9:00-18:00)",
    address: "Shenzhen, Guangdong, China",
    detailedAddress: "Linnuo Technology Co., Ltd.\nInnovation District\nShenzhen, Guangdong Province, China",
    companyName: "Linnuo",
    companySlogan: "Embedded Computing Solutions",
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section - 与application页面保持一致的背景图样式 */}
      <section
        className="relative py-16 overflow-hidden"
        style={{
          backgroundImage: 'url(/images/background/about-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          height: '400px'
        }}
      >
        <div className="relative container mx-auto px-4 h-full flex items-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white w-full"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">
              {t("title")}
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              {t("intro")}
            </p>
          </motion.div>
        </div>
      </section>

      {/* 主要内容区域 - 左侧办公楼图片，右侧公司介绍和统计数据 */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
            {/* 左侧办公楼图片 */}
            <ScrollReveal direction="left" delay={0.1}>
              <div className="relative">
                <div 
                  className="w-full h-[500px] lg:h-[600px] shadow-xl relative overflow-hidden"
                  style={{
                    backgroundImage: 'url(/images/company.png)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat'
                  }}
                >
                  {/* <div className="absolute inset-0 bg-black/30"></div>
                  <div className="relative z-10 h-full flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-32 h-32 bg-white/20 flex items-center justify-center mx-auto mb-4 rounded-lg backdrop-blur-sm">
                        <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                          <path d="M9 12l2 2 4-4"/>
                        </svg>
                      </div>
                    </div>
                  </div> */}
                </div>
              </div>
            </ScrollReveal>

            {/* 右侧公司介绍和统计数据 */}
            <ScrollReveal direction="right" delay={0.2}>
              <div className="space-y-8">
                {/* 公司标题 */}
                <div>
                  <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
                    {contactInfo.companyName}
                  </h2>
                  <div className="w-16 h-1 bg-blue-600 mb-6"></div>
                </div>

                {/* 公司介绍文字 */}
                <div className="space-y-4 text-gray-600 leading-relaxed">
                  <p>
                    {t("story.content")}
                  </p>
                  <p>
                    {t("products.content")}
                  </p>
                  <p>
                    {t("mission.content")}
                  </p>
                </div>

                {/* 统计数据 */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 pt-8">
                  <StatCard icon={Shield} number="50+" label="Patents" />
                  <StatCard icon={Settings} number="30+" label="Teammates" />
                  <StatCard icon={FileText} number="6000+" label="Clients Served" />
                  <StatCard icon={Clock} number="9+" label="Years of Experience" />
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* 企业文化部分 - 6张图片网格布局 */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="up" delay={0.1}>
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">{t("values.title")}</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {t("team.content")}
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <CultureCard
              bgColor="bg-gradient-to-br from-blue-500 to-blue-600"
              title={t("values.collaboration.title")}
              subtitle="Teamwork"
              description={t("values.collaboration.content")}
              icon={Users}
              backgroundImage="/images/about/teamwork-collaboration.jpg"
            />
            <CultureCard
              bgColor="bg-gradient-to-br from-purple-500 to-purple-600"
              title={t("values.innovation.title")}
              subtitle="Innovation"
              description={t("values.innovation.content")}
              icon={Lightbulb}
              backgroundImage="/images/about/innovation-technology.jpg"
            />
            <CultureCard
              bgColor="bg-gradient-to-br from-green-500 to-green-600"
              title={t("values.quality.title")}
              subtitle="Quality First"
              description={t("values.quality.content")}
              icon={Award}
              backgroundImage="/images/about/quality-excellence.jpg"
            />
            <CultureCard
              bgColor="bg-gradient-to-br from-orange-500 to-orange-600"
              title={t("values.customer.title")}
              subtitle="Customer Service"
              description={t("values.customer.content")}
              icon={Target}
              backgroundImage="/images/about/customer-service.jpg"
            />
            <CultureCard
              bgColor="bg-gradient-to-br from-teal-500 to-teal-600"
              title={t("vision.title")}
              subtitle="Development"
              description={t("vision.content")}
              icon={TrendingUp}
              backgroundImage="/images/about/development-vision.jpg"
            />
            <CultureCard
              bgColor="bg-gradient-to-br from-indigo-500 to-indigo-600"
              title={t("values.partnership.title")}
              subtitle="Future Together"
              description={t("values.partnership.content")}
              icon={Globe}
              backgroundImage="/images/about/future-together.jpg"
            />
          </div>
        </div>
      </section>

    </div>
  )
}

// StatCard 组件
interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>
  number: string
  label: string
}

function StatCard({ icon: Icon, number, label }: StatCardProps) {
  return (
    <div className="text-center">
      <div className="w-16 h-16 bg-blue-600 flex items-center justify-center mx-auto mb-4">
        <Icon className="w-8 h-8 text-white" />
      </div>
      <div className="text-2xl lg:text-3xl font-bold text-gray-800 mb-2">
        {number}
      </div>
      <div className="text-gray-600 text-sm">
        {label}
      </div>
    </div>
  )
}

// CultureCard 组件
interface CultureCardProps {
  bgColor: string
  title: string
  subtitle: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  backgroundImage?: string
}

function CultureCard({ bgColor, title, subtitle, description, icon: Icon, backgroundImage }: CultureCardProps) {
  return (
    <ScrollReveal direction="up" delay={0.1}>
      <div className="group cursor-pointer">
        <div 
          className={`relative overflow-hidden mb-4 h-64 ${bgColor} flex items-center justify-center transition-transform duration-500 group-hover:scale-105`}
          style={backgroundImage ? {
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          } : {}}
        >
          <div className="text-center text-white relative z-10">
            <div className="w-16 h-16 bg-white/30 backdrop-blur-sm flex items-center justify-center mx-auto mb-4 rounded-lg">
              <Icon className="w-8 h-8" />
            </div>
            <h3 className="text-lg font-bold mb-1 drop-shadow-lg">{title}</h3>
            <p className="text-sm opacity-90 drop-shadow-lg">{subtitle}</p>
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent"></div>
        </div>
        <p className="text-gray-600 text-sm leading-relaxed">
          {description}
        </p>
      </div>
    </ScrollReveal>
  )
}
