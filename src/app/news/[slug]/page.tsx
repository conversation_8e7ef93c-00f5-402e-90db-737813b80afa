import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { getNewsArticles, getNewsArticleBySlug } from '@/lib/api/news'
import RichTextRenderer from '@/components/ui/rich-text-renderer'

import {
  Calendar,
  Clock,
  ArrowLeft,
  User,
  Share2
} from 'lucide-react'

interface NewsDetailPageProps {
  params: {
    slug: string
  }
}

// 生成静态参数用于静态导出
export async function generateStaticParams() {
  // 为了确保静态导出的可靠性，直接返回预定义的新闻slug
  // 这样可以避免在构建时依赖外部API
  return [
    { slug: 'Linnuo-sigma-ces-2024-innovation-award' },
    { slug: 'Linnuo-sigma-intel-partner-award' },
    { slug: 'building-iot-solutions-Linnuo-mu' },
  ]
}

export default async function NewsDetailPage({ params }: NewsDetailPageProps) {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 新闻详情页面 - 请求的 slug:', params.slug)
  }

  const post = await getNewsArticleBySlug(params.slug, 'en') // 使用英文作为默认语言

  if (process.env.NODE_ENV === 'development') {
    console.log('📦 获取到的新闻数据:', post ? { id: post.id, title: post.title, slug: post.slug } : null)
  }

  if (!post) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ 新闻未找到，显示 404 页面')
    }
    notFound()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      'announcements': 'bg-blue-500',
      'tutorials': 'bg-green-500',
      'projects': 'bg-purple-500',
      'technical': 'bg-orange-500'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-500'
  }

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    "headline": post.title,
    "image": [post.image],
    "datePublished": post.date,
    "dateModified": post.date,
    "author": [{
      "@type": "Person",
      "name": post.author,
      "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/about`
    }],
    "publisher": {
      "@type": "Organization",
      "name": "Linnuo",
      "logo": {
        "@type": "ImageObject",
        "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/logo-top-bg-white.svg`
      }
    },
    "description": post.excerpt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/news/${post.slug}`
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-background">
        {/* Header */}
        <section className="py-12 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Button asChild variant="outline" className="mb-6">
              <Link href="/news">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回新闻列表
              </Link>
            </Button>

            <div className="mb-6">
              <Badge 
                className={`${getCategoryColor(post.category)} text-white border-0 mb-4`}
              >
                {post.category}
              </Badge>
              
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {post.title}
              </h1>

              <div className="flex items-center gap-6 text-gray-600 mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {formatDate(post.date)}
                </div>
                {/* `<div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  5 min read
                </div>` */}
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  {post.author}
                </div>
              </div>

              <p className="text-xl text-gray-600">
                {post.excerpt}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="relative h-64 lg:h-96 overflow-hidden shadow-xl">
              <Image
                src={post.image || '/images/background/prod_bg.jpg'}
                alt={post.title}
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-8">
                <RichTextRenderer
                  content={post.content}
                  className="prose prose-lg max-w-none"
                />

                {/* Category Badge */}
                <div className="mt-8 pt-8 border-t">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">分类:</span>
                    <Badge variant="outline">
                      {post.category}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related Posts */}
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">相关文章</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 暂时注释掉相关文章功能，避免构建错误 */}
              {/*
              {relatedPosts
                .filter(p => p.id !== post.id && p.category === post.category)
                .slice(0, 2)
                .map((relatedPost) => (
                  <Card key={relatedPost.id} className="group hover:shadow-lg transition-all duration-300">
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={relatedPost.featuredImage?.url || '/images/background/prod_bg.jpg'}
                        alt={relatedPost.featuredImage?.alt || relatedPost.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-2 group-hover:text-Linnuo-orange transition-colors">
                        {relatedPost.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {relatedPost.excerpt}
                      </p>
                      <Button asChild size="sm" className="bg-Linnuo-orange hover:bg-Linnuo-orange/90">
                        <Link href={`/news/${relatedPost.slug}`}>
                          阅读更多
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
                */}
              <div className="text-center text-gray-500">
                <p>相关文章功能正在开发中...</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    </>
  )
}
