"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { getNewsArticles } from "@/data/unified-data-fetcher"
import { NewsArticle } from "@/lib/api/news"
import { useNewsTranslations, useCommonTranslations } from "@/hooks/use-translations"
import { useLanguage } from "@/contexts/simple-language-context"
import { usePageMetadata } from "@/hooks/use-page-metadata"
import {
  Calendar,
  Clock,
  User,
  Search,
  ArrowRight,
  ExternalLink,
  Newspaper,
  TrendingUp
} from "lucide-react"
// 类别配置将在组件内部使用翻译函数动态生成

export default function NewsPage() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [searchQuery, setSearchQuery] = useState("")
  const [mounted, setMounted] = useState(false)
  const [newsArticles, setNewsArticles] = useState<NewsArticle[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const { t: newsT } = useNewsTranslations()
  const { t: commonT } = useCommonTranslations()
  const { language } = useLanguage()

  // 设置页面元数据
  usePageMetadata()

  // 动态生成类别配置
  const categories = [
    { key: "All", label: newsT("categories.all") },
    { key: "Product", label: newsT("categories.product") },
    { key: "Launch", label: newsT("categories.launch") },
    { key: "Awards", label: newsT("categories.awards") },
    { key: "Tutorial", label: newsT("categories.tutorial") },
    { key: "Company News", label: newsT("categories.companyNews") },
    { key: "Technology", label: newsT("categories.technology") }
  ]

  // 获取新闻数据
  useEffect(() => {
    setMounted(true)

    const fetchNews = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 新闻页面获取数据，语言:', language)
        }
        const articles = await getNewsArticles(language)
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 新闻数据获取成功:', articles.length, '篇文章')
        }
        setNewsArticles(articles)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 新闻数据获取失败:', error)
        }
        setNewsArticles([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchNews()
  }, [language]) // 依赖语言变化重新加载

  // 过滤新闻
  const filteredNews = (newsArticles || []).filter(article => {
    const matchesCategory = selectedCategory === "All" || article.category === selectedCategory
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // 在组件挂载前或数据加载时显示加载状态
  if (!mounted || isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <section className="relative py-24 lg:py-32 bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center" style={{
         backgroundImage: 'url(/images/background/about-bg.jpg)',
         backgroundSize: 'cover',
         backgroundPosition: 'center',
         backgroundRepeat: 'no-repeat'
       }}>
          <div className="container mx-auto px-4 relative z-10">
           <div className="text-center mb-12">
             <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 drop-shadow-lg">
               {newsT("title")}
             </h1>
             <p className="text-xl text-white max-w-3xl mx-auto drop-shadow-md">
               {newsT("description")}
             </p>
           </div>
         </div>
        </section>
        <div className="container-custom py-20">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-b-2 border-primary rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">{newsT("loadingNews")}</p>
          </div>
        </div>
      </div>
    )
  }

  // 如果组件未挂载，显示加载状态
  if (!mounted) {
    return (
      <div className="min-h-screen bg-background">
        <section className="relative py-20 bg-gradient-to-br from-Linnuo-orange/10 to-Linnuo-orange/5">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center">
              <div className="w-16 h-16 bg-Linnuo-orange flex items-center justify-center mx-auto mb-6">
                <Newspaper className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                {newsT("title")}
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {newsT("description")}
              </p>
            </div>
          </div>
        </section>
        <div className="container-custom py-20">
          <div className="text-center">
            <p className="text-red-500 mb-4">Failed to load news articles.</p>
            <p className="text-muted-foreground">Please try again later.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-24 lg:py-32 bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center" style={{
         backgroundImage: 'url(/images/background/about-bg.jpg)',
         backgroundSize: 'cover',
         backgroundPosition: 'center',
         backgroundRepeat: 'no-repeat'
       }}>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 drop-shadow-lg">
              {newsT("title")}
            </h1>
            <p className="text-xl text-white max-w-3xl mx-auto drop-shadow-md">
              {newsT("description")}
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 border-b">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={newsT("searchPlaceholder")}
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.key}
                  variant={selectedCategory === category.key ? "default" : "outline"}
                  size="sm"
                  className={selectedCategory === category.key ? "bg-Linnuo-orange hover:bg-Linnuo-orange/90" : ""}
                  onClick={() => setSelectedCategory(category.key)}
                >
                  {category.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* News Articles */}
      <section className="py-20">
        <div className="container-custom">
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{newsT("title")}</h2>
            <p className="text-xl text-muted-foreground">
              {newsT("subtitle")}
            </p>
          </div>

          {filteredNews.length === 0 ? (
            <div className="text-center py-20">
              <Newspaper className="h-16 w-16 text-muted-foreground/50 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{newsT("noNewsFound")}</h3>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredNews.map((article, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 min-h-[300px]">
                  {/* Left side - Content */}
                  <div className="p-6 lg:p-8 flex flex-col justify-center order-2 lg:order-1">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge className="bg-Linnuo-orange hover:bg-Linnuo-orange/90 text-white">
                        {article.category}
                      </Badge>
                    </div>
                    <h3 className="text-xl lg:text-2xl font-bold mb-4 group-hover:text-Linnuo-orange transition-colors">
                      {article.title}
                    </h3>
                    <p className="text-muted-foreground mb-6 text-base lg:text-lg leading-relaxed">
                      {article.content ? article.content.substring(0, 30) + '...' : article.excerpt?.substring(0, 30) + '...'}
                    </p>
                    <div className="flex flex-wrap items-center gap-3 lg:gap-4 text-sm text-muted-foreground mb-6">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span className="truncate">{article.author}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(article.date).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        5 {newsT("minRead")}
                      </div>
                    </div>
                    <Button asChild variant="outline" className="w-fit hover:bg-Linnuo-orange hover:text-white transition-colors">
                      <Link href={`/news/${article.slug}`}>
                        {newsT("readMore")}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>

                  {/* Right side - Image */}
                  <div className="aspect-video lg:aspect-auto order-1 lg:order-2 min-h-[200px] lg:min-h-[300px] relative overflow-hidden">
                    {article.image ? (
                      <Image
                        src={article.image}
                        alt={article.title}
                        fill
                        className="object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="bg-gradient-to-br from-Linnuo-orange/20 to-Linnuo-orange/10 flex items-center justify-center h-full">
                        <div className="text-center">
                          <TrendingUp className="h-12 w-12 lg:h-16 lg:w-16 text-Linnuo-orange/60 mx-auto mb-2" />
                          <p className="text-sm text-Linnuo-orange/60 font-medium">{newsT("newsImage")}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Load More */}
      {filteredNews.length > 5 && (
        <section className="py-12">
          <div className="container-custom">
            <div className="text-center">
              <Button variant="outline" size="lg">
                {newsT("loadMoreArticles")}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Newsletter Signup */}
      <section className="py-20">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {newsT("newsletter.title")}
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              {newsT("newsletter.description")}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder={newsT("newsletter.emailPlaceholder")}
                className="flex-1"
              />
              <Button className="bg-Linnuo-orange hover:bg-Linnuo-orange/90">
                {newsT("newsletter.subscribe")}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              {newsT("newsletter.privacyNote")}
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
