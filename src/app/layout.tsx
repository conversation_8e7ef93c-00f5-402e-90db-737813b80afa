import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

import { ServiceWorkerProvider } from '@/components/providers/service-worker-provider'
import { ImageOptimizationProvider } from '@/components/providers/image-optimization-provider'
import { SearchProvider } from '@/contexts/search-context'
import { SimpleLanguageProvider } from '@/contexts/simple-language-context'
import { SearchModal } from '@/components/search/search-modal'
import { QueryProvider } from '@/providers/query-provider'
import { LoadingProvider } from '@/components/providers/loading-provider'
import { TranslationLoading } from '@/components/ui/translation-loading'
import { CoreWebVitalsOptimizer } from '@/components/optimization/core-web-vitals'
import { InternationalSEO } from '@/components/seo/international-seo'
import { StaticSEOHead } from '@/components/seo/seo-head'


const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Linnuo - x86 Windows/Linux Single Board Computers',
    template: '%s | Linnuo'
  },
  description: 'Linnuois a complete Windows/Linux device in a single board computer that can run almost any x86 software. Perfect for IoT, robotics, and embedded applications.',
  keywords: [
    'Linnuo',
    'single board computer',
    'SBC',
    'x86',
    'Windows',
    'Linux',
    'IoT',
    'robotics',
    'embedded',
    'development board'
  ],
  authors: [{ name: 'LinnuoTeam' }],
  creator: 'Linnuo',
  publisher: 'Linnuo',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://Linnuo-clone.vercel.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://Linnuo-clone.vercel.app',
    title: 'Linnuo- x86 Windows/Linux Single Board Computers',
    description: 'Linnuois a complete Windows/Linux device in a single board computer that can run almost any x86 software.',
    siteName: 'Linnuo',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'LinnuoSingle Board Computer',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Linnuo- x86 Windows/Linux Single Board Computers',
    description: 'Linnuois a complete Windows/Linux device in a single board computer that can run almost any x86 software.',
    images: ['/images/twitter-image.jpg'],
    creator: '@LinnuoCN',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon-new.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#FF6B35" />
        <meta name="msapplication-TileColor" content="#FF6B35" />

        {/* Favicon 配置 - 使用新的 logo favicon */}
        <link rel="icon" type="image/x-icon" href="/favicon-new.ico" />
        <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
        <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
        <link rel="shortcut icon" href="/favicon-new.ico" />

        <StaticSEOHead />
      </head>
      <body className={inter.className}>
        <CoreWebVitalsOptimizer />
        <SimpleLanguageProvider>
          <InternationalSEO />
          <QueryProvider>
            <LoadingProvider>
              <ThemeProvider
                attribute="class"
                defaultTheme="light"
                enableSystem
                disableTransitionOnChange
              >
                <SearchProvider>
                  <ServiceWorkerProvider>
                    <ImageOptimizationProvider>
                      <Header />
                      <main className="min-h-screen">
                        {children}
                      </main>
                      <Footer />
                      <SearchModal />
                      <TranslationLoading />
                    </ImageOptimizationProvider>
                  </ServiceWorkerProvider>
                </SearchProvider>
              </ThemeProvider>
            </LoadingProvider>
          </QueryProvider>
        </SimpleLanguageProvider>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Linnuo",
              "url": "https://Linnuo-clone.vercel.app",
              "logo": "https://Linnuo-clone.vercel.app/logo-top-bg-white.svg",
              "sameAs": [
                "https://twitter.com/LinnuoCN",
                "https://www.linkedin.com/company/Linnuo",
                "https://www.facebook.com/Linnuo"
              ]
            })
          }}
        />
      </body>
    </html>
  )
}
