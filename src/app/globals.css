@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography System - 基于 futurerobottech.com 的字体设计理念 */

/* 字体预设类 */
@layer components {
  /* 标题字体样式 */
  .text-heading-1 {
    @apply font-display text-responsive-5xl font-bold leading-tight tracking-tight;
  }
  
  .text-heading-2 {
    @apply font-display text-responsive-4xl font-bold leading-tight tracking-tight;
  }
  
  .text-heading-3 {
    @apply font-display text-responsive-3xl font-semibold leading-snug;
  }
  
  .text-heading-4 {
    @apply font-display text-responsive-2xl font-semibold leading-snug;
  }
  
  .text-heading-5 {
    @apply font-display text-responsive-xl font-medium leading-normal;
  }
  
  .text-heading-6 {
    @apply font-display text-responsive-lg font-medium leading-normal;
  }
  
  /* 正文字体样式 */
  .text-body-large {
    @apply font-sans text-responsive-lg font-normal leading-relaxed;
  }
  
  .text-body {
    @apply font-sans text-responsive-base font-normal leading-relaxed;
  }
  
  .text-body-small {
    @apply font-sans text-responsive-sm font-normal leading-relaxed;
  }
  
  /* 特殊用途字体 */
  .text-caption {
    @apply font-sans text-responsive-xs font-normal leading-normal text-gray-600;
  }
  
  .text-label {
    @apply font-sans text-responsive-sm font-medium leading-normal;
  }
  
  .text-button {
    @apply font-sans text-responsive-sm font-semibold leading-none tracking-wide;
  }
  
  .text-link {
    @apply font-sans text-responsive-base font-medium leading-normal text-blue-600 hover:text-blue-800 transition-colors;
  }
  
  /* 代码字体 */
  .text-code {
    @apply font-mono text-responsive-sm leading-relaxed;
  }
  
  .text-code-inline {
    @apply font-mono text-responsive-xs bg-gray-100 px-1 py-0.5 rounded;
  }
}

/* 响应式字体工具类 */
@layer utilities {
  /* 移动端优化 */
  @media (max-width: 640px) {
    .text-mobile-heading {
      @apply text-2xl font-bold leading-tight;
    }
    
    .text-mobile-subheading {
      @apply text-xl font-semibold leading-snug;
    }
    
    .text-mobile-body {
      @apply text-base leading-relaxed;
    }
  }
  
  /* 平板端优化 */
  @media (min-width: 641px) and (max-width: 1024px) {
    .text-tablet-heading {
      @apply text-3xl font-bold leading-tight;
    }
    
    .text-tablet-subheading {
      @apply text-2xl font-semibold leading-snug;
    }
    
    .text-tablet-body {
      @apply text-lg leading-relaxed;
    }
  }
  
  /* 桌面端优化 */
  @media (min-width: 1025px) {
    .text-desktop-heading {
      @apply text-5xl font-bold leading-tight;
    }
    
    .text-desktop-subheading {
      @apply text-3xl font-semibold leading-snug;
    }
    
    .text-desktop-body {
      @apply text-xl leading-relaxed;
    }
  }
}

/* 全局字体优化 */
@layer base {
  body {
    font-family: "Microsoft YaHei", "Open Sans", "Inter", "Arial", sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 中文字体优化 */
  :lang(zh) {
    font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  }
  
  /* 英文字体优化 */
  :lang(en) {
    font-family: "Open Sans", "Inter", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif;
  }
}

/* Background patterns */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Carousel animations */
@keyframes progress {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Custom responsive utilities */
@layer utilities {
  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }

  /* Touch-friendly tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile-first responsive text */
  .text-responsive-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  @screen sm {
    .text-responsive-xs {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
  }

  .text-responsive-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  @screen sm {
    .text-responsive-sm {
      font-size: 1rem;
      line-height: 1.5rem;
    }
  }

  .text-responsive-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @screen sm {
    .text-responsive-base {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  /* Container queries support */
  .container-xs {
    container-type: inline-size;
  }

  @container (min-width: 320px) {
    .cq-xs\:block {
      display: block;
    }
  }

  @container (min-width: 640px) {
    .cq-sm\:flex {
      display: flex;
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 215 89% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 215 89% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 215 89% 58%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  }
  
  .gradient-bg-blue {
    background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
  }

  /* Glass effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Industrial grid pattern */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Industrial tech animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
  }

  @keyframes slide-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-slide-in-up {
    animation: slide-in-up 0.6s ease-out;
  }

  /* Industrial card styles */
  .industrial-card {
    @apply bg-white/90 backdrop-blur-sm border border-slate-200/50 shadow-lg hover:shadow-2xl transition-all duration-500;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  }

  .industrial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  }

  /* Tech gradient backgrounds */
  .tech-gradient-blue {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
  }

  .tech-gradient-dark {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  /* Button hover effects */
  .btn-hover-lift {
    @apply transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-lg;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:transform hover:-translate-y-2;
  }

  /* Text gradient */
  .text-gradient {
    @apply bg-gradient-to-r from-Linnuo-orange to-Linnuo-orange-light bg-clip-text text-transparent;
  }

  /* Navigation styles - 优化以防止布局偏移 */
  .nav-link {
    @apply relative text-gray-700 hover:text-Linnuo-orange transition-colors duration-200;
    /* 防止文本变化时的布局偏移 */
    min-height: 1.5rem;
    display: flex;
    align-items: center;
    /* 添加固定的最小宽度和居中对齐，防止跳动 */
    min-width: fit-content;
    white-space: nowrap;
    justify-content: center;
    padding: 0.5rem 0.75rem;
  }

  .nav-link::after {
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-Linnuo-orange transition-all duration-300;
    content: '';
    /* 使用 transform 而不是 width 来避免重排 */
    width: 100%;
    transform: scaleX(0);
    transform-origin: left;
  }

  .nav-link:hover::after {
    transform: scaleX(1);
  }

  /* 防止语言切换时的布局偏移 */
  .language-selector {
    min-width: 80px;
    transition: opacity 0.15s ease-in-out;
  }

  .language-selector.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  /* Dropdown menu animations - 优化性能 */
  .dropdown-enter {
    @apply opacity-0;
    transform: translateY(-8px) scale(0.95);
  }

  .dropdown-enter-active {
    @apply opacity-100;
    transform: translateY(0) scale(1);
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  }

  .dropdown-exit {
    @apply opacity-100;
    transform: translateY(0) scale(1);
  }

  .dropdown-exit-active {
    @apply opacity-0;
    transform: translateY(-8px) scale(0.95);
    transition: opacity 0.1s ease-in, transform 0.1s ease-in;
  }

  /* 页面过渡优化 */
  .page-transition {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  }

  .page-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Code block styles */
  .code-block {
    @apply bg-gray-900 text-gray-100 p-4 overflow-x-auto;
  }
  
  .code-block pre {
    @apply m-0;
  }
  
  .code-block code {
    @apply text-sm font-mono;
  }

  /* Documentation styles */
  .docs-content {
    @apply prose prose-lg max-w-none;
  }
  
  .docs-content h1 {
    @apply text-3xl font-bold text-gray-900 dark:text-white mb-6;
  }
  
  .docs-content h2 {
    @apply text-2xl font-semibold text-gray-800 dark:text-gray-200 mt-8 mb-4;
  }
  
  .docs-content h3 {
    @apply text-xl font-medium text-gray-700 dark:text-gray-300 mt-6 mb-3;
  }
  
  .docs-content p {
    @apply text-gray-600 dark:text-gray-400 leading-relaxed mb-4;
  }
  
  .docs-content ul {
    @apply list-disc list-inside text-gray-600 dark:text-gray-400 mb-4;
  }
  
  .docs-content ol {
    @apply list-decimal list-inside text-gray-600 dark:text-gray-400 mb-4;
  }
  
  .docs-content li {
    @apply mb-2;
  }
  
  .docs-content a {
    @apply text-Linnuo-orange hover:text-Linnuo-orange-dark underline;
  }
  
  .docs-content blockquote {
    @apply border-l-4 border-Linnuo-orange bg-orange-50 dark:bg-orange-900/20 p-4 my-4;
  }
  
  .docs-content table {
    @apply w-full border-collapse border border-gray-300 dark:border-gray-600 my-4;
  }
  
  .docs-content th {
    @apply bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-semibold;
  }
  
  .docs-content td {
    @apply border border-gray-300 dark:border-gray-600 px-4 py-2;
  }

  /* Hero section styles */
  .hero-gradient {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #1E3A8A 100%);
  }

  /* Product card styles */
  .product-card {
    @apply bg-white dark:bg-gray-800 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:transform hover:-translate-y-2;
  }

  /* Feature section styles */
  .feature-icon {
    @apply w-12 h-12 bg-Linnuo-orange text-white flex items-center justify-center mb-4;
  }

  /* Blog card styles */
  .blog-card {
    @apply bg-white dark:bg-gray-800 shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg;
  }

  /* Responsive utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Animation keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-Linnuo-orange ring-offset-2;
}
