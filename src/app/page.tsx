'use client'

import { HeroSection } from "@/components/home/<USER>"
import { FeaturedProducts } from "@/components/home/<USER>"
import { FeatureHighlights } from "@/components/home/<USER>"
import { CommunitySection } from "@/components/home/<USER>"
import { NewsSection } from "@/components/home/<USER>"
import { OrganizationStructuredData, WebsiteStructuredData } from '@/components/seo/structured-data'
import { ScrollReveal } from '@/components/home/<USER>'
import { usePageMetadata } from '@/hooks/use-page-metadata'


export default function HomePage() {
  // 设置页面元数据
  usePageMetadata()

  return (
    <>
      <OrganizationStructuredData />
      <WebsiteStructuredData />
      

      
      {/* 主要内容区域 */}
      <main className="relative overflow-hidden">
        {/* 轮播图区域 */}
        <ScrollReveal direction="up" distance={80} duration={1.2} delay={0.1}>
          <div className="relative z-10">
            <HeroSection />
          </div>
        </ScrollReveal>

        {/* 内容区域 - 添加渐变背景和更好的间距 */}
        <div className="relative">


          {/* 特色产品 */}
          <ScrollReveal direction="up" distance={60} duration={1.0} delay={0.2}>
            <div className="relative z-10">
              <FeaturedProducts />
            </div>
          </ScrollReveal>

          {/* 功能亮点 */}
          <ScrollReveal direction="up" distance={60} duration={1.0} delay={0.3}>
            <div className="relative z-10">
              <FeatureHighlights />
            </div>
          </ScrollReveal>

          {/* 社区部分 */}
          <ScrollReveal direction="up" distance={60} duration={1.0} delay={0.4}>
            <div className="relative z-10">
              <CommunitySection />
            </div>
          </ScrollReveal>

          {/* 新闻部分 */}
          <ScrollReveal direction="up" distance={60} duration={1.0} delay={0.5}>
            <div className="relative z-10">
              <NewsSection />
            </div>
          </ScrollReveal>
        </div>
      </main>
    </>
  )
}
