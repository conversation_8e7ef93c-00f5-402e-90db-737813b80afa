import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证必填字段
    const requiredFields = ['company', 'contactName', 'email', 'projectType', 'requirements', 'privacyConsent']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields', 
          missingFields 
        },
        { status: 400 }
      )
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email format' 
        },
        { status: 400 }
      )
    }

    // 验证隐私政策同意
    if (!body.privacyConsent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Privacy policy consent is required' 
        },
        { status: 400 }
      )
    }

    // 这里将来会连接到实际的后台数据库
    // 目前只是记录请求信息用于监控

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里将来可以添加：
    // 1. 保存到数据库
    // 2. 发送确认邮件给客户
    // 3. 发送通知邮件给销售团队
    // 4. 集成CRM系统
    // 5. 生成工单号

    return NextResponse.json({
      success: true,
      message: 'Customization request submitted successfully',
      requestId: `CR-${Date.now()}`, // 临时的请求ID
      data: {
        company: body.company,
        contactName: body.contactName,
        email: body.email,
        projectType: body.projectType,
        submittedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    // Error handling - in production, this should be logged to a proper logging service

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
