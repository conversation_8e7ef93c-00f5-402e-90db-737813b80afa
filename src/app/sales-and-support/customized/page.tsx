'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Zap,
  Settings,
  Shield,
  DollarSign,
  MessageSquare,
  FileText,
  Search,
  TestTube,
  CheckCircle,
  Truck,
  Send,
  Loader2
} from 'lucide-react'
import { useCustomizedServiceTranslations } from '@/hooks/use-translations'

// 特性和流程数据将在组件内部动态生成

export default function CustomizedServicePage() {
  const { t } = useCustomizedServiceTranslations()

  // 动态生成特性数据
  const customizationFeatures = [
    {
      icon: Zap,
      title: t("features.quickProductization.title"),
      description: t("features.quickProductization.description")
    },
    {
      icon: Settings,
      title: t("features.hardwareSoftwareSupport.title"),
      description: t("features.hardwareSoftwareSupport.description")
    },
    {
      icon: Shield,
      title: t("features.supplyGuarantee.title"),
      description: t("features.supplyGuarantee.description")
    },
    {
      icon: DollarSign,
      title: t("features.costOptimization.title"),
      description: t("features.costOptimization.description")
    }
  ]

  // 动态生成服务流程数据
  const serviceProcess = [
    {
      icon: MessageSquare,
      step: '01',
      title: t("process.communication.title"),
      description: t("process.communication.description")
    },
    {
      icon: FileText,
      step: '02',
      title: t("process.proposal.title"),
      description: t("process.proposal.description")
    },
    {
      icon: Search,
      step: '03',
      title: t("process.evaluation.title"),
      description: t("process.evaluation.description")
    },
    {
      icon: TestTube,
      step: '04',
      title: t("process.trial.title"),
      description: t("process.trial.description")
    },
    {
      icon: CheckCircle,
      step: '05',
      title: t("process.confirmation.title"),
      description: t("process.confirmation.description")
    },
    {
      icon: Truck,
      step: '06',
      title: t("process.delivery.title"),
      description: t("process.delivery.description")
    }
  ]

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [formData, setFormData] = useState({
    company: '',
    contactName: '',
    email: '',
    phone: '',
    projectType: '',
    quantity: '',
    timeline: '',
    requirements: '',
    additionalInfo: '',
    privacyConsent: false
  })

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const response = await fetch('/api/customization-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        setSubmitStatus('success')
        // 重置表单
        setFormData({
          company: '',
          contactName: '',
          email: '',
          phone: '',
          projectType: '',
          quantity: '',
          timeline: '',
          requirements: '',
          additionalInfo: '',
          privacyConsent: false
        })
        // Request submitted successfully
      } else {
        throw new Error(result.error || 'Submission failed')
      }
    } catch (error) {
      // Handle submission error
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Background */}
      <section 
        className="relative bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center py-24 lg:py-32"
        style={{
          backgroundImage: "url('/images/background/about-bg.jpg')"
        }}
      >
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 drop-shadow-lg">
              {t("title")}
            </h1>
            <p className="text-xl text-white max-w-3xl mx-auto drop-shadow-md">
              {t("subtitle")}
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {customizationFeatures.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>


      {/* Service Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t("process.title")}
              </h2>
              <p className="text-gray-600 text-lg">
                {t("process.subtitle")}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {serviceProcess.map((process, index) => (
                <Card key={index} className="relative hover:shadow-lg transition-shadow">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-blue-600 flex items-center justify-center mx-auto mb-4">
                      <process.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="absolute top-4 right-4">
                      <Badge variant="outline" className="text-blue-600 border-blue-600">
                        {process.step}
                      </Badge>
                    </div>
                    <h3 className="font-semibold text-lg mb-2">{process.title}</h3>
                    <p className="text-gray-600 text-sm">{process.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Customization Request Form Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t("form.title")}
              </h2>
              <p className="text-gray-600 text-lg">
                {t("form.subtitle")}
              </p>
            </div>

            <Card>
              <CardContent className="p-8">
                {/* Success Message */}
                {submitStatus === 'success' && (
                  <div className="mb-6 p-4 bg-green-50 border border-green-200">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                      <p className="text-green-800 font-medium">
                        {t("form.successMessage")}
                      </p>
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {submitStatus === 'error' && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200">
                    <div className="flex items-center">
                      <div className="w-5 h-5 text-red-600 mr-2">⚠</div>
                      <p className="text-red-800 font-medium">
                        {t("form.errorMessage")}
                      </p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="company">{t("form.companyName")} *</Label>
                      <Input
                        id="company"
                        placeholder={t("form.placeholders.companyName")}
                        value={formData.company}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contact-name">{t("form.contactPerson")} *</Label>
                      <Input
                        id="contact-name"
                        placeholder={t("form.placeholders.contactPerson")}
                        value={formData.contactName}
                        onChange={(e) => handleInputChange('contactName', e.target.value)}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="email">{t("form.email")} *</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder={t("form.placeholders.email")}
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">{t("form.phone")}</Label>
                      <Input
                        id="phone"
                        placeholder={t("form.placeholders.phone")}
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="space-y-2">
                    <Label htmlFor="project-type">{t("form.projectType")} *</Label>
                    <Select
                      value={formData.projectType}
                      onValueChange={(value) => handleInputChange('projectType', value)}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t("form.placeholders.selectProjectType")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="odm">{t("form.projectTypes.odm")}</SelectItem>
                        <SelectItem value="oem">{t("form.projectTypes.oem")}</SelectItem>
                        <SelectItem value="custom-board">{t("form.projectTypes.customBoard")}</SelectItem>
                        <SelectItem value="software-dev">{t("form.projectTypes.softwareDev")}</SelectItem>
                        <SelectItem value="integration">{t("form.projectTypes.integration")}</SelectItem>
                        <SelectItem value="other">{t("form.projectTypes.other")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="quantity">{t("form.expectedQuantity")}</Label>
                      <Select
                        value={formData.quantity}
                        onValueChange={(value) => handleInputChange('quantity', value)}
                        disabled={isSubmitting}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("form.placeholders.selectQuantity")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-100">{t("form.quantities.1-100")}</SelectItem>
                          <SelectItem value="100-500">{t("form.quantities.100-500")}</SelectItem>
                          <SelectItem value="500-1000">{t("form.quantities.500-1000")}</SelectItem>
                          <SelectItem value="1000-5000">{t("form.quantities.1000-5000")}</SelectItem>
                          <SelectItem value="5000+">{t("form.quantities.5000+")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timeline">{t("form.timeline")}</Label>
                      <Select
                        value={formData.timeline}
                        onValueChange={(value) => handleInputChange('timeline', value)}
                        disabled={isSubmitting}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("form.placeholders.selectTimeline")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-3months">{t("form.timelines.1-3months")}</SelectItem>
                          <SelectItem value="3-6months">{t("form.timelines.3-6months")}</SelectItem>
                          <SelectItem value="6-12months">{t("form.timelines.6-12months")}</SelectItem>
                          <SelectItem value="12months+">{t("form.timelines.12months+")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="requirements">{t("form.requirements")} *</Label>
                    <Textarea
                      id="requirements"
                      placeholder={t("form.placeholders.requirements")}
                      rows={6}
                      value={formData.requirements}
                      onChange={(e) => handleInputChange('requirements', e.target.value)}
                      required
                      disabled={isSubmitting}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="additional-info">{t("form.additionalInfo")}</Label>
                    <Textarea
                      id="additional-info"
                      placeholder={t("form.placeholders.additionalInfo")}
                      rows={3}
                      value={formData.additionalInfo}
                      onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                      disabled={isSubmitting}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="privacy-policy"
                      checked={formData.privacyConsent}
                      onCheckedChange={(checked) => handleInputChange('privacyConsent', checked as boolean)}
                      required
                      disabled={isSubmitting}
                    />
                    <Label htmlFor="privacy-policy" className="text-sm">
                      {t("form.privacyConsent")} *
                    </Label>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      type="submit"
                      size="lg"
                      className="px-8"
                      disabled={isSubmitting || !formData.privacyConsent}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          {t("form.submitting")}
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          {t("form.submit")}
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
