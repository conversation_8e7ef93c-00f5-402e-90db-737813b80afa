"use client"

// React hooks 导入 - 用于状态管理和副作用处理
import { useState, useEffect } from "react"

// UI 组件导入 - 用于构建用户界面
import { Button } from "@/components/ui/button"  // 按钮组件
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"  // 卡片组件
import { Badge } from "@/components/ui/badge"  // 徽章组件，用于显示文件类型标签


// 图标导入 - 用于增强视觉效果和用户体验
import {
  Download,    // 下载图标
  FileText,    // 文档图标
  HardDrive,   // 硬盘图标，用于驱动程序
  Cpu,         // CPU图标，用于固件
  Code,        // 代码图标，用于软件
  Image,       // 图片图标，用于系统镜像
  Calendar,    // 日历图标，用于显示发布日期
  File,        // 文件图标，用于显示文件大小
  Monitor,     // 显示器图标，用于显示产品信息
  Star         // 星星图标，用于标记推荐文件
} from "lucide-react"

// API 和数据类型导入
// import { getDownloadFiles } from "@/data/unified-data-fetcher" // 暂时注释，下载功能待实现
import { type DownloadFile } from "@/lib/api"  // 下载文件的数据类型定义

// 国际化和语言相关导入
import { useDownloadsTranslations } from "@/hooks/use-translations"  // 下载页面的翻译钩子
import { useLanguage } from "@/contexts/simple-language-context"  // 语言上下文钩子
import { usePageMetadata } from "@/hooks/use-page-metadata"  // 页面元数据钩子

// 模拟下载数据 - 用于展示不同类型的下载文件
// 在实际应用中，这些数据应该从API或数据库获取

// 根据文件类型返回对应图标的函数
// 用于在下载列表中为不同类型的文件显示相应的图标
// 参数: type - 文件类型字符串
// 返回: JSX元素 - 对应的图标组件
const getFileIcon = (type: string) => {
  switch (type) {
    case 'driver':        // 驱动程序文件 - 蓝色硬盘图标
      return <HardDrive className="h-5 w-5 text-blue-600" />
    case 'documentation': // 文档文件 - 绿色文档图标
      return <FileText className="h-5 w-5 text-green-600" />
    case 'specification': // 规格说明文件 - 紫色文档图标
      return <FileText className="h-5 w-5 text-purple-600" />
    case 'firmware':      // 固件文件 - 红色CPU图标
      return <Cpu className="h-5 w-5 text-red-600" />
    case 'software':      // 软件文件 - 黄色代码图标
      return <Code className="h-5 w-5 text-yellow-600" />
    case 'image':         // 系统镜像文件 - 靛蓝色图片图标
      return <Image className="h-5 w-5 text-indigo-600" />
    default:              // 默认文件类型 - 灰色文档图标
      return <FileText className="h-5 w-5 text-gray-600" />
  }
}

// 根据文件类型返回对应的颜色样式类名
// 用于为文件类型徽章设置背景色和文字色，支持明暗主题
// 参数: type - 文件类型字符串
// 返回: string - Tailwind CSS类名字符串
const getTypeColor = (type: string) => {
  switch (type) {
    case 'driver':        // 驱动程序 - 蓝色主题
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'documentation': // 文档 - 绿色主题
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'specification': // 规格说明 - 紫色主题
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    case 'firmware':      // 固件 - 红色主题
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    case 'software':      // 软件 - 黄色主题
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'image':         // 系统镜像 - 靛蓝色主题
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
    default:              // 默认 - 灰色主题
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

export default function DownloadsPage() {
  const { t } = useDownloadsTranslations()
  const { language } = useLanguage()

  // 设置页面元数据
  usePageMetadata()

  const [downloadFiles, setDownloadFiles] = useState<DownloadFile[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)


  // 获取下载文件数据
  useEffect(() => {
    const fetchDownloadFiles = async () => {
      setLoading(true)
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 获取下载文件数据，语言:', language)
        }
        const { getDownloadFiles } = await import('@/lib/api')
        const files = await getDownloadFiles(language)
        setDownloadFiles(files)
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 下载文件数据获取成功:', files.length, '个文件')
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 获取下载文件数据失败:', error)
        }
        setDownloadFiles([])
      } finally {
        setLoading(false)
      }
    }

    fetchDownloadFiles()
  }, [language])

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null // 避免服务端和客户端渲染不一致
  }

  // 渲染下载页面的主要内容
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section
        className="relative py-16 overflow-hidden"
        style={{
          backgroundImage: 'url(/images/background/about-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          height: '400px'
        }}
      >
        <div className="relative container-custom h-full flex items-center">
          <div className="text-center text-white w-full">

            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">
              {t("title")} & <span className="text-Linnuo-orange">{t("resources")}</span>
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              {t("subtitle")}
            </p>
          </div>
        </div>
      </section>



      {/* Featured Downloads - 推荐下载区域 */}
      {loading ? (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <div className="animate-spin h-8 w-8 border-b-2 border-Linnuo-orange mx-auto mb-4 rounded-full"></div>
              <p className="text-gray-600">
                {t("loading") || '正在加载下载文件...'}
              </p>
            </div>
          </div>
        </section>
      ) : downloadFiles.filter(file => file.featured).length > 0 ? (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">{t("featuredDownloads")}</h2>
              <p className="text-xl text-gray-600">
                {t("mostPopular")}
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {downloadFiles.filter(file => file.featured).slice(0, 6).map((file) => (
                <Card key={file.id} className="group hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <div className="p-2 bg-Linnuo-orange/10 rounded-lg">
                        {getFileIcon(file.type)}
                      </div>
                      <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                        <Star className="h-3 w-3 mr-1 fill-current" />
                        {t("featured")}
                      </Badge>
                    </div>
                    <CardTitle className="group-hover:text-Linnuo-orange transition-colors">
                      {file.name}
                    </CardTitle>
                    <CardDescription>
                      {file.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(file.releaseDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        <span>{file.product}</span>
                      </div>
                    </div>
                    <Button
                      asChild
                      className="w-full bg-Linnuo-orange hover:bg-Linnuo-orange/90"
                    >
                      <a
                        href={file.downloadUrl}
                        download={file.name}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        {t("downloadButton")}
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      ) : null}

      {/* All Downloads */}
      {!loading && (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{t("allDownloads")}</h2>
            <p className="text-xl text-gray-600">
              {t("completeCollection")}
            </p>
          </div>

          <div className="space-y-4">
            {downloadFiles.map((file) => (
                <Card key={file.id} className="group hover:shadow-lg transition-all duration-300">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-0">
                    {/* File Info */}
                    <div className="lg:col-span-3 p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-3 bg-Linnuo-orange/10">
                          {getFileIcon(file.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={getTypeColor(file.type)}>
                              {(() => {
                                switch (file.type) {
                                  case 'driver':
                                    return t("categories.drivers")
                                  case 'documentation':
                                    return t("categories.documentation")
                                  case 'firmware':
                                    return t("categories.firmware")
                                  case 'software':
                                    return t("categories.software")
                                  case 'specification':
                                    return t("categories.specifications")
                                  case 'image':
                                    return t("categories.images")
                                  default:
                                    return (file.type as string).charAt(0).toUpperCase() + (file.type as string).slice(1)
                                }
                              })()}
                            </Badge>
                            {file.featured && (
                              <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                                <Star className="h-3 w-3 mr-1 fill-current" />
                                {t("featured")}
                              </Badge>
                            )}
                          </div>
                          <h3 className="text-xl font-bold mb-2 group-hover:text-Linnuo-orange transition-colors">
                            {file.name}
                          </h3>
                          <p className="text-gray-600 mb-4">
                            {file.description}
                          </p>
                          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Monitor className="h-4 w-4" />
                              <span>{file.product}</span>
                            </div>
                            <span className="font-medium">{t("fileInfo.version")} {file.version}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Download Button */}
                    <div className="p-6 flex items-center justify-center lg:border-l">
                      <Button
                        asChild
                        className="w-full lg:w-auto bg-Linnuo-orange hover:bg-Linnuo-orange/90"
                      >
                        <a
                          href={file.downloadUrl}
                          download={file.name}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Download className="mr-2 h-4 w-4" />
                          {t("downloadButton")}
                        </a>
                      </Button>
                    </div>
                  </div>
                </Card>
            ))}
          </div>


        </div>
      </section>
      )}
    </div>
  )
}

