'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { ProductCompare } from '@/components/products/product-compare'
import { getProducts } from '@/data/unified-data-fetcher'
import { Product } from '@/types/product'
import { useLanguage } from '@/contexts/simple-language-context'
import { useProductsTranslations } from '@/hooks/use-translations'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Search, Plus } from 'lucide-react'
import Image from 'next/image'

function ComparePageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { language } = useLanguage()
  const { t } = useProductsTranslations()
  const [compareProducts, setCompareProducts] = useState<Product[]>([])
  const [allProducts, setAllProducts] = useState<Product[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)

  // 加载所有产品数据
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const products = await getProducts(language)
        setAllProducts(products)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('加载产品失败:', error)
        }
        setAllProducts([])
      } finally {
        setLoading(false)
      }
    }
    loadProducts()
  }, [language])

  // 从 URL 参数中获取要对比的产品 ID
  useEffect(() => {
    if (allProducts.length === 0) return

    const productIds = searchParams.get('products')?.split(',') || []
    const validProducts = productIds
      .map(id => allProducts.find(p => p.id === id))
      .filter((p): p is Product => p !== undefined)

    setCompareProducts(validProducts)
  }, [searchParams, allProducts])

  // 更新 URL 参数
  const updateURL = (productIds: string[]) => {
    const params = new URLSearchParams()
    if (productIds.length > 0) {
      params.set('products', productIds.join(','))
    }
    router.push(`/products/compare?${params.toString()}`)
  }

  const handleRemoveProduct = (productId: string) => {
    const newProducts = compareProducts.filter(p => p.id !== productId)
    setCompareProducts(newProducts)
    updateURL(newProducts.map(p => p.id))
  }

  const handleAddProduct = (product: Product) => {
    if (compareProducts.length >= 4) return
    if (compareProducts.find(p => p.id === product.id)) return

    const newProducts = [...compareProducts, product]
    setCompareProducts(newProducts)
    updateURL(newProducts.map(p => p.id))
    setIsAddDialogOpen(false)
  }

  const filteredProducts = allProducts.filter(product =>
    !compareProducts.find(p => p.id === product.id) &&
    (product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
     product.shortDescription?.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-orange-500 rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">{t('page.loading')}</p>
        </div>
      </main>
    )
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <ProductCompare
        products={compareProducts}
        onRemoveProduct={handleRemoveProduct}
        onAddProduct={() => setIsAddDialogOpen(true)}
        maxProducts={4}
      />

      {/* Add Product Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('compare.addProduct')}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={t('compare.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
              {filteredProducts.map((product) => {
                const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]

                return (
                  <Card
                    key={product.id}
                    className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => handleAddProduct(product)}
                  >
                    <CardContent className="p-4">
                      {/* Product Image */}
                      <div className="aspect-square bg-gray-100 mb-3 overflow-hidden">
                        {mainImage ? (
                          <Image
                            src={mainImage.url}
                            alt={mainImage.alt}
                            width={150}
                            height={150}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <div className="text-2xl">📦</div>
                          </div>
                        )}
                      </div>

                      {/* Product Info */}
                      <h3 className="font-semibold text-sm text-gray-900 mb-2 line-clamp-2">
                        {product.name}
                      </h3>

                      {/* Short Description */}
                      {product.shortDescription && (
                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {product.shortDescription}
                        </p>
                      )}

                      {/* Category */}
                      <Badge variant="secondary" className="text-xs">
                        {product.category.replace(/-/g, ' ')}
                      </Badge>

                      {/* Add Button */}
                      <Button size="sm" className="w-full mt-3">
                        <Plus className="w-3 h-3 mr-1" />
                        {t('compare.addToCompare')}
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>{t('compare.noProductsFound')}</p>
              </div>
            )}

            {compareProducts.length >= 4 && (
              <div className="text-center py-4">
                <p className="text-orange-600 font-medium">
                  {t('compare.maxProductsReached')}
                </p>
                <p className="text-sm text-gray-600">
                  {t('compare.removeToAdd')}
                </p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </main>
  )
}

export default function ComparePage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-b-2 border-orange-600 rounded-full mx-auto mb-4"></div>
          <p>Loading comparison...</p>
        </div>
      </div>
    }>
      <ComparePageContent />
    </Suspense>
  )
}
