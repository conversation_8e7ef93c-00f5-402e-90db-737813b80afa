'use client'
import React, { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ProductCard } from '@/components/products/product-card'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Cpu, Zap, Package, Monitor, GitCompare, X } from 'lucide-react'
import Link from 'next/link'
import { getProducts, getProductsByCategory, getProductCategories } from '@/data/unified-data-fetcher'
import { Product } from '@/types/product'
import type { ProductCategory } from '@/data/unified-data-fetcher'
import { useProductsTranslations } from '@/hooks/use-translations'
import { useLanguage } from '@/contexts/simple-language-context'
import { usePageMetadata } from '@/hooks/use-page-metadata'

// 产品分类配置将在组件内部动态生成

function ProductsContent() {
  const searchParams = useSearchParams()
  const selectedCategory = searchParams.get('category') || 'all'
  const [products, setProducts] = useState<Product[]>([])
  const [productCategories, setProductCategories] = useState<ProductCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([])
  const { t } = useProductsTranslations()
  const { language } = useLanguage()

  // 设置页面元数据
  usePageMetadata({
    customTitle: selectedCategory !== 'all' ? `${selectedCategory} - 产品中心` : undefined
  })

  // 获取产品分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categories = await getProductCategories(language)
        setProductCategories(categories)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取产品分类失败:', error)
        }
        setProductCategories([])
      }
    }
    fetchCategories()
  }, [language])

  useEffect(() => {
    const fetchProducts = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 开始获取产品数据，分类:', selectedCategory, '语言:', language)
      }
      setLoading(true)
      try {
        const data = selectedCategory === 'all'
          ? await getProducts(language)
          : await getProductsByCategory(selectedCategory, language)
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 产品数据获取完成，数量:', data.length)
          console.log('📦 产品数据详情:', data)
        }
        setProducts(data)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 产品数据获取失败:', error)
        }
        setProducts([])
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [selectedCategory, language]) // 依赖语言变化重新加载

  // 动态生成分类图标映射，基于实际获取的分类数据
  const categoryIcons = React.useMemo(() => {
    const iconMap: Record<string, any> = {}
    productCategories.forEach(category => {
      // 根据分类名称或slug匹配图标
      const key = category.slug.toLowerCase()
      if (key.includes('scalable') || key.includes('embedded')) {
        iconMap[category.slug] = Cpu
      } else if (key.includes('mini') || key.includes('size')) {
        iconMap[category.slug] = Zap
      } else if (key.includes('universal')) {
        iconMap[category.slug] = Package
      } else if (key.includes('ipc') || key.includes('一体机')) {
        iconMap[category.slug] = Monitor
      } else {
        iconMap[category.slug] = Package // 默认图标
      }
    })
    return iconMap
  }, [productCategories])

  // 处理产品选择
  const handleProductSelect = (productId: string) => {
    setSelectedProductIds(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId)
      } else if (prev.length < 4) {
        return [...prev, productId]
      }
      return prev
    })
  }

  // 移除选中的产品
  const handleRemoveProduct = (productId: string) => {
    setSelectedProductIds(prev => prev.filter(id => id !== productId))
  }

  // 清空所有选中的产品
  const handleClearAll = () => {
    setSelectedProductIds([])
  }

  // 生成对比URL
  const getCompareUrl = () => {
    return `/products/compare?products=${selectedProductIds.join(',')}`
  }

  return (
    <div className="min-h-screen">
      {/* Page Header with Background */}
      <section
        className="relative py-24 lg:py-32 bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center"
        style={{
          backgroundImage: 'url(/images/background/prod_bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 drop-shadow-lg">
              {t("page.title")}
            </h1>
            <p className="text-xl text-white max-w-3xl mx-auto drop-shadow-md">
              {t("page.subtitle")}
            </p>
          </div>
        </div>
      </section>


      {/* Products Display */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          {/* Header with Compare Button */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="text-center lg:text-left mb-6 lg:mb-0">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {selectedCategory === 'all'
                  ? t("page.allProducts")
                  : (() => {
                      const category = productCategories.find(cat => cat.slug === selectedCategory)
                      return category ? category.name : t("page.allProducts")
                    })()
                }
              </h2>
              <p className="text-lg text-gray-600">
                {selectedCategory === 'all'
                  ? t("page.browseComplete")
                  : (() => {
                      const category = productCategories.find(cat => cat.slug === selectedCategory)
                      return category ? category.description : t("page.browseCategory")
                    })()
                }
              </p>
            </div>

            {/* Compare Section */}
            <div className="flex flex-col items-center lg:items-end gap-4">
              {selectedProductIds.length > 0 && (
                <div className="flex flex-wrap gap-2 max-w-md">
                  {selectedProductIds.map(productId => {
                    const product = products.find(p => p.id === productId)
                    return product ? (
                      <Badge
                        key={productId}
                        variant="secondary"
                        className="flex items-center gap-1 px-3 py-1"
                      >
                        <span className="text-xs truncate max-w-20">{product.name}</span>
                        <button
                          onClick={() => handleRemoveProduct(productId)}
                          className="ml-1 hover:text-red-500 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ) : null
                  })}
                </div>
              )}
              
              <div className="flex gap-2">
                {selectedProductIds.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                    className="text-gray-600 hover:text-red-600"
                  >
                    {t('compare.clearSelection')}
                  </Button>
                )}
                
                <Button
                  asChild
                  disabled={selectedProductIds.length === 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Link href={selectedProductIds.length > 0 ? getCompareUrl() : '#'} className="flex items-center gap-2">
                    <GitCompare className="w-4 h-4" />
                    {t('compare.viewComparison')} ({selectedProductIds.length})
                  </Link>
                </Button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-8 w-8 border-b-2 border-orange-500 rounded-full"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <ProductCard 
                  key={product.id} 
                  product={product}
                  isSelected={selectedProductIds.includes(product.id)}
                  onSelect={() => handleProductSelect(product.id)}
                  maxSelected={selectedProductIds.length >= 4}
                />
              ))}
            </div>
          )}

          {!loading && products.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">{t("page.noProducts")}</p>
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

export default function ProductsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-b-2 border-orange-500 rounded-full"></div>
      </div>
    }>
      <ProductsContent />
    </Suspense>
  )
}
