'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import { ProductDetailView } from '@/components/products/product-detail-view'
import { ProductStructuredData } from '@/components/seo/structured-data'
import { getProductBySlug } from '@/data/unified-data-fetcher'
import { useLanguage } from '@/contexts/simple-language-context'
import { Product } from '@/types/product'

interface ProductPageProps {
  params: {
    slug: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const { language } = useLanguage()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [notFoundError, setNotFoundError] = useState(false)

  useEffect(() => {
    const fetchProduct = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 获取产品详情:', params.slug, '语言:', language)
      }
      setLoading(true)
      setNotFoundError(false)
      
      try {
        const productData = await getProductBySlug(params.slug, language)
        if (productData) {
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ 产品详情获取成功:', productData.name)
          }
          setProduct(productData)
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('❌ 产品未找到:', params.slug)
          }
          setNotFoundError(true)
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 产品详情获取失败:', error)
        }
        setNotFoundError(true)
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [params.slug, language])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载产品详情中...</p>
        </div>
      </div>
    )
  }

  if (notFoundError || !product) {
    notFound()
  }

  return (
    <>
      <ProductStructuredData product={product} />
      <ProductDetailView product={product} />
    </>
  )
}
