'use client'

import { useState, useEffect } from 'react'
import { ProductDetailView } from '@/components/products/product-detail-view'
import { ProductStructuredData } from '@/components/seo/structured-data'
import { getProductBySlug } from '@/data/unified-data-fetcher'
import { useLanguage } from '@/contexts/simple-language-context'
import { Product } from '@/types/product'

interface ProductPageProps {
  params: {
    slug: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const { language } = useLanguage()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true)
      setError(null)

      try {
        const productData = await getProductBySlug(params.slug, language)
        if (!productData) {
          setError('Product not found')
          return
        }
        setProduct(productData)
      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error(`Error loading product ${params.slug}:`, err)
        }
        setError('Failed to load product')
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [params.slug, language])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-orange-500 rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">加载产品详情中...</p>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">产品未找到</h1>
          <p className="text-lg text-gray-600 mb-8">
            抱歉，您访问的产品页面不存在或已被移除。
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <ProductStructuredData product={product} />
      <ProductDetailView product={product} />
    </>
  )
}
