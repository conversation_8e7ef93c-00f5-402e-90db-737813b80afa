'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft, Search } from 'lucide-react'
import { useCommonTranslations } from '@/hooks/use-translations'

export default function NotFound() {
  const { t } = useCommonTranslations()
  const router = useRouter()

  const handleGoBack = () => {
    router.back()
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4">
      <div className="max-w-md mx-auto text-center">
        <div className="mb-8">
          <div className="w-24 h-24 bg-Linnuo-orange/10 flex items-center justify-center mx-auto mb-6">
            <span className="text-4xl font-bold text-Linnuo-orange">404</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t("notFound.title")}
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            {t("notFound.description")}
          </p>
        </div>

        <div className="space-y-4">
          <Button asChild className="w-full bg-Linnuo-orange hover:bg-Linnuo-orange/90">
            <Link href="/">
              <Home className="w-4 h-4 mr-2" />
              {t("notFound.backHome")}
            </Link>
          </Button>
          
          <Button asChild variant="outline" className="w-full">
            <Link href="/search">
              <Search className="w-4 h-4 mr-2" />
              {t("notFound.search")}
            </Link>
          </Button>
          
          <Button variant="ghost" className="w-full" onClick={handleGoBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t("notFound.goBack")}
          </Button>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            {t("notFound.helpText")}
          </p>
        </div>
      </div>
    </div>
  )
}
