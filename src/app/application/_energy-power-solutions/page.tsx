'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Zap,
  Shield,
  Settings,
  Activity,
  ArrowRight,
  ExternalLink,
  Play,
  CheckCircle,
  Star,
  TrendingUp,
  Globe,
  Cpu,
  Network,
  HardDrive,
  Monitor,
  Power,
  Thermometer,
  Gauge,
  Database,
  Wifi,
  Cable,
  Server,
  BarChart3,
  Target,
  Award,
  Users,
  Building2,
  Factory,
  Sun,
  Wind,
  Battery,
  Leaf,
  Recycle,
  TreePine,
  Lightbulb,
  Layers,
  Smartphone,
  Cloud,
  Lock,
  Timer,
  Wrench,
  Truck,
  MapPin,
  Phone,
  Mail,
  Workflow,
  PenTool,
  Rocket,
  Headphones,
  Search,
  Building,
  ShoppingBag,
  AlertTriangle
} from "lucide-react"

export default function EnergyPowerSolutionsPage() {

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-green-900 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[url('/images/energy-grid.jpg')] bg-cover bg-center opacity-10"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-transparent to-green-600/20"></div>
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-green-500/10 blur-3xl animate-pulse delay-1000"></div>
        </div>
        
        <div className="container-custom relative z-10">
          <div className="text-center max-w-5xl mx-auto">
            <Badge className="mb-8 bg-white/10 text-white hover:bg-white/20 border-white/20 backdrop-blur-sm">
              <Zap className="w-4 h-4 mr-2" />
              清洁能源 · 智慧未来
            </Badge>
            
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 text-white leading-tight">
              构建可持续的
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-green-400 to-yellow-400 bg-clip-text text-transparent">
                能源生态系统
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-blue-100 mb-12 leading-relaxed max-w-4xl mx-auto">
              通过先进的可再生能源技术和智能化管理系统，为您提供高效、可靠、环保的综合能源解决方案，
              助力实现碳中和目标，共创绿色未来。
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button size="lg" className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white px-8 py-4 text-lg">
                探索解决方案
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg backdrop-blur-sm">
                观看案例视频
                <Play className="ml-2 h-5 w-5" />
              </Button>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">500+</div>
                <div className="text-blue-200 text-sm md:text-base">成功项目</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">99.9%</div>
                <div className="text-blue-200 text-sm md:text-base">系统可靠性</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">40%</div>
                <div className="text-blue-200 text-sm md:text-base">节能效果</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">24/7</div>
                <div className="text-blue-200 text-sm md:text-base">技术支持</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 flex justify-center">
            <div className="w-1 h-3 bg-white/50 mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* 核心解决方案 */}
      <section className="py-24 bg-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-green-50/50"></div>
        <div className="container-custom relative">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-gradient-to-r from-blue-500 to-green-500 text-white border-0">
              <Sun className="w-4 h-4 mr-2" />
              核心技术方案
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              领先的清洁能源技术
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              整合太阳能、风能、储能和智能电网技术，构建完整的可再生能源生态系统，
              为各行业提供可靠、高效、环保的能源解决方案
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
             {[
               {
                 icon: Sun,
                 title: "太阳能光伏系统",
                 description: "采用最新一代高效硅晶体技术，配备智能MPPT控制器和AI优化算法，实现最大功率点跟踪，发电效率提升25%以上。",
                 features: ["转换效率>22%", "25年质保", "智能运维", "模块化设计"],
                 stats: "发电效率提升25%",
                 color: "from-yellow-400 via-orange-400 to-red-400",
                 bgColor: "from-yellow-50 to-orange-50"
               },
               {
                 icon: Wind,
                 title: "风力发电系统",
                 description: "先进的变桨距控制技术和永磁同步发电机，适应2-25m/s风速范围，具备优异的低风速发电能力和电网友好特性。",
                 features: ["低风速启动", "变桨控制", "并网友好", "智能维护"],
                 stats: "年发电量提升30%",
                 color: "from-blue-400 via-cyan-400 to-teal-400",
                 bgColor: "from-blue-50 to-cyan-50"
               },
               {
                 icon: Battery,
                 title: "储能管理系统",
                 description: "采用磷酸铁锂电池技术，配备先进的BMS系统和能量管理算法，提供毫秒级响应速度，支持削峰填谷和电网调频服务。",
                 features: ["循环寿命>8000次", "毫秒级响应", "安全防护", "云端监控"],
                 stats: "储能效率>95%",
                 color: "from-green-400 via-emerald-400 to-teal-400",
                 bgColor: "from-green-50 to-emerald-50"
               },
               {
                 icon: Zap,
                 title: "智能电网管理",
                 description: "基于物联网和大数据技术的智能电网解决方案，实现发电、输电、配电、用电全链条智能化管理和优化调度。",
                 features: ["实时监控", "预测分析", "自动调度", "故障自愈"],
                 stats: "能效提升40%",
                 color: "from-purple-400 via-violet-400 to-indigo-400",
                 bgColor: "from-purple-50 to-violet-50"
               }
             ].map((solution, index) => (
               <Card key={index} className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/90 backdrop-blur-sm hover:-translate-y-3 overflow-hidden">
                 <div className={`h-2 bg-gradient-to-r ${solution.color}`}></div>
                 <CardContent className="p-8">
                   <div className="flex items-start gap-6">
                     <div className={`w-20 h-20 bg-gradient-to-br ${solution.bgColor} flex items-center justify-center group-hover:scale-110 transition-transform duration-300 border border-white/50`}>
                       <solution.icon className={`h-10 w-10 bg-gradient-to-br ${solution.color} bg-clip-text text-transparent`} />
                     </div>
                     <div className="flex-1">
                       <h3 className="text-2xl font-bold mb-3 text-slate-900 group-hover:text-blue-600 transition-colors">
                         {solution.title}
                       </h3>
                       <div className={`inline-block px-3 py-1 text-sm font-medium bg-gradient-to-r ${solution.color} text-white mb-4`}>
                         {solution.stats}
                       </div>
                     </div>
                   </div>
                   
                   <p className="text-slate-600 mb-6 leading-relaxed text-base">
                     {solution.description}
                   </p>
                   
                   <div className="grid grid-cols-2 gap-3">
                     {solution.features.map((feature, idx) => (
                       <div key={idx} className="flex items-center gap-2 text-sm text-slate-700">
                         <div className={`w-2 h-2 bg-gradient-to-r ${solution.color}`}></div>
                         <span className="font-medium">{feature}</span>
                       </div>
                     ))}
                   </div>
                   
                   <Button variant="ghost" className="mt-6 text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-0 h-auto font-medium">
                     了解详情 <ArrowRight className="ml-1 h-4 w-4" />
                   </Button>
                 </CardContent>
               </Card>
             ))}
          </div>
        </div>
      </section>

      {/* 技术优势 */}
       <section className="py-24 bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
         <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
         <div className="container-custom relative">
           <div className="text-center mb-20">
             <Badge className="mb-6 bg-blue-500/10 text-blue-600 border-blue-200">
               <TrendingUp className="w-4 h-4 mr-2" />
               核心优势
             </Badge>
             <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
               领先技术，卓越性能
             </h2>
             <p className="text-xl text-slate-600 max-w-3xl mx-auto">
               凭借多年技术积累和持续创新，我们在能源领域建立了显著的技术优势
             </p>
           </div>
 
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
             {[
               {
                 icon: Cpu,
                 title: "AI智能优化",
                 description: "基于机器学习算法的智能能源管理系统，实时优化能源配置，提升整体效率。",
                 stats: "效率提升35%",
                 color: "from-blue-500 to-cyan-500"
               },
               {
                 icon: Shield,
                 title: "安全可靠",
                 description: "多重安全防护机制，99.9%系统可靠性，确保能源系统稳定运行。",
                 stats: "可靠性99.9%",
                 color: "from-green-500 to-emerald-500"
               },
               {
                 icon: Zap,
                 title: "快速响应",
                 description: "毫秒级响应速度，支持电网调频和削峰填谷，保障电力系统稳定。",
                 stats: "响应<10ms",
                 color: "from-yellow-500 to-orange-500"
               },
               {
                 icon: Leaf,
                 title: "绿色环保",
                 description: "100%清洁能源技术，零排放解决方案，助力实现碳中和目标。",
                 stats: "零碳排放",
                 color: "from-emerald-500 to-green-500"
               }
             ].map((advantage, index) => (
               <Card key={index} className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm hover:-translate-y-2">
                 <CardContent className="p-8 text-center">
                   <div className={`w-20 h-20 bg-gradient-to-br ${advantage.color} flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-xl`}>
                     <advantage.icon className="h-10 w-10 text-white" />
                   </div>
                   <h3 className="text-xl font-bold mb-3 text-slate-900">
                     {advantage.title}
                   </h3>
                   <Badge className={`mb-4 bg-gradient-to-r ${advantage.color} text-white border-0`}>
                     {advantage.stats}
                   </Badge>
                   <p className="text-slate-600 leading-relaxed text-sm">
                     {advantage.description}
                   </p>
                 </CardContent>
               </Card>
             ))}
           </div>
         </div>
       </section>

      {/* 应用场景 */}
       <section className="py-24 bg-white relative">
         <div className="container-custom">
           <div className="text-center mb-20">
             <Badge className="mb-6 bg-green-500/10 text-green-600 border-green-200">
               <Building className="w-4 h-4 mr-2" />
               应用场景
             </Badge>
             <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
               广泛应用，深度赋能
             </h2>
             <p className="text-xl text-slate-600 max-w-4xl mx-auto">
               从工业制造到智慧城市，从数据中心到农业设施，我们的能源解决方案覆盖各个行业领域
             </p>
           </div>
 
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
             {[
               {
                 icon: Factory,
                 title: "工业园区",
                 description: "为大型工业园区提供综合能源管理解决方案，通过智能调度和储能系统，实现能源的高效利用和成本优化。",
                 benefits: ["降低用电成本30%", "提升能源利用率", "减少碳排放", "24/7智能监控"],
                 image: "/images/industrial-park.jpg"
               },
               {
                 icon: Building,
                 title: "制造工厂",
                 description: "针对制造业的特殊需求，提供稳定可靠的电力供应和能效管理，支持生产线的连续运行和智能化升级。",
                 benefits: ["保障生产连续性", "智能负荷管理", "设备预测维护", "能耗实时监测"],
                 image: "/images/manufacturing.jpg"
               },
               {
                 icon: Server,
                 title: "数据中心",
                 description: "为数据中心提供高可靠性的电力保障和冷却系统优化，确保关键业务的持续运行和能效最大化。",
                 benefits: ["99.99%可用性", "PUE<1.3", "智能冷却", "绿色认证"],
                 image: "/images/data-center.jpg"
               },
               {
                 icon: Leaf,
                 title: "农业设施",
                 description: "为现代农业提供清洁能源解决方案，支持智能温室、灌溉系统和农产品加工设施的可持续发展。",
                 benefits: ["降低运营成本", "提升作物产量", "环境友好", "自动化管理"],
                 image: "/images/agriculture.jpg"
               },
               {
                 icon: ShoppingBag,
                 title: "商业综合体",
                 description: "为购物中心、办公楼等商业建筑提供智能能源管理，优化照明、空调等系统的能耗，提升用户体验。",
                 benefits: ["节能减排40%", "智能照明", "舒适环境", "成本控制"],
                 image: "/images/commercial.jpg"
               },
               {
                 icon: MapPin,
                 title: "智慧城市",
                 description: "参与智慧城市建设，为城市基础设施提供清洁能源支持，助力城市实现可持续发展和碳中和目标。",
                 benefits: ["城市级调度", "多能互补", "智慧运维", "碳中和目标"],
                 image: "/images/smart-city.jpg"
               }
             ].map((scenario, index) => (
               <Card key={index} className="group hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 bg-gradient-to-br from-white to-slate-50/50">
                 <div className="aspect-video bg-gradient-to-br from-blue-100 to-green-100 relative overflow-hidden">
                   <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-green-500/20"></div>
                   <div className="absolute inset-0 flex items-center justify-center">
                     <scenario.icon className="h-16 w-16 text-blue-600" />
                   </div>
                 </div>
                 <CardContent className="p-6">
                   <h3 className="text-xl font-bold mb-3 text-slate-900 group-hover:text-blue-600 transition-colors">
                     {scenario.title}
                   </h3>
                   <p className="text-slate-600 leading-relaxed mb-4 text-sm">
                     {scenario.description}
                   </p>
                   <div className="space-y-2">
                     {scenario.benefits.map((benefit, idx) => (
                       <div key={idx} className="flex items-center gap-2 text-sm text-slate-700">
                         <div className="w-1.5 h-1.5 bg-green-500"></div>
                         <span>{benefit}</span>
                       </div>
                     ))}
                   </div>
                   <Button variant="ghost" className="mt-4 text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-0 h-auto font-medium text-sm">
                     查看案例 <ArrowRight className="ml-1 h-3 w-3" />
                   </Button>
                 </CardContent>
               </Card>
             ))}
           </div>
         </div>
       </section>

      {/* Product Features */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">产品方案</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-green-500 mx-auto mb-6"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "智能监控系统",
                description: "实时监控能源使用情况，提供详细的数据分析和报告",
                icon: Monitor,
                specs: "24/7监控"
              },
              {
                title: "自动化控制",
                description: "智能调节能源分配，优化系统运行效率",
                icon: Settings,
                specs: "AI驱动"
              },
              {
                title: "预警系统",
                description: "提前预警潜在问题，确保系统稳定运行",
                icon: AlertTriangle,
                specs: "毫秒级响应"
              },
              {
                title: "远程管理",
                description: "支持远程监控和管理，降低运维成本",
                icon: Smartphone,
                specs: "云端部署"
              },
              {
                title: "数据分析",
                description: "深度数据挖掘，提供决策支持和优化建议",
                icon: BarChart3,
                specs: "大数据分析"
              },
              {
                title: "安全防护",
                description: "多层安全防护机制，保障系统和数据安全",
                icon: Shield,
                specs: "企业级安全"
              }
            ].map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center group-hover:scale-110 transition-transform">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-lg">{feature.title}</CardTitle>
                        <Badge variant="secondary" className="mt-1">{feature.specs}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="leading-relaxed">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>


      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-500/10 to-green-500/10">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              准备定制您的能源电力解决方案？
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              联系我们的专业团队，为您的项目提供定制化的工业主机解决方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-blue-500 hover:bg-blue-600">
                立即咨询
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline">
                下载方案书
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}