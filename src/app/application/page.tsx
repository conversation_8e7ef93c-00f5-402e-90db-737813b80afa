"use client"

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Factory,
  Car,
  Zap,
  Cpu,
  Eye,
  ArrowRight,
  CheckCircle,
  TrendingUp,
  Shield,
  Clock,
  Users,
  Power
} from 'lucide-react';
import { useLanguage } from '@/contexts/simple-language-context'
import { usePageMetadata } from '@/hooks/use-page-metadata'

// 类型定义
interface Industry {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  image: string;
  features: string[];
  benefits: string[];
}

interface CaseStudy {
  title: string;
  industry: string;
  challenge: string;
  solution: string;
  results: string[];
  image: string;
  icon: React.ComponentType<any>;
  specs: string;
}

interface Advantage {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
}

// 页面内容配置
const pageContent = {
  zh: {
    hero: {
      title: "应用案例",
      subtitle: "探索Linnuo单板计算机在各个领域的创新应用，从物联网到人工智能，从工业自动化到教育研究。"
    },
    categories: {
      title: "应用领域",
      subtitle: "Linnuo单板计算机在多个行业和领域中发挥着重要作用"
    },
    advantages: {
      title: "为什么选择Linnuo",
      subtitle: "专业的技术实力和完善的服务体系"
    },
    common: {
      features: "功能特性",
      benefits: "核心优势"
    },
    industries: [
      {
        title: "工业自动化",
        description: "为工业4.0提供可靠的计算和控制解决方案",
        icon: Factory,
        image: '/images/product-background.svg',
        features: [
          "生产线控制",
          "设备监控",
          "质量检测",
          "预测性维护",
          "数据采集"
        ],
        benefits: [
          "提高生产效率和产品质量",
          "降低人工成本和运营风险",
          "实现设备智能化管理",
          "支持工业4.0数字化转型"
        ]
      },
      {
        title: "物联网智能家居",
        description: "构建智能家居系统，实现设备互联和自动化控制",
        icon: Zap,
        image: '/images/industrial-background.svg',
        features: [
          "智能照明控制",
          "温度湿度监控",
          "安防监控系统",
          "智能门锁",
          "语音助手集成"
        ],
        benefits: [
          "设备互联互通",
          "智能化场景控制",
          "远程监控管理",
          "节能环保解决方案"
        ]
      },
      {
        title: "人工智能与机器学习",
        description: "利用强大的计算能力进行AI模型训练和推理",
        icon: Cpu,
        image: '/images/edge-computing-platform.svg',
        features: [
          "图像识别",
          "自然语言处理",
          "边缘计算",
          "机器视觉",
          "深度学习推理"
        ],
        benefits: [
          "强大的AI计算能力",
          "支持多种深度学习框架",
          "边缘计算降低延迟",
          "灵活的模型部署方案"
        ]
      },
      // {
      //   title: "教育与研究",
      //   description: "为学术研究和教学提供灵活的计算平台",
      //   icon: Eye,
      //   image: '/images/fd-bg01.svg',
      //   features: [
      //     "编程教学",
      //     "科研项目",
      //     "原型开发",
      //     "实验平台",
      //     "创客教育"
      //   ],
      //   benefits: [
      //     "丰富的教学资源",
      //     "开放的开发环境",
      //     "项目实践平台",
      //     "创新能力培养"
      //   ]
      // },
      {
        title: "小尺寸嵌入式",
        description: "紧凑型设计，适用于空间受限的应用场景",
        icon: Car,
        image: '/images/product-background.svg',
        features: [
          "紧凑设计",
          "低功耗",
          "高集成度",
          "可靠性高"
        ],
        benefits: [
          "节省空间",
          "降低功耗",
          "简化部署",
          "提高可靠性"
        ]
      },
      {
        title: "能源电力",
        description: "为能源管理和电力系统提供智能化解决方案",
        icon: Power,
        image: '/images/power-grid-sunset.svg',
        features: [
          "能源监控",
          "智能调度",
          "故障诊断",
          "数据分析"
        ],
        benefits: [
          "优化能源使用",
          "提高系统效率",
          "预防故障发生",
          "降低运营成本"
        ]
      },
      {
        title: "网络通信",
        description: "构建高性能网络设备和通信系统",
        icon: Zap,
        image: '/images/data-center.svg',
        features: [
          "网络路由器",
          "防火墙设备",
          "VPN网关",
          "网络存储",
          "通信基站"
        ],
        benefits: [
          "高速数据传输",
          "稳定网络连接",
          "安全通信保障",
          "灵活网络配置"
        ]
      }
    ]
  },
  en: {
    hero: {
      title: "Application Cases",
      subtitle: "Explore innovative applications of Linnuo single board computers across various fields, from IoT to artificial intelligence, from industrial automation to education and research."
    },
    categories: {
      title: "Application Fields",
      subtitle: "Linnuo single board computers play important roles in multiple industries and fields"
    },
    advantages: {
      title: "Why Choose Linnuo",
      subtitle: "Professional technical expertise and comprehensive service system"
    },
    common: {
      features: "Features",
      benefits: "Core Benefits"
    },
    industries: [
      {
        title: "Industrial Automation",
        description: "Provide reliable computing and control solutions for Industry 4.0",
        icon: Factory,
        image: '/images/product-background.svg',
        features: [
          "Production line control",
          "Equipment monitoring",
          "Quality inspection",
          "Predictive maintenance",
          "Data collection"
        ],
        benefits: [
          "Improve production efficiency and product quality",
          "Reduce labor costs and operational risks",
          "Enable intelligent equipment management",
          "Support Industry 4.0 digital transformation"
        ]
      },
      {
        title: "IoT & Smart Home",
        description: "Build smart home systems with device connectivity and automation control",
        icon: Zap,
        image: '/images/industrial-background.svg',
        features: [
          "Smart lighting control",
          "Temperature and humidity monitoring",
          "Security monitoring systems",
          "Smart door locks",
          "Voice assistant integration"
        ],
        benefits: [
          "Device interconnection and interoperability",
          "Intelligent scenario control",
          "Remote monitoring and management",
          "Energy-saving and environmental solutions"
        ]
      },
      {
        title: "AI & Machine Learning",
        description: "Leverage powerful computing capabilities for AI model training and inference",
        icon: Cpu,
        image: '/images/edge-computing-platform.svg',
        features: [
          "Image recognition",
          "Natural language processing",
          "Edge computing",
          "Machine vision",
          "Deep learning inference"
        ],
        benefits: [
          "Powerful AI computing capabilities",
          "Support for multiple deep learning frameworks",
          "Edge computing reduces latency",
          "Flexible model deployment solutions"
        ]
      },
      // {
      //   title: "Education & Research",
      //   description: "Provide flexible computing platforms for academic research and teaching",
      //   icon: Eye,
      //   image: '/images/fd-bg01.svg',
      //   features: [
      //     "Programming education",
      //     "Research projects",
      //     "Prototype development",
      //     "Experimental platforms",
      //     "Maker education"
      //   ],
      //   benefits: [
      //     "Rich educational resources",
      //     "Open development environment",
      //     "Project practice platform",
      //     "Innovation capability development"
      //   ]
      // },
      {
        title: "Compact Embedded",
        description: "Compact design suitable for space-constrained application scenarios",
        icon: Car,
        image: '/images/product-background.svg',
        features: [
          "Compact design",
          "Low power consumption",
          "High integration",
          "High reliability"
        ],
        benefits: [
          "Space saving",
          "Reduced power consumption",
          "Simplified deployment",
          "Improved reliability"
        ]
      },
      {
        title: "Energy & Power",
        description: "Provide intelligent solutions for energy management and power systems",
        icon: Power,
        image: '/images/power-grid-sunset.svg',
        features: [
          "Energy monitoring",
          "Intelligent scheduling",
          "Fault diagnosis",
          "Data analysis"
        ],
        benefits: [
          "Optimize energy usage",
          "Improve system efficiency",
          "Prevent failures",
          "Reduce operating costs"
        ]
      },
      {
        title: "Network Communication",
        description: "Build high-performance network devices and communication systems",
        icon: Zap,
        image: '/images/data-center.svg',
        features: [
          "Network routers",
          "Firewall devices",
          "VPN gateways",
          "Network storage",
          "Communication base stations"
        ],
        benefits: [
          "High-speed data transmission",
          "Stable network connections",
          "Secure communication",
          "Flexible network configuration"
        ]
      }
    ]
  }
};

// 案例研究数据也添加到内容配置中
const getCaseStudiesData = (language: 'zh' | 'en'): CaseStudy[] => {
  const caseStudiesContent = {
    zh: [
      {
        title: "智能工厂自动化系统",
        industry: "制造业",
        challenge: "传统生产线缺乏实时监控和智能化管理，生产效率低下，质量控制困难。",
        solution: "采用Linnuo单板计算机构建智能工厂系统，实现生产线实时监控、设备状态管理和质量自动检测。",
        results: [
          "生产效率提升35%",
          "产品质量提升20%",
          "设备故障率降低40%",
          "人工成本节省30%"
        ],
        image: '/images/industrial-background.svg',
        icon: Factory,
        specs: "ARM Cortex-A78 四核处理器，8GB LPDDR5内存，支持多种工业通信协议"
      },
      {
        title: "边缘计算数据中心",
        industry: "云计算",
        challenge: "传统云计算延迟高，无法满足实时数据处理需求，带宽成本高昂。",
        solution: "部署基于Linnuo的边缘计算节点，就近处理数据，降低延迟，减少带宽使用。",
        results: [
          "数据处理延迟降低80%",
          "带宽使用减少60%",
          "运营成本降低45%",
          "服务可用性提升至99.9%"
        ],
        image: '/images/edge-computing-platform.svg',
        icon: Cpu,
        specs: "高性能GPU加速，支持AI推理，千兆以太网连接"
      },
      {
        title: "机器视觉质检系统",
        industry: "电子制造",
        challenge: "人工质检效率低，漏检率高，无法满足大批量生产的质量要求。",
        solution: "基于Linnuo平台开发机器视觉系统，实现产品缺陷自动检测和分类。",
        results: [
          "检测精度提升至99.5%",
          "检测速度提升10倍",
          "人工成本降低70%",
          "产品合格率提升15%"
        ],
        image: '/images/fd-bg01.svg',
        icon: Eye,
        specs: "支持4K摄像头，AI视觉处理单元，实时图像分析"
      },
      {
        title: "智能电网监控平台",
        industry: "能源电力",
        challenge: "电网设备分布广泛，传统监控方式效率低，故障响应慢。",
        solution: "构建基于Linnuo的分布式监控网络，实现电网设备实时监控和智能调度。",
        results: [
          "故障检测时间缩短90%",
          "电网稳定性提升25%",
          "能源利用效率提升18%",
          "维护成本降低35%"
        ],
        image: '/images/power-grid-sunset.svg',
        icon: Power,
        specs: "工业级设计，支持恶劣环境，多种通信接口"
      }
    ],
    en: [
      {
        title: "Smart Factory Automation System",
        industry: "Manufacturing",
        challenge: "Traditional production lines lack real-time monitoring and intelligent management, resulting in low production efficiency and difficult quality control.",
        solution: "Use Linnuo single board computers to build smart factory systems, enabling real-time production line monitoring, equipment status management, and automatic quality detection.",
        results: [
          "35% increase in production efficiency",
          "20% improvement in product quality",
          "40% reduction in equipment failure rate",
          "30% savings in labor costs"
        ],
        image: '/images/industrial-background.svg',
        icon: Factory,
        specs: "ARM Cortex-A78 quad-core processor, 8GB LPDDR5 memory, supports multiple industrial communication protocols"
      },
      {
        title: "Edge Computing Data Center",
        industry: "Cloud Computing",
        challenge: "Traditional cloud computing has high latency and cannot meet real-time data processing requirements, with high bandwidth costs.",
        solution: "Deploy Linnuo-based edge computing nodes to process data locally, reducing latency and bandwidth usage.",
        results: [
          "80% reduction in data processing latency",
          "60% reduction in bandwidth usage",
          "45% reduction in operating costs",
          "Service availability improved to 99.9%"
        ],
        image: '/images/edge-computing-platform.svg',
        icon: Cpu,
        specs: "High-performance GPU acceleration, supports AI inference, gigabit ethernet connectivity"
      },
      {
        title: "Machine Vision Quality Inspection System",
        industry: "Electronics Manufacturing",
        challenge: "Manual quality inspection is inefficient with high miss rates, unable to meet quality requirements for large-scale production.",
        solution: "Develop machine vision systems based on Linnuo platform for automatic product defect detection and classification.",
        results: [
          "Detection accuracy improved to 99.5%",
          "10x faster detection speed",
          "70% reduction in labor costs",
          "15% improvement in product qualification rate"
        ],
        image: '/images/fd-bg01.svg',
        icon: Eye,
        specs: "Supports 4K cameras, AI vision processing unit, real-time image analysis"
      },
      {
        title: "Smart Grid Monitoring Platform",
        industry: "Energy & Power",
        challenge: "Grid equipment is widely distributed, traditional monitoring methods are inefficient with slow fault response.",
        solution: "Build distributed monitoring network based on Linnuo for real-time grid equipment monitoring and intelligent scheduling.",
        results: [
          "90% reduction in fault detection time",
          "25% improvement in grid stability",
          "18% improvement in energy utilization efficiency",
          "35% reduction in maintenance costs"
        ],
        image: '/images/power-grid-sunset.svg',
        icon: Power,
        specs: "Industrial-grade design, supports harsh environments, multiple communication interfaces"
      }
    ]
  };

  return caseStudiesContent[language];
};

// 优势数据
const getAdvantagesData = (language: 'zh' | 'en'): Advantage[] => {
  const advantagesContent = {
    zh: [
      {
        icon: Shield,
        title: "可靠性保证",
        description: "工业级设计标准，严格的质量控制，确保产品在各种环境下稳定运行。"
      },
      {
        icon: TrendingUp,
        title: "定制化服务",
        description: "根据客户需求提供个性化解决方案，满足不同行业的特殊要求。"
      },
      {
        icon: Clock,
        title: "快速交付",
        description: "完善的供应链管理，快速响应客户需求，缩短项目周期。"
      },
      {
        icon: Users,
        title: "技术支持",
        description: "专业的技术团队提供全方位支持，从设计到部署的全程服务。"
      }
    ],
    en: [
      {
        icon: Shield,
        title: "Reliability Assurance",
        description: "Industrial-grade design standards with strict quality control ensure stable operation in various environments."
      },
      {
        icon: TrendingUp,
        title: "Customization Services",
        description: "Provide personalized solutions according to customer needs, meeting special requirements of different industries."
      },
      {
        icon: Clock,
        title: "Fast Delivery",
        description: "Complete supply chain management, quick response to customer needs, shortening project cycles."
      },
      {
        icon: Users,
        title: "Technical Support",
        description: "Professional technical team provides comprehensive support, from design to deployment."
      }
    ]
  };

  return advantagesContent[language];
};

const Applications: React.FC = () => {
  const { language } = useLanguage()

  // 设置页面元数据
  usePageMetadata()

  const content = pageContent[language];

  const caseStudies = getCaseStudiesData(language);

  const advantages = getAdvantagesData(language);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section
        className="relative py-16 overflow-hidden"
        style={{
          backgroundImage: 'url(/images/background/about-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          height: '400px'
        }}
      >
        {/* 渐变覆盖层，增强文字可读性 */}
        {/* <div className="absolute inset-0 bg-gradient-to-r from-black/40 to-black/60"></div> */}
        <div className="relative container-custom h-full flex items-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white w-full"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">{content.hero.title}</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              {content.hero.subtitle}
            </p>
          </motion.div>
        </div>
      </section>

      {/* 统计数据部分移到独立section */}
      <section className="py-12 bg-card">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
              {language === 'zh'
                ? 'Linnuo单板计算机凭借强大的性能、丰富的接口和可靠的品质，广泛应用于智能制造、边缘计算、机器视觉等多个领域，为客户提供完整的解决方案。'
                : 'Linnuo single board computers, with their powerful performance, rich interfaces and reliable quality, are widely used in intelligent manufacturing, edge computing, machine vision and other fields, providing complete solutions for customers.'
              }
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-Linnuo-orange mb-1">16+</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'zh' ? '最大端口数' : 'Max Ports'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-Linnuo-orange mb-1">12+</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'zh' ? '最大串口数' : 'Max Serial Ports'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-Linnuo-orange mb-1">10+</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'zh' ? '最大USB数' : 'Max USB Ports'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-Linnuo-orange mb-1">90mm</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'zh' ? '最小尺寸' : 'Min Size'}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">{content.categories.title}</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {content.categories.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {content.industries.map((industry, index) => {
              const IconComponent = industry.icon;
              return (
                <motion.div
                  key={industry.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-card shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-border"
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={industry.image}
                      alt={industry.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-card-foreground mb-3">{industry.title}</h3>
                    <p className="text-muted-foreground mb-4">{industry.description}</p>

                    <div className="mb-4">
                      <h4 className="font-medium text-card-foreground mb-2">{content.common.features}</h4>
                      <ul className="space-y-1">
                        {Array.isArray(industry.features) ? industry.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        )) : null}
                      </ul>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-medium text-card-foreground mb-2">{content.common.benefits}</h4>
                      <ul className="space-y-1">
                        {Array.isArray(industry.benefits) ? industry.benefits.map((benefit: string, benefitIndex: number) => (
                          <li key={benefitIndex} className="flex items-center text-sm text-muted-foreground">
                            <TrendingUp className="w-4 h-4 text-Linnuo-orange mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        )) : null}
                      </ul>
                    </div>

                    {/* 暂时隐藏跳转按钮 */}
                    {false && (industry.title === '网络通信' ? (
                      <Link
                        href="/network-communication-case"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : industry.title === '机器视觉' ? (
                      <Link
                        href="/machine-vision-solutions"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : industry.title === '边缘计算' ? (
                      <Link
                        href="/edge-computing-solutions"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : industry.title === '智能制造' ? (
                      <Link
                        href="/smart-manufacturing-case"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : industry.title === '能源电力' ? (
                      <Link
                        href="/energy-power-solutions"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : industry.title === '小尺寸嵌入式' ? (
                      <Link
                        href="/compact-embedded-case"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ) : (
                      <Link
                        href="/applications/general"
                        className="block w-full bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 transition-colors duration-200 text-center"
                      >
                        查看案例
                      </Link>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-card">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              {language === 'zh' ? '成功案例' : 'Featured Projects'}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {language === 'zh' ? '真实项目展示Linnuo单板计算机的卓越性能' : 'Real projects showcasing the excellent performance of Linnuo single board computers'}
            </p>
          </div>

          <div className="space-y-12">
            {caseStudies.map((caseStudy, index) => (
                <motion.div
                  key={caseStudy.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-8 items-center`}
                >
                  <div className="flex-1">
                    <div className="flex items-center mb-4">
                      <div className="bg-gradient-to-r from-Linnuo-orange to-Linnuo-orange-light text-white px-3 py-1.5 text-xs font-semibold uppercase tracking-wide shadow-sm">
                        {caseStudy.industry}
                      </div>
                      <div className="ml-3 h-px bg-gradient-to-r from-Linnuo-orange/30 to-transparent flex-1"></div>
                    </div>
                    <h3 className="text-2xl font-bold text-foreground mb-4">{caseStudy.title}</h3>

                    <div className="bg-muted/50 p-4 mb-4">
                      <h4 className="font-semibold text-foreground mb-2">
                        {language === 'zh' ? '技术规格' : 'Technical Specifications'}
                      </h4>
                      <p className="text-muted-foreground text-sm">{caseStudy.specs}</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-foreground mb-2">
                        {language === 'zh' ? '面临挑战' : 'Challenges'}
                      </h4>
                      <p className="text-muted-foreground">{caseStudy.challenge}</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-foreground mb-2">
                        {language === 'zh' ? '解决方案' : 'Solution'}
                      </h4>
                      <p className="text-muted-foreground">{caseStudy.solution}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">
                        {language === 'zh' ? '实施效果' : 'Results'}
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {Array.isArray(caseStudy.results) ? caseStudy.results.map((result: string, resultIndex: number) => (
                          <div key={resultIndex} className="flex items-center">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" />
                            <span className="text-foreground">{result}</span>
                          </div>
                        )) : null}
                      </div>
                    </div>
                  </div>

                  <div className="flex-1">
                    <img
                      src={caseStudy.image}
                      alt={caseStudy.title}
                      className="w-full h-80 object-cover shadow-lg"
                    />
                  </div>
                </motion.div>
              )
            )}
          </div>
        </div>
      </section>

      {/* Advantages */}
      <section className="py-20 bg-muted/30">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">{content.advantages.title}</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {content.advantages.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {advantages.map((advantage, index) => {
              const IconComponent = advantage.icon;
              return (
                <motion.div
                  key={advantage.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-Linnuo-orange to-Linnuo-orange-dark flex items-center justify-center mx-auto mb-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-1">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">{advantage.title}</h3>
                  <p className="text-muted-foreground">{advantage.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-background">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-Linnuo-orange to-Linnuo-orange-light p-12 text-white shadow-2xl"
            >
              <h2 className="text-4xl font-bold mb-4">
                {language === 'zh' ? '开始您的项目' : 'Start Your Project'}
              </h2>
              <p className="text-xl mb-8 text-white/90">
                {language === 'zh'
                  ? '联系我们，获取专业的技术支持和定制化解决方案'
                  : 'Contact us for professional technical support and customized solutions'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/support"
                  className="inline-flex items-center px-8 py-3 bg-white text-Linnuo-orange font-medium hover:bg-gray-100 transition-all duration-300 hover:shadow-lg hover:transform hover:-translate-y-0.5"
                >
                  {language === 'zh' ? '申请样品' : 'Request Sample'}
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium hover:bg-white hover:text-Linnuo-orange transition-all duration-300 hover:shadow-lg hover:transform hover:-translate-y-0.5"
                >
                  {language === 'zh' ? '技术咨询' : 'Technical Consultation'}
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Applications;