'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Factory,
  Cog,
  BarChart3,
  Shield,
  Zap,
  ArrowRight,
  ExternalLink,
  Play,
  CheckCircle,
  Settings,
  Activity,
  TrendingUp,
  Cpu,
  Network,
  Eye,
  Wrench
} from "lucide-react"

export default function IndustrialAutomationPage() {
  // 工业自动化核心功能
  const coreFeatures = [
    {
      icon: Factory,
      title: "智能制造",
      description: "自动化生产线控制、设备监控和工艺优化",
      color: "bg-orange-500",
      stats: "效率提升 30%"
    },
    {
      icon: BarChart3,
      title: "数据分析",
      description: "实时生产数据采集、分析和可视化展示",
      color: "bg-blue-500",
      stats: "实时监控"
    },
    {
      icon: Shield,
      title: "质量控制",
      description: "AI驱动的质量检测和缺陷识别系统",
      color: "bg-green-500",
      stats: "99.5% 准确率"
    },
    {
      icon: Zap,
      title: "能耗优化",
      description: "智能能源管理和设备功耗优化",
      color: "bg-purple-500",
      stats: "节能 25%"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-orange-500/10 via-blue-500/5 to-green-500/10 overflow-hidden">
        <div className="container-custom relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-6 bg-orange-500/10 text-orange-600 hover:bg-orange-500/20 border-orange-200">
                <Factory className="w-4 h-4 mr-2" />
                工业自动化
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                智能制造
                <br />
                <span className="bg-gradient-to-r from-orange-600 to-blue-600 bg-clip-text text-transparent">
                  新时代
                </span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                使用Linnuo单板计算机构建下一代工业自动化系统。从智能制造到预测维护，实现工业4.0的全面升级。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
                  开始工业升级
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button size="lg" variant="outline">
                  查看案例
                  <Play className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  工业级可靠性
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  实时控制
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  模块化设计
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-orange-500/20 to-blue-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                <Factory className="h-32 w-32 text-orange-500" />
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-orange-500/10 flex items-center justify-center backdrop-blur-sm border border-orange-200/20">
                <Cog className="h-8 w-8 text-orange-500" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-blue-500/10 flex items-center justify-center backdrop-blur-sm border border-blue-200/20">
                <BarChart3 className="h-6 w-6 text-blue-500" />
              </div>
              <div className="absolute top-1/2 -left-8 w-16 h-16 bg-green-500/10 flex items-center justify-center backdrop-blur-sm border border-green-200/20">
                <Shield className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">核心功能</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              全面的工业自动化解决方案，提升生产效率和产品质量
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreFeatures.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50">
                  <CardHeader className="text-center">
                    <div className={`w-16 h-16 ${feature.color} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                    <Badge variant="secondary" className="w-fit mx-auto">
                      {feature.stats}
                    </Badge>
                  </CardHeader>
                  <CardContent className="text-center">
                    <CardDescription className="leading-relaxed">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-orange-500/10 to-blue-500/10">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              准备开始工业4.0转型？
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              加入智能制造革命，使用Linnuo构建下一代工业自动化系统
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
                立即开始
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline">
                查看文档
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
