'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Cpu,
  Brain,
  Eye,
  Mic,
  Camera,
  ArrowRight,
  ExternalLink,
  Play,
  CheckCircle,
  TrendingUp,
  BarChart3,
  Network,
  Layers,
  Cloud,
  Settings
} from "lucide-react"

export default function AIMachineLearningPage() {
  // AI核心能力
  const aiCapabilities = [
    {
      icon: Eye,
      title: "计算机视觉",
      description: "实时图像识别、物体检测、人脸识别等视觉AI应用",
      color: "bg-purple-500",
      stats: "95%+ 准确率"
    },
    {
      icon: Mic,
      title: "语音处理",
      description: "语音识别、自然语言处理、语音合成等音频AI技术",
      color: "bg-blue-500",
      stats: "多语言支持"
    },
    {
      icon: Brain,
      title: "深度学习",
      description: "神经网络训练、模型推理、迁移学习等深度学习框架",
      color: "bg-green-500",
      stats: "TensorFlow/PyTorch"
    },
    {
      icon: BarChart3,
      title: "数据分析",
      description: "大数据处理、预测分析、模式识别等智能数据挖掘",
      color: "bg-orange-500",
      stats: "实时分析"
    }
  ]

  // 技术架构
  const techStack = [
    {
      icon: Cpu,
      title: "边缘AI计算",
      description: "本地AI推理，低延迟响应",
      value: "< 50ms",
      label: "推理时间"
    },
    {
      icon: Cloud,
      title: "云端训练",
      description: "大规模模型训练和优化",
      value: "GPU",
      label: "加速计算"
    },
    {
      icon: Network,
      title: "模型部署",
      description: "轻量化模型，高效部署",
      value: "ONNX",
      label: "标准格式"
    },
    {
      icon: Layers,
      title: "框架支持",
      description: "多种AI框架兼容",
      value: "10+",
      label: "框架支持"
    }
  ]

  // 应用场景
  const applications = [
    {
      icon: Camera,
      title: "智能监控",
      description: "AI驱动的安防监控和行为分析",
      features: ["人脸识别", "异常检测", "行为分析", "实时告警"]
    },
    {
      icon: Brain,
      title: "智能助手",
      description: "语音交互和智能对话系统",
      features: ["语音识别", "自然语言理解", "智能问答", "多轮对话"]
    },
    {
      icon: TrendingUp,
      title: "预测分析",
      description: "基于机器学习的预测和决策支持",
      features: ["趋势预测", "异常检测", "风险评估", "智能推荐"]
    },
    {
      icon: Settings,
      title: "工业AI",
      description: "制造业智能化和质量控制",
      features: ["质量检测", "预测维护", "工艺优化", "智能调度"]
    }
  ]

  // AI模型库
  const modelLibrary = [
    {
      name: "YOLOv8",
      type: "目标检测",
      accuracy: "92%",
      speed: "45 FPS"
    },
    {
      name: "ResNet-50",
      type: "图像分类",
      accuracy: "96%",
      speed: "120 FPS"
    },
    {
      name: "BERT",
      type: "自然语言处理",
      accuracy: "94%",
      speed: "50ms"
    },
    {
      name: "MobileNet",
      type: "轻量化模型",
      accuracy: "89%",
      speed: "200 FPS"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-purple-500/10 via-blue-500/5 to-green-500/10 overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container-custom relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-6 bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 border-purple-200">
                <Brain className="w-4 h-4 mr-2" />
                人工智能 & 机器学习
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                释放AI的
                <br />
                <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  无限潜能
                </span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                使用Linnuo单板计算机构建强大的AI应用。从计算机视觉到自然语言处理，在边缘设备上实现高性能机器学习。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                  开始AI之旅
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button size="lg" variant="outline">
                  查看演示
                  <Play className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  边缘AI推理
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  多框架支持
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  高性能计算
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                <Brain className="h-32 w-32 text-purple-500" />
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-purple-500/10 flex items-center justify-center backdrop-blur-sm border border-purple-200/20">
                <Eye className="h-8 w-8 text-purple-500" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-blue-500/10 flex items-center justify-center backdrop-blur-sm border border-blue-200/20">
                <Mic className="h-6 w-6 text-blue-500" />
              </div>
              <div className="absolute top-1/2 -left-8 w-16 h-16 bg-green-500/10 flex items-center justify-center backdrop-blur-sm border border-green-200/20">
                <BarChart3 className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Capabilities */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">AI核心能力</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              强大的AI算力与先进算法相结合，为您提供完整的人工智能解决方案
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {aiCapabilities.map((capability, index) => {
              const IconComponent = capability.icon
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50">
                  <CardHeader className="text-center">
                    <div className={`w-16 h-16 ${capability.color} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-lg">{capability.title}</CardTitle>
                    <Badge variant="secondary" className="w-fit mx-auto">
                      {capability.stats}
                    </Badge>
                  </CardHeader>
                  <CardContent className="text-center">
                    <CardDescription className="leading-relaxed">{capability.description}</CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Tech Stack */}
      <section className="py-20 bg-muted/30">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">技术架构</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              从边缘到云端的完整AI技术栈，确保最佳性能和可扩展性
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {techStack.map((tech, index) => {
              const IconComponent = tech.icon
              return (
                <Card key={index} className="text-center group hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-purple-600 mb-1">{tech.value}</div>
                    <div className="text-sm text-muted-foreground mb-2">{tech.label}</div>
                    <CardTitle className="text-lg">{tech.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{tech.description}</CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Applications */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">应用场景</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              AI技术在各个领域的创新应用，推动智能化转型
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {applications.map((app, index) => {
              const IconComponent = app.icon
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{app.title}</CardTitle>
                        <CardDescription className="mt-1">{app.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2">
                      {app.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Model Library */}
      <section className="py-20 bg-muted/30">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">预训练模型库</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              丰富的预训练模型，开箱即用，快速部署AI应用
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {modelLibrary.map((model, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader className="text-center">
                  <CardTitle className="text-lg">{model.name}</CardTitle>
                  <Badge variant="outline">{model.type}</Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">准确率</span>
                    <span className="font-semibold text-green-600">{model.accuracy}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">速度</span>
                    <span className="font-semibold text-blue-600">{model.speed}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-purple-500/10 to-blue-500/10">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              准备构建您的AI应用？
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              加入AI革命，使用Linnuo打造下一代智能应用和解决方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                立即开始
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline">
                查看文档
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
