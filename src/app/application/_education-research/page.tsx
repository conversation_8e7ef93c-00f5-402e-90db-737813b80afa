'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  GraduationCap,
  BookOpen,
  Microscope,
  Cpu,
  ArrowRight,
  ExternalLink,
  Play,
  CheckCircle,
  Users,
  Lightbulb,
  Code,
  Beaker
} from "lucide-react"

export default function EducationResearchPage() {
  // 教育研究核心功能
  const coreFeatures = [
    {
      icon: BookOpen,
      title: "STEM教育",
      description: "科学、技术、工程、数学综合教育平台",
      color: "bg-indigo-500",
      stats: "跨学科学习"
    },
    {
      icon: Code,
      title: "编程教学",
      description: "从入门到高级的编程教育解决方案",
      color: "bg-blue-500",
      stats: "多语言支持"
    },
    {
      icon: Microscope,
      title: "科学实验",
      description: "数字化实验室和虚拟仿真实验",
      color: "bg-green-500",
      stats: "安全可靠"
    },
    {
      icon: Beaker,
      title: "研究平台",
      description: "支持学术研究和创新项目开发",
      color: "bg-purple-500",
      stats: "开放生态"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-indigo-500/10 via-blue-500/5 to-purple-500/10 overflow-hidden">
        <div className="container-custom relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-6 bg-indigo-500/10 text-indigo-600 hover:bg-indigo-500/20 border-indigo-200">
                <GraduationCap className="w-4 h-4 mr-2" />
                教育 & 研究
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                启发创新
                <br />
                <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  教育未来
                </span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                使用Linnuo单板计算机构建现代化教育平台。从STEM教育到科学研究，激发学习热情，培养创新能力。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Button size="lg" className="bg-indigo-500 hover:bg-indigo-600">
                  开始学习之旅
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button size="lg" variant="outline">
                  查看课程
                  <Play className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  互动学习
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  项目驱动
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  开源共享
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                <GraduationCap className="h-32 w-32 text-indigo-500" />
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-indigo-500/10 flex items-center justify-center backdrop-blur-sm border border-indigo-200/20">
                <BookOpen className="h-8 w-8 text-indigo-500" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-purple-500/10 flex items-center justify-center backdrop-blur-sm border border-purple-200/20">
                <Microscope className="h-6 w-6 text-purple-500" />
              </div>
              <div className="absolute top-1/2 -left-8 w-16 h-16 bg-blue-500/10 flex items-center justify-center backdrop-blur-sm border border-blue-200/20">
                <Code className="h-5 w-5 text-blue-500" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-20">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">教育核心功能</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              全面的教育技术解决方案，培养面向未来的创新人才
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreFeatures.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50">
                  <CardHeader className="text-center">
                    <div className={`w-16 h-16 ${feature.color} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                    <Badge variant="secondary" className="w-fit mx-auto">
                      {feature.stats}
                    </Badge>
                  </CardHeader>
                  <CardContent className="text-center">
                    <CardDescription className="leading-relaxed">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Learning Paths */}
      <section className="py-20 bg-muted/30">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">学习路径</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              从基础到高级，系统化的学习体系
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl">初学者</CardTitle>
                <CardDescription>适合编程和硬件初学者</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    基础电子知识
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    图形化编程
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    简单项目实践
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center mb-4">
                  <Lightbulb className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl">进阶者</CardTitle>
                <CardDescription>有一定基础的学习者</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Python编程
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    传感器应用
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    物联网项目
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mb-4">
                  <Cpu className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl">专家级</CardTitle>
                <CardDescription>高级开发者和研究者</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    AI/ML应用
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    系统级开发
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    研究项目
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-500/10 to-purple-500/10">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              准备开启学习之旅？
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              加入全球教育创新社区，使用Linnuo构建面向未来的教育解决方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-indigo-500 hover:bg-indigo-600">
                立即开始
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline">
                查看资源
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
