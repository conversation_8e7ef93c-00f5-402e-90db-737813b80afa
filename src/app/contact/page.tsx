
'use client'


import { Card, CardContent } from '@/components/ui/card'
import { Phone, Mail, Clock } from 'lucide-react'
import { useContactTranslations } from '@/hooks/use-translations'
import { useCompanyInfo } from '@/hooks/use-api'
import { usePageMetadata } from '@/hooks/use-page-metadata'

export default function ContactPage() {
  const { t } = useContactTranslations()

  // 设置页面元数据
  usePageMetadata()

  // 获取动态公司信息
  const { data: companyInfo, isLoading: isCompanyLoading } = useCompanyInfo()

  // 使用动态数据或fallback到默认值
  const contactInfo = companyInfo ? {
    businessPhone: companyInfo.businessPhone,
    businessHours: companyInfo.businessHours,
    businessEmail: companyInfo.businessEmail,
    companyName: companyInfo.companyName,
    companySlogan: companyInfo.companySlogan
  } : {
    // Fallback默认值
    businessPhone: "+86-152-7791-5606",
    businessHours: "(Mon-Fr<PERSON> 9:00-18:00)",
    businessEmail: "<EMAIL>",
    companyName: "Linnuo",
    companySlogan: "Embedded Computing Solutions"
  }

  // 如果正在加载，显示加载状态
  if (isCompanyLoading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <div className="animate-spin h-12 w-12 border-b-2 border-blue-600 rounded-full mx-auto mb-4"></div>
            <p className="text-gray-600">{t("loadingText")}</p>
          </div>
        </div>
      </div>
    )
  }

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact Linnuo",
    "description": "Get in touch with Linnuo for sales and general inquiries.",
    "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/contact`,
    "mainEntity": {
      "@type": "Organization",
      "name": contactInfo.companyName,
      "logo": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/logo-top-bg-white.svg`,
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": contactInfo.businessPhone,
        "contactType": "sales",
        "areaServed": "Worldwide",
        "availableLanguage": ["English", "Chinese"]
      }
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="relative py-24 lg:py-32 bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center" style={{
         backgroundImage: 'url(/images/background/about-bg.jpg)',
         backgroundSize: 'cover',
         backgroundPosition: 'center',
         backgroundRepeat: 'no-repeat'
       }}>
        <div className="container mx-auto px-4 relative z-10">
           <div className="text-center mb-12">
             <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 drop-shadow-lg">{t("title")}</h1>
             <p className="text-xl text-white max-w-3xl mx-auto drop-shadow-md">
               {t("subtitle")}
             </p>
           </div>
         </div>
      </section>
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">

        {/* Contact Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16 max-w-2xl mx-auto">
          {/* Business Sales */}
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-blue-100 flex items-center justify-center mx-auto mb-6">
                <Phone className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{t("contactInfo.business.title")}</h3>
              <div className="space-y-2 text-gray-600">
                <p className="font-semibold text-lg text-blue-600">{contactInfo.businessPhone}</p>
                <p className="text-sm">{contactInfo.businessHours}</p>
                <p className="text-sm">{contactInfo.businessEmail}</p>
              </div>
            </CardContent>
          </Card>

          {/* Email Contact */}
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-orange-100 flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{t("contactInfo.email.title")}</h3>
              <div className="space-y-2 text-gray-600">
                <p className="font-semibold text-lg text-orange-600">{contactInfo.businessEmail}</p>
                <p className="text-sm">{t("contactInfo.email.description")}</p>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* Business Hours */}
        <Card className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 max-w-2xl mx-auto">
          <CardContent className="p-8 text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <Clock className="w-8 h-8 text-blue-600" />
              <h3 className="text-2xl font-bold text-gray-900">{t("contactInfo.hours.title")}</h3>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">{t("contactInfo.hours.business")}</h4>
              <p className="text-gray-600">{contactInfo.businessHours}</p>
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
    </>
  )
}
