import { MetadataRoute } from 'next'
// 临时注释以解决构建卡住问题
// import { getProducts, getNewsArticles } from '@/data/unified-data-fetcher'
import { Language } from '@/lib/api-with-locale'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'
  const languages: Language[] = ['en', 'zh']
  
  const routes: MetadataRoute.Sitemap = []

  // 1. 添加静态页面路由 (为每种语言)
  const staticPages = [
    '',
    '/products',
    '/products/compare',
    '/news',
    '/downloads',
    '/about',
    '/contact',
    '/application',
    '/application/energy-power-solutions',
    '/application/ai-machine-learning',
    '/application/industrial-automation',
    '/application/education-research',
  ]

  languages.forEach(lang => {
    staticPages.forEach(page => {
      routes.push({
        url: `${baseUrl}/${lang}${page}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: page === '' ? 1.0 : 0.8,
      })
    })
  })

  // 2. 暂时注释动态路由以解决构建问题
  // TODO: 在生产环境中恢复这些动态路由
  /*
  // 2. 添加动态产品路由 (为每种语言)
  for (const lang of languages) {
    try {
      const products = await getProducts(lang)
      products.forEach(product => {
        routes.push({
          url: `${baseUrl}/${lang}/products/${product.slug}`,
          lastModified: new Date(product.updatedAt || product.createdAt || Date.now()),
          changeFrequency: 'weekly',
          priority: 0.7,
        })
      })
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Failed to get products for ${lang}:`, error)
      }
    }
  }

  // 3. 添加动态新闻路由 (为每种语言)
  for (const lang of languages) {
    try {
      const articles = await getNewsArticles(lang)
      articles.forEach(article => {
        routes.push({
          url: `${baseUrl}/${lang}/news/${article.slug}`,
          lastModified: new Date(article.date),
          changeFrequency: 'monthly',
          priority: 0.6,
        })
      })
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Failed to get articles for ${lang}:`, error)
      }
    }
  }
  */

  return routes
}
