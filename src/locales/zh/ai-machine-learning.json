{"hero": {"title": "人工智能与机器学习", "subtitle": "在边缘构建智能AI应用。利用机器学习、计算机视觉和自然语言处理的力量，创建能够实时学习和适应的智能系统。", "startBuilding": "开始构建", "viewDocumentation": "查看文档"}, "keyCapabilities": {"title": "核心功能", "subtitle": "构建先进AI和机器学习解决方案所需的一切", "features": {"computerVision": {"title": "计算机视觉", "description": "在边缘进行物体检测、面部识别和图像分类"}, "naturalLanguageProcessing": {"title": "自然语言处理", "description": "语音识别、文本分析和对话式AI应用"}, "predictiveAnalytics": {"title": "预测分析", "description": "实时数据分析和机器学习推理用于决策制定"}, "edgeAIInference": {"title": "边缘AI推理", "description": "在本地运行AI模型，实现低延迟、注重隐私的应用"}}}, "featuredProjects": {"title": "精选项目", "subtitle": "您今天就可以构建的真实AI和机器学习项目", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "projects": {"aiPoweredSecuritySystem": {"title": "AI驱动的安全系统", "description": "具有面部识别、行为分析和自动威胁检测的智能监控", "duration": "3-4周", "components": "<PERSON><PERSON><PERSON><PERSON><PERSON>, AI加速器, IP摄像头, Edge TPU", "features": "面部识别, 行为分析, 实时警报, 隐私保护"}, "smartVoiceAssistant": {"title": "智能语音助手", "description": "具有自然语言理解和智能家居控制的AI助手", "duration": "2-3周", "components": "LinnuoDelta 3, 麦克风阵列, 扬声器, AI芯片", "features": "语音识别, 自然语言理解, 智能家居控制, 离线处理"}, "predictiveMaintenanceSystem": {"title": "预测性维护系统", "description": "使用机器学习预测设备故障的工业AI解决方案", "duration": "4-5周", "components": "LinnuoSigma, 传感器套件, 数据记录器, ML加速器", "features": "异常检测, 预测分析, 实时监控, 维护调度"}}, "viewProjectGuide": "查看项目指南"}, "tutorialsResources": {"title": "教程与资源", "subtitle": "通过我们全面的AI和机器学习指南逐步学习", "tutorials": {"gettingStartedAI": {"title": "AI开发入门", "description": "学习在Linnuo上进行AI开发的基础知识", "type": "文档指南", "duration": "45分钟"}, "buildingComputerVisionApp": {"title": "构建计算机视觉应用", "description": "创建一个实时物体检测和识别系统", "type": "视频教程", "duration": "1小时"}, "deployingMLModels": {"title": "部署机器学习模型", "description": "学习如何在边缘设备上优化和部署ML模型", "type": "视频教程", "duration": "1.5小时"}}, "watchTutorial": "观看教程", "downloadGuide": "下载指南"}, "callToAction": {"title": "准备构建您的AI项目？", "subtitle": "开始使用Linnuo，将人工智能的力量带到您的应用中", "shopLinnuo": "购买<PERSON><PERSON>o", "joinCommunity": "加入社区"}, "common": {"components": "组件", "features": "功能", "duration": "持续时间", "difficulty": "难度"}}