{"hero": {"title": "应用案例", "subtitle": "探索Linnuo单板计算机在各个领域的创新应用，从物联网到人工智能，从工业自动化到教育研究。", "exploreProjects": "探索项目", "viewDocumentation": "查看文档"}, "categories": {"title": "应用领域", "subtitle": "Linnuo单板计算机在多个行业和领域中发挥着重要作用", "iotSmartHome": {"title": "物联网智能家居", "description": "构建智能家居系统，实现设备互联和自动化控制", "applications": ["智能照明控制", "温度湿度监控", "安防监控系统", "智能门锁", "语音助手集成"]}, "aiMachineLearning": {"title": "人工智能与机器学习", "description": "利用强大的计算能力进行AI模型训练和推理", "applications": ["图像识别", "自然语言处理", "边缘计算", "机器视觉", "深度学习推理"]}, "industrialAutomation": {"title": "工业自动化", "description": "为工业4.0提供可靠的计算和控制解决方案", "applications": ["生产线控制", "设备监控", "质量检测", "预测性维护", "数据采集"]}, "educationResearch": {"title": "教育与研究", "description": "为学术研究和教学提供灵活的计算平台", "applications": ["编程教学", "科研项目", "原型开发", "实验平台", "创客教育"]}, "compactEmbedded": {"title": "小尺寸嵌入式", "description": "紧凑型设计，适用于空间受限的应用场景", "applications": ["紧凑设计", "低功耗", "高集成度", "可靠性高"]}, "energyPower": {"title": "能源电力", "description": "为能源管理和电力系统提供智能化解决方案", "applications": ["能源监控", "智能调度", "故障诊断", "数据分析"]}, "networkCommunication": {"title": "网络通信", "description": "构建高性能网络设备和通信系统", "applications": ["网络路由器", "防火墙设备", "VPN网关", "网络存储", "通信基站"]}}, "featuredProjects": {"title": "精选项目", "subtitle": "查看使用Linnuo单板计算机构建的优秀项目案例", "viewAllProjects": "查看所有项目", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "projects": {"smartMirror": {"title": "智能魔镜", "description": "集成天气、日历、新闻等信息的智能显示镜", "tags": ["物联网", "显示", "传感器", "语音控制"]}, "droneController": {"title": "无人机控制器", "description": "高性能无人机飞行控制和图像处理系统", "tags": ["飞行控制", "图像处理", "实时系统", "传感器融合"]}, "iotGateway": {"title": "物联网网关", "description": "连接多种设备的智能物联网数据网关", "tags": ["物联网", "数据处理", "网络", "协议转换"]}}}, "callToAction": {"title": "开始您的项目", "subtitle": "选择合适的Linnuo单板计算机，开始构建您的创新项目"}, "benefits": {"industrialAutomation": ["提高生产效率和产品质量", "降低人工成本和运营风险", "实现设备智能化管理", "支持工业4.0数字化转型"], "iotSmartHome": ["设备互联互通", "智能化场景控制", "远程监控管理", "节能环保解决方案"], "aiMachineLearning": ["强大的AI计算能力", "支持多种深度学习框架", "边缘计算降低延迟", "灵活的模型部署方案"], "educationResearch": ["丰富的教学资源", "开放的开发环境", "项目实践平台", "创新能力培养"], "compactEmbedded": ["节省空间", "降低功耗", "简化部署", "提高可靠性"], "energyPower": ["优化能源使用", "提高系统效率", "预防故障发生", "降低运营成本"], "networkCommunication": ["高速数据传输", "稳定网络连接", "安全通信保障", "灵活网络配置"]}, "caseStudies": {"smartFactory": {"title": "智能工厂自动化系统", "industry": "智能制造", "challenge": "传统制造业面临生产效率低、质量控制难、人工成本高等挑战，急需数字化转型。", "solution": "采用Linnuo单板计算机构建智能工厂系统，集成传感器网络、机器视觉和AI算法，实现生产过程的全面自动化和智能化管理。", "results": ["生产效率提升40%", "产品质量合格率达到99.5%", "人工成本降低30%", "设备故障率减少60%"], "specs": "Linnuo-X1 Pro, 8GB RAM, 64GB eMMC, 工业级温度范围"}, "edgeComputing": {"title": "边缘计算数据中心", "industry": "边缘计算", "challenge": "传统云计算架构在处理实时数据时存在延迟高、带宽成本大的问题，需要在边缘侧部署计算能力。", "solution": "使用Linnuo单板计算机构建分布式边缘计算节点，在数据产生的源头进行实时处理和分析，减少数据传输延迟。", "results": ["数据处理延迟降低80%", "网络带宽使用减少50%", "系统响应时间提升3倍", "运营成本降低35%"], "specs": "Linnuo-Edge, 16GB RAM, 128GB SSD, 多网口配置"}, "machineVision": {"title": "机器视觉质检系统", "industry": "机器视觉", "challenge": "传统人工质检效率低、主观性强、无法24小时连续工作，难以满足现代制造业的质量要求。", "solution": "基于Linnuo单板计算机开发高精度机器视觉系统，结合深度学习算法实现产品缺陷的自动检测和分类。", "results": ["检测精度达到99.8%", "检测速度提升10倍", "24小时不间断工作", "质检成本降低70%"], "specs": "Linnuo-Vision, GPU加速, 4K摄像头支持, 实时图像处理"}, "smartGrid": {"title": "智能电网监控平台", "industry": "能源电力", "challenge": "传统电网缺乏实时监控和智能调度能力，难以应对新能源接入和负荷波动的挑战。", "solution": "部署Linnuo单板计算机构建智能电网监控系统，实现电网状态的实时监测、故障预警和智能调度。", "results": ["电网稳定性提升45%", "故障响应时间缩短60%", "能源利用效率提高25%", "维护成本降低40%"], "specs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 工业级设计, 多协议支持, 冗余电源"}}, "advantages": {"reliability": {"title": "可靠性保证", "description": "工业级设计，通过严格测试，确保在恶劣环境下稳定运行"}, "customization": {"title": "定制化服务", "description": "提供灵活的硬件配置和软件定制，满足不同应用场景需求"}, "delivery": {"title": "快速交付", "description": "完善的供应链体系，确保产品快速交付和技术支持"}, "support": {"title": "专业支持", "description": "专业的技术团队提供全方位支持，从方案设计到售后服务"}}}