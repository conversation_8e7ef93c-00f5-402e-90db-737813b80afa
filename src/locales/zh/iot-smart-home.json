{"hero": {"title": "物联网与智能家居", "subtitle": "将您的家庭转变为智能、互联的生态系统。构建自动化系统、监控解决方案和智能设备，让生活更轻松、更高效。", "startBuilding": "开始构建", "viewDocumentation": "查看文档"}, "keyCapabilities": {"title": "核心功能", "subtitle": "构建全面的物联网和智能家居解决方案所需的一切", "features": {"environmentalMonitoring": {"title": "环境监测", "description": "实时监测温度、湿度、空气质量和其他环境参数"}, "securitySystems": {"title": "安全系统", "description": "创建包含摄像头、传感器和自动警报的综合安全解决方案"}, "smartLighting": {"title": "智能照明", "description": "具有调度、运动检测和能源优化的自动照明控制"}, "connectivityHub": {"title": "连接中心", "description": "连接和管理所有物联网设备和传感器的中央枢纽"}}}, "featuredProjects": {"title": "精选项目", "subtitle": "您今天就可以构建的真实物联网和智能家居项目", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "projects": {"smartHomeCentralHub": {"title": "智能家居中央枢纽", "description": "具有语音控制、移动应用和AI驱动调度的完整家庭自动化系统", "duration": "2-3周", "components": "<PERSON><PERSON>oSigma, 语音识别模块, 各种传感器, 移动应用", "features": "语音控制, 移动应用, AI调度, 能源监控"}, "environmentalMonitoringStation": {"title": "环境监测站", "description": "监测空气质量、温度、湿度和噪音水平的综合系统", "duration": "1-2周", "components": "LinnuoDelta 3, 环境传感器, 显示屏, 数据记录器", "features": "实时监测, 数据记录, 警报系统, 远程访问"}, "smartSecuritySystem": {"title": "智能安全系统", "description": "具有面部识别、运动检测和自动警报的AI驱动安全解决方案", "duration": "2-3周", "components": "LinnuoSigma, IP摄像头, 运动传感器, 警报系统", "features": "面部识别, 运动检测, 自动警报, 远程监控"}}, "viewProjectGuide": "查看项目指南"}, "tutorialsResources": {"title": "教程与资源", "subtitle": "通过我们全面的指南和视频教程逐步学习", "tutorials": {"gettingStartedIoT": {"title": "物联网入门", "description": "学习物联网开发的基础知识和最佳实践", "type": "文档指南", "duration": "30分钟"}, "buildingSmartThermostat": {"title": "构建智能恒温器", "description": "创建一个可以远程控制的智能恒温器", "type": "视频教程", "duration": "45分钟"}, "advancedHomeSecuritySetup": {"title": "高级家庭安全设置", "description": "实施包含摄像头和传感器的完整安全系统", "type": "视频教程", "duration": "1.5小时"}}, "watchTutorial": "观看教程", "downloadGuide": "下载指南"}, "callToAction": {"title": "准备构建您的智能家居？", "subtitle": "开始使用Linnuo，将您的家庭转变为智能、互联的空间", "shopLinnuo": "购买<PERSON><PERSON>o", "joinCommunity": "加入社区"}, "common": {"components": "组件", "features": "功能", "duration": "持续时间", "difficulty": "难度"}}