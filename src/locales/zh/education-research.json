{"hero": {"title": "教育与研究", "subtitle": "为学习和发现构建强大的教育工具和研究平台。从交互式学习系统到先进的研究设备，创建激发创新和知识的解决方案。", "startBuilding": "开始构建", "viewDocumentation": "查看文档"}, "keyCapabilities": {"title": "核心功能", "subtitle": "构建创新教育和研究解决方案所需的一切", "features": {"interactiveLearning": {"title": "交互式学习", "description": "创建引人入胜的交互式教育体验和学习平台"}, "dataCollection": {"title": "数据收集", "description": "用于科学研究和实验的高精度数据采集系统"}, "labAutomation": {"title": "实验室自动化", "description": "自动化实验室流程和设备控制，提高研究效率"}, "collaborativePlatforms": {"title": "协作平台", "description": "支持远程学习和研究协作的连接平台"}}}, "featuredProjects": {"title": "精选项目", "subtitle": "您今天就可以构建的真实教育和研究项目", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "projects": {"smartClassroomSystem": {"title": "智能教室系统", "description": "具有交互式显示、自动出勤和智能环境控制的完整教室解决方案", "duration": "4-5周", "components": "Lin<PERSON>oSigma, 交互式显示器, 传感器套件, 摄像头系统", "features": "交互式学习, 出勤跟踪, 环境控制, 远程访问"}, "researchDataLogger": {"title": "研究数据记录器", "description": "用于科学实验和长期研究项目的高精度数据采集系统", "duration": "2-3周", "components": "LinnuoDelta 3, 精密传感器, 数据存储, 网络模块", "features": "高精度测量, 长期记录, 远程监控, 数据分析"}, "virtualLabPlatform": {"title": "虚拟实验室平台", "description": "支持远程实验和协作研究的虚拟实验室环境", "duration": "5-6周", "components": "<PERSON><PERSON><PERSON>S<PERSON>, 实验室设备接口, 视频系统, 云平台", "features": "远程实验, 实时协作, 设备控制, 结果共享"}}, "viewProjectGuide": "查看项目指南"}, "tutorialsResources": {"title": "教程与资源", "subtitle": "通过我们全面的教育和研究指南逐步学习", "tutorials": {"buildingEducationalTools": {"title": "构建教育工具", "description": "学习如何创建交互式教育应用和学习平台", "type": "文档指南", "duration": "1小时"}, "scientificDataAcquisition": {"title": "科学数据采集", "description": "设置精确的数据采集系统用于研究和实验", "type": "视频教程", "duration": "1.5小时"}, "labAutomationSetup": {"title": "实验室自动化设置", "description": "实施自动化解决方案以提高实验室效率和准确性", "type": "视频教程", "duration": "2小时"}}, "watchTutorial": "观看教程", "downloadGuide": "下载指南"}, "callToAction": {"title": "准备构建您的教育解决方案？", "subtitle": "开始使用<PERSON><PERSON><PERSON>，为学习和研究创建创新的工具和平台", "shopLinnuo": "购买<PERSON><PERSON>o", "joinCommunity": "加入社区"}, "common": {"components": "组件", "features": "功能", "duration": "持续时间", "difficulty": "难度"}}