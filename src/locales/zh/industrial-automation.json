{"hero": {"title": "工业自动化", "subtitle": "构建智能工业系统和自动化解决方案。从过程控制到预测性维护，创建提高效率、质量和安全性的强大工业应用。", "startBuilding": "开始构建", "viewDocumentation": "查看文档"}, "keyCapabilities": {"title": "核心功能", "subtitle": "构建先进工业自动化解决方案所需的一切", "features": {"processControl": {"title": "过程控制", "description": "制造过程和工业设备的实时控制系统"}, "qualityInspection": {"title": "质量检测", "description": "使用计算机视觉和机器学习的自动化质量控制"}, "predictiveMaintenance": {"title": "预测性维护", "description": "监控设备健康状况，在故障发生前预测维护需求"}, "dataAcquisition": {"title": "数据采集", "description": "实时收集、处理和分析工业传感器数据"}}}, "featuredProjects": {"title": "精选项目", "subtitle": "您今天就可以构建的真实工业自动化项目", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "projects": {"smartFactoryControlSystem": {"title": "智能工厂控制系统", "description": "具有实时监控、控制和优化的完整工厂自动化", "duration": "6-8周", "components": "<PERSON><PERSON>oSigma, 工业I/O, HMI显示器, 网络基础设施", "features": "实时控制, SCADA集成, 远程监控, 数据分析"}, "qualityInspectionStation": {"title": "质量检测站", "description": "使用AI驱动的计算机视觉进行自动化产品质量检测", "duration": "3-4周", "components": "LinnuoDelta 3, 工业摄像头, 照明系统, 传送带接口", "features": "缺陷检测, 质量评分, 实时处理, 数据分析"}, "predictiveMaintenanceSystem": {"title": "预测性维护系统", "description": "使用机器学习预测设备故障的工业物联网解决方案", "duration": "4-5周", "components": "Lin<PERSON>oSigma, 传感器套件, 数据记录器, 边缘计算单元", "features": "异常检测, 预测分析, 实时监控, 维护调度"}}, "viewProjectGuide": "查看项目指南"}, "tutorialsResources": {"title": "教程与资源", "subtitle": "通过我们全面的工业自动化指南逐步学习", "tutorials": {"industrialIoTBasics": {"title": "工业物联网基础", "description": "学习工业物联网系统和传感器集成的基础知识", "type": "文档指南", "duration": "1小时"}, "buildingControlSystem": {"title": "构建控制系统", "description": "创建用于工业过程控制的实时监控和控制系统", "type": "视频教程", "duration": "1.5小时"}, "implementingPredictiveMaintenance": {"title": "实施预测性维护", "description": "部署机器学习驱动的预测性维护解决方案", "type": "视频教程", "duration": "2小时"}}, "watchTutorial": "观看教程", "downloadGuide": "下载指南"}, "callToAction": {"title": "准备构建您的工业解决方案？", "subtitle": "开始使用Lin<PERSON>o，将智能自动化和控制带到您的工业应用中", "shopLinnuo": "购买<PERSON><PERSON>o", "joinCommunity": "加入社区"}, "common": {"components": "组件", "features": "功能", "duration": "持续时间", "difficulty": "难度"}}