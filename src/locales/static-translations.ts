// 静态翻译文件，用于解决生产环境下的翻译加载问题
export const staticTranslations = {
  en: {
    "about": {
        "title": "About Linnuo",
        "intro": "<PERSON><PERSON><PERSON> is a leading innovator in single board computer technology, empowering makers, developers, and businesses to bring their ideas to life.",
        "mission": {
            "title": "Our Mission",
            "content": "To democratize computing by providing powerful, accessible, and versatile single board computers that enable innovation across industries and communities. We are committed to breaking down technological barriers and making advanced computing technology accessible to everyone."
        },
        "story": {
            "title": "Our Story",
            "content": "Founded with a vision to bridge the gap between traditional computing and embedded systems, Linnuo has been at the forefront of single board computer development since our inception. From our initial innovative ideas to today's industry leadership position, we have always adhered to the perfect combination of technological innovation and user experience."
        },
        "products": {
            "title": "Our Products",
            "content": "From the powerful LinnuoSigma to the compact LinnuoMu, our product line offers solutions for every project scale and complexity. Each product is carefully designed to ensure industry-leading performance, stability, and ease of use."
        },
        "vision": {
            "title": "Our Vision",
            "content": "To become the global technology leader in single board computing, driving the popularization and development of embedded computing technology, and providing strong technical support for digital transformation."
        },
        "values": {
            "title": "Core Values",
            "innovation": {
                "title": "Innovation Driven",
                "content": "Continuous technological innovation is the core driving force of our development"
            },
            "quality": {
                "title": "Quality First",
                "content": "Strict quality control ensures the excellent performance of every product"
            },
            "customer": {
                "title": "Customer Centric",
                "content": "Customer-oriented approach to provide the best products and services"
            },
            "collaboration": {
                "title": "Open Collaboration",
                "content": "Working with global partners to jointly promote industry development"
            },
            "partnership": {
                "title": "Future Together",
                "content": "Working hand in hand with partners to explore markets together, create a better future, and achieve mutually beneficial development goals."
            }
        },
        "team": {
            "title": "Our Team",
            "content": "Linnuo has a professional team composed of senior engineers, product experts, and technical consultants. Our team members come from globally renowned technology companies and have extensive experience in embedded systems, hardware design, software development, and other fields."
        },
        "achievements": {
            "title": "Achievements",
            "stats": {
                "products": {
                    "number": "50+",
                    "label": "Product Models"
                },
                "customers": {
                    "number": "10000+",
                    "label": "Global Customers"
                },
                "countries": {
                    "number": "80+",
                    "label": "Countries Served"
                },
                "experience": {
                    "number": "15+",
                    "label": "Years of Experience"
                }
            }
        },
        "technology": {
            "title": "Technical Advantages",
            "content": "We have core technical advantages in single board computer design, embedded system development, AI edge computing, and other fields. Through continuous R&D investment and technological innovation, we provide customers with the most advanced solutions."
        }
    },
    "after-sales": {
        "title": "After-sales Service",
        "subtitle": "Thank you very much for purchasing Linnuo products. In order to protect your due rights, please read the following content in detail to fully understand the perfect after-sales service provided by our company.",
        "content": "Thank you very much for purchasing Linnuo products. In order to protect your due rights, please read the following content in detail to fully understand the perfect after-sales service provided by our company.\n\n1.Before using the products of our company, please be sure to read the user manual attached to the products carefully, and operate in accordance with the described usage methods. When the customer sends the faulty product for repair, he should attach the repair form with the product to request repair.\n\n2.One-year maintenance service for using our company's products.\nA.The one-year period starts from the date of shipment.\nB.Products and peripheral products not manufactured by our company are subject to the warranty conditions provided by the original factory unless otherwise specified.\nC.Customers and dealers are requested to back up hard disk data before sending it for repair. Our company will not backup, restore or transfer data on behalf of you.\nD.Before sending all kinds of products for repair, please uninstall the illegal software in the product by yourself, otherwise any relevant legal liability caused by it has nothing to do with the company.\nE.General faulty products, please do not attach any accessories. If accessories are required, please note in detail in the repair application form. If the accessories are not specified in detail, our company will not be responsible for the loss of accessories.\n\n3.In the following cases, the company will not provide the above warranty service even within the warranty period:\nA.Damage caused by not following the usage method provided in the user manual.\nB.Damage caused by falling and vibration during personal transportation and carrying.\nC.Damaged by fire, earthquake, flood, electric shock or other natural disasters, pollution, or use of a wrong power source.\nD.Damaged due to improper storage environment, such as high temperature, high humidity, or close to volatile products.\nE.Damaged due to battery wear and tear, replacement of a new battery, and electrolyte outflow.\nF.If it is not repaired or changed by the company's technicians and causes damage, the company will charge an additional fee.\nG.The product serial number attached to the product has been altered or destroyed.\nH.Warranty items not listed by our company.\n\n4.The customer needs to bear the risk of damage and the shipping cost of shipping the product to be repaired to the company or the original distributor.\n\n5.To ensure maintenance efficiency and quality, please download the repair application form. The company's repair policy takes priority for customers who have filled out the repair application form.\n\nIf you have any questions about the use of our products, please feel free to contact our FAE staff, or email your questions to: <EMAIL>. Thank you！",
        "introduction": {
            "title": "Service Commitment",
            "content": "Thank you very much for purchasing Linnuo products. In order to protect your due rights, please read the following content in detail to fully understand the perfect after-sales service provided by our company."
        },
        "beforeUse": {
            "title": "Before Use",
            "content": "Before using the products of our company, please be sure to read the user manual attached to the products carefully, and operate in accordance with the described usage methods. When the customer sends the faulty product for repair, he should attach the repair form with the product to request repair."
        },
        "warrantyService": {
            "title": "One-year Maintenance Service",
            "subtitle": "One-year maintenance service for using our company's products",
            "terms": [
                {
                    "label": "A.",
                    "content": "The one-year period starts from the date of shipment."
                },
                {
                    "label": "B.",
                    "content": "Products and peripheral products not manufactured by our company are subject to the warranty conditions provided by the original factory unless otherwise specified."
                },
                {
                    "label": "C.",
                    "content": "Customers and dealers are requested to back up hard disk data before sending it for repair. Our company will not backup, restore or transfer data on behalf of you."
                },
                {
                    "label": "D.",
                    "content": "Before sending all kinds of products for repair, please uninstall the illegal software in the product by yourself, otherwise any relevant legal liability caused by it has nothing to do with the company."
                },
                {
                    "label": "E.",
                    "content": "General faulty products, please do not attach any accessories. If accessories are required, please note in detail in the repair application form. If the accessories are not specified in detail, our company will not be responsible for the loss of accessories."
                }
            ]
        },
        "warrantyExclusions": {
            "title": "Warranty Exclusions",
            "subtitle": "In the following cases, the company will not provide the above warranty service even within the warranty period:",
            "items": [
                {
                    "label": "A.",
                    "content": "Damage caused by not following the usage method provided in the user manual."
                },
                {
                    "label": "B.",
                    "content": "Damage caused by falling and vibration during personal transportation and carrying."
                },
                {
                    "label": "C.",
                    "content": "Damaged by fire, earthquake, flood, electric shock or other natural disasters, pollution, or use of a wrong power source."
                },
                {
                    "label": "D.",
                    "content": "Damaged due to improper storage environment, such as high temperature, high humidity, or close to volatile products."
                },
                {
                    "label": "E.",
                    "content": "Damaged due to battery wear and tear, replacement of a new battery, and electrolyte outflow."
                },
                {
                    "label": "F.",
                    "content": "If it is not repaired or changed by the company's technicians and causes damage, the company will charge an additional fee."
                },
                {
                    "label": "G.",
                    "content": "The product serial number attached to the product has been altered or destroyed."
                },
                {
                    "label": "H.",
                    "content": "Warranty items not listed by our company."
                }
            ]
        },
        "additionalTerms": {
            "title": "Additional Terms",
            "items": [
                {
                    "number": "4.",
                    "content": "The customer needs to bear the risk of damage and the shipping cost of shipping the product to be repaired to the company or the original distributor."
                },
                {
                    "number": "5.",
                    "content": "To ensure maintenance efficiency and quality, please download the repair application form. The company's repair policy takes priority for customers who have filled out the repair application form."
                }
            ]
        },
        "contactSupport": {
            "title": "Technical Support & Contact",
            "subtitle": "If you have any questions about the use of our products, please feel free to contact our FAE staff, or email your questions to us.",
            "content": "If you have any questions about the use of our products, please feel free to contact our FAE staff, or email your questions to: <EMAIL>. Thank you!",
            "downloadForm": "Download Repair Application Form",
            "repairForm": {
                "title": "Repair Application Form",
                "description": "Download repair application form for priority repair service"
            },
            "emailSupport": {
                "title": "Email Support",
                "description": "Send your questions to our technical support email",
                "email": "<EMAIL>"
            },
            "phoneSupport": {
                "title": "Technical Support Hotline",
                "description": "Contact our FAE technical experts",
                "phone": "+86-152-7791-5606"
            }
        }
    },
    "ai-machine-learning": {
        "hero": {
            "title": "AI & Machine Learning",
            "subtitle": "Build intelligent AI applications at the edge. Harness the power of machine learning, computer vision, and natural language processing to create smart systems that learn and adapt in real-time.",
            "startBuilding": "Start Building",
            "viewDocumentation": "View Documentation"
        },
        "keyCapabilities": {
            "title": "Key Capabilities",
            "subtitle": "Everything you need to build advanced AI and machine learning solutions",
            "features": {
                "computerVision": {
                    "title": "Computer Vision",
                    "description": "Object detection, facial recognition, and image classification at the edge"
                },
                "naturalLanguageProcessing": {
                    "title": "Natural Language Processing",
                    "description": "Voice recognition, text analysis, and conversational AI applications"
                },
                "predictiveAnalytics": {
                    "title": "Predictive Analytics",
                    "description": "Real-time data analysis and machine learning inference for decision making"
                },
                "edgeAIInference": {
                    "title": "Edge AI Inference",
                    "description": "Run AI models locally for low-latency, privacy-focused applications"
                }
            }
        },
        "featuredProjects": {
            "title": "Featured Projects",
            "subtitle": "Real-world AI and machine learning projects you can build today",
            "difficulty": {
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced"
            },
            "projects": {
                "aiPoweredSecuritySystem": {
                    "title": "AI-Powered Security System",
                    "description": "Intelligent surveillance with facial recognition, behavior analysis, and automated threat detection",
                    "duration": "3-4 weeks",
                    "components": "LinnuoSigma, AI Accelerator, IP Cameras, Edge TPU",
                    "features": "Facial Recognition, Behavior Analysis, Real-time Alerts, Privacy Protection"
                },
                "smartVoiceAssistant": {
                    "title": "Smart Voice Assistant",
                    "description": "AI assistant with natural language understanding and smart home control",
                    "duration": "2-3 weeks",
                    "components": "LinnuoDelta 3, Microphone Array, Speakers, AI Chip",
                    "features": "Voice Recognition, Natural Language Understanding, Smart Home Control, Offline Processing"
                },
                "predictiveMaintenanceSystem": {
                    "title": "Predictive Maintenance System",
                    "description": "Industrial AI solution that uses machine learning to predict equipment failures",
                    "duration": "4-5 weeks",
                    "components": "LinnuoSigma, Sensor Kit, Data Logger, ML Accelerator",
                    "features": "Anomaly Detection, Predictive Analytics, Real-time Monitoring, Maintenance Scheduling"
                }
            },
            "viewProjectGuide": "View Project Guide"
        },
        "tutorialsResources": {
            "title": "Tutorials & Resources",
            "subtitle": "Learn step-by-step with our comprehensive AI and machine learning guides",
            "tutorials": {
                "gettingStartedAI": {
                    "title": "Getting Started with AI Development",
                    "description": "Learn the fundamentals of AI development on Linnuo",
                    "type": "Documentation Guide",
                    "duration": "45 minutes"
                },
                "buildingComputerVisionApp": {
                    "title": "Building a Computer Vision App",
                    "description": "Create a real-time object detection and recognition system",
                    "type": "Video Tutorial",
                    "duration": "1 hour"
                },
                "deployingMLModels": {
                    "title": "Deploying Machine Learning Models",
                    "description": "Learn how to optimize and deploy ML models on edge devices",
                    "type": "Video Tutorial",
                    "duration": "1.5 hours"
                }
            },
            "watchTutorial": "Watch Tutorial",
            "downloadGuide": "Download Guide"
        },
        "callToAction": {
            "title": "Ready to Build Your AI Project?",
            "subtitle": "Get started with Linnuoand bring the power of artificial intelligence to your applications",
            "shopLinnuo": "Shop Linnuo",
            "joinCommunity": "Join Community"
        },
        "common": {
            "components": "Components",
            "features": "Features",
            "duration": "Duration",
            "difficulty": "Difficulty"
        }
    },
    "application": {
        "hero": {
            "title": "Application Cases",
            "subtitle": "Explore innovative applications of Linnuo single board computers across various fields, from IoT to artificial intelligence, from industrial automation to education and research.",
            "exploreProjects": "Explore Projects",
            "viewDocumentation": "View Documentation"
        },
        "categories": {
            "title": "Application Fields",
            "subtitle": "Linnuo single board computers play important roles in multiple industries and fields",
            "iotSmartHome": {
                "title": "IoT & Smart Home",
                "description": "Build smart home systems with device connectivity and automation control",
                "applications": [
                    "Smart lighting control",
                    "Temperature and humidity monitoring",
                    "Security monitoring systems",
                    "Smart door locks",
                    "Voice assistant integration"
                ]
            },
            "aiMachineLearning": {
                "title": "AI & Machine Learning",
                "description": "Leverage powerful computing capabilities for AI model training and inference",
                "applications": [
                    "Image recognition",
                    "Natural language processing",
                    "Edge computing",
                    "Machine vision",
                    "Deep learning inference"
                ]
            },
            "industrialAutomation": {
                "title": "Industrial Automation",
                "description": "Provide reliable computing and control solutions for Industry 4.0",
                "applications": [
                    "Production line control",
                    "Equipment monitoring",
                    "Quality inspection",
                    "Predictive maintenance",
                    "Data acquisition"
                ]
            },
            "educationResearch": {
                "title": "Education & Research",
                "description": "Provide flexible computing platforms for academic research and teaching",
                "applications": [
                    "Programming education",
                    "Research projects",
                    "Prototype development",
                    "Experimental platforms",
                    "Maker education"
                ]
            },
            "compactEmbedded": {
                "title": "Compact Embedded",
                "description": "Compact design suitable for space-constrained application scenarios",
                "applications": [
                    "Compact design",
                    "Low power consumption",
                    "High integration",
                    "High reliability"
                ]
            },
            "energyPower": {
                "title": "Energy & Power",
                "description": "Provide intelligent solutions for energy management and power systems",
                "applications": [
                    "Energy monitoring",
                    "Intelligent scheduling",
                    "Fault diagnosis",
                    "Data analysis"
                ]
            },
            "networkCommunication": {
                "title": "Network Communication",
                "description": "Build high-performance network devices and communication systems",
                "applications": [
                    "Network routers",
                    "Firewall devices",
                    "VPN gateways",
                    "Network storage",
                    "Communication base stations"
                ]
            }
        },
        "featuredProjects": {
            "title": "Featured Projects",
            "subtitle": "View excellent project cases built with Linnuo single board computers",
            "viewAllProjects": "View All Projects",
            "difficulty": {
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced"
            },
            "projects": {
                "smartMirror": {
                    "title": "Smart Mirror",
                    "description": "Intelligent display mirror integrating weather, calendar, news and other information",
                    "tags": [
                        "IoT",
                        "Display",
                        "Sensors",
                        "Voice Control"
                    ]
                },
                "droneController": {
                    "title": "Drone Controller",
                    "description": "High-performance drone flight control and image processing system",
                    "tags": [
                        "Flight Control",
                        "Image Processing",
                        "Real-time Systems",
                        "Sensor Fusion"
                    ]
                },
                "iotGateway": {
                    "title": "IoT Gateway",
                    "description": "Smart IoT data gateway connecting multiple devices",
                    "tags": [
                        "IoT",
                        "Data Processing",
                        "Networking",
                        "Protocol Conversion"
                    ]
                }
            }
        },
        "callToAction": {
            "title": "Start Your Project",
            "subtitle": "Choose the right Linnuo single board computer and start building your innovative project"
        },
        "benefits": {
            "industrialAutomation": [
                "Improve production efficiency and product quality",
                "Reduce labor costs and operational risks",
                "Enable intelligent equipment management",
                "Support Industry 4.0 digital transformation"
            ],
            "iotSmartHome": [
                "Device interconnection and interoperability",
                "Intelligent scenario control",
                "Remote monitoring and management",
                "Energy-saving and environmental solutions"
            ],
            "aiMachineLearning": [
                "Powerful AI computing capabilities",
                "Support for multiple deep learning frameworks",
                "Edge computing reduces latency",
                "Flexible model deployment solutions"
            ],
            "educationResearch": [
                "Rich educational resources",
                "Open development environment",
                "Project practice platform",
                "Innovation capability development"
            ],
            "compactEmbedded": [
                "Space saving",
                "Reduced power consumption",
                "Simplified deployment",
                "Improved reliability"
            ],
            "energyPower": [
                "Optimize energy usage",
                "Improve system efficiency",
                "Prevent failures",
                "Reduce operating costs"
            ],
            "networkCommunication": [
                "High-speed data transmission",
                "Stable network connections",
                "Secure communication",
                "Flexible network configuration"
            ]
        },
        "caseStudies": {
            "smartFactory": {
                "title": "Smart Factory Automation System",
                "industry": "Smart Manufacturing",
                "challenge": "Traditional manufacturing faces challenges of low production efficiency, difficult quality control, and high labor costs, urgently needing digital transformation.",
                "solution": "Build smart factory systems using Linnuo single board computers, integrating sensor networks, machine vision, and AI algorithms to achieve comprehensive automation and intelligent management of production processes.",
                "results": [
                    "Production efficiency increased by 40%",
                    "Product quality pass rate reached 99.5%",
                    "Labor costs reduced by 30%",
                    "Equipment failure rate reduced by 60%"
                ],
                "specs": "Linnuo-X1 Pro, 8GB RAM, 64GB eMMC, Industrial temperature range"
            },
            "edgeComputing": {
                "title": "Edge Computing Data Center",
                "industry": "Edge Computing",
                "challenge": "Traditional cloud computing architecture has high latency and high bandwidth costs when processing real-time data, requiring computing power deployment at the edge.",
                "solution": "Build distributed edge computing nodes using Linnuo single board computers to perform real-time processing and analysis at the source of data generation, reducing data transmission latency.",
                "results": [
                    "Data processing latency reduced by 80%",
                    "Network bandwidth usage reduced by 50%",
                    "System response time improved by 3x",
                    "Operating costs reduced by 35%"
                ],
                "specs": "Linnuo-Edge, 16GB RAM, 128GB SSD, Multi-port configuration"
            },
            "machineVision": {
                "title": "Machine Vision Quality Inspection System",
                "industry": "Machine Vision",
                "challenge": "Traditional manual quality inspection is inefficient, subjective, and cannot work 24 hours continuously, making it difficult to meet modern manufacturing quality requirements.",
                "solution": "Develop high-precision machine vision systems based on Linnuo single board computers, combined with deep learning algorithms to achieve automatic detection and classification of product defects.",
                "results": [
                    "Detection accuracy reached 99.8%",
                    "Detection speed increased by 10x",
                    "24-hour continuous operation",
                    "Quality inspection costs reduced by 70%"
                ],
                "specs": "Linnuo-Vision, GPU acceleration, 4K camera support, Real-time image processing"
            },
            "smartGrid": {
                "title": "Smart Grid Monitoring Platform",
                "industry": "Energy & Power",
                "challenge": "Traditional power grids lack real-time monitoring and intelligent scheduling capabilities, making it difficult to cope with challenges of new energy integration and load fluctuations.",
                "solution": "Deploy Linnuo single board computers to build smart grid monitoring systems, achieving real-time monitoring, fault warning, and intelligent scheduling of grid status.",
                "results": [
                    "Grid stability improved by 45%",
                    "Fault response time reduced by 60%",
                    "Energy utilization efficiency improved by 25%",
                    "Maintenance costs reduced by 40%"
                ],
                "specs": "Linnuo-Grid, Industrial design, Multi-protocol support, Redundant power supply"
            }
        },
        "advantages": {
            "reliability": {
                "title": "Reliability Guarantee",
                "description": "Industrial-grade design, rigorously tested to ensure stable operation in harsh environments"
            },
            "customization": {
                "title": "Customization Service",
                "description": "Flexible hardware configuration and software customization to meet different application scenarios"
            },
            "delivery": {
                "title": "Fast Delivery",
                "description": "Complete supply chain system ensures fast product delivery and technical support"
            },
            "support": {
                "title": "Professional Support",
                "description": "Professional technical team provides comprehensive support from solution design to after-sales service"
            }
        }
    },
    "common": {
        "loading": "Loading...",
        "error": {
            "title": "Something went wrong",
            "description": "An unexpected error occurred. Please try again.",
            "retry": "Try Again",
            "backHome": "Back to Home"
        },
        "success": "Success",
        "cancel": "Cancel",
        "confirm": "Confirm",
        "save": "Save",
        "edit": "Edit",
        "delete": "Delete",
        "close": "Close",
        "next": "Next",
        "previous": "Previous",
        "viewMore": "View More",
        "showLess": "Show Less",
        "category": "Category",
        "author": "Author",
        "date": "Date",
        "tags": "Tags",
        "featured": "Featured",
        "new": "New",
        "popular": "Popular",
        "recommended": "Recommended",
        "search": "Search",
        "language": "Language",
        "home": "Home",
        "features": "Features",
        "benefits": "Core Benefits",
        "notFound": {
            "title": "Page Not Found",
            "description": "The page you're looking for doesn't exist or has been moved.",
            "backHome": "Back to Home",
            "search": "Search Site",
            "goBack": "Go Back",
            "helpText": "If you think this is an error, please contact our support team."
        },
        "loadingStates": {
            "loading": "Loading...",
            "loadingData": "Loading data...",
            "processing": "Processing...",
            "saving": "Saving...",
            "searching": "Searching...",
            "submitting": "Submitting..."
        }
    },
    "contact": {
        "title": "Contact Us",
        "subtitle": "Get in touch with our team for support, partnerships, or general inquiries.",
        "loadingText": "Loading contact information...",
        "postalCode": "Postal Code",
        "form": {
            "title": "Send us a message",
            "firstName": "First Name",
            "lastName": "Last Name",
            "email": "Email",
            "subject": "Subject",
            "message": "Message",
            "firstNamePlaceholder": "John",
            "lastNamePlaceholder": "Doe",
            "emailPlaceholder": "<EMAIL>",
            "subjectPlaceholder": "How can we help you?",
            "messagePlaceholder": "Tell us more about your inquiry...",
            "sendMessage": "Send Message"
        },
        "contactInfo": {
            "business": {
                "title": "Business Sales"
            },
            "technical": {
                "title": "Technical Support"
            },
            "email": {
                "title": "Email",
                "value": "<EMAIL>",
                "description": "General inquiries and support"
            },
            "community": {
                "title": "Community",
                "value": "Join our Discord and Forum"
            },
            "address": {
                "title": "Address",
                "value": "LinnuoTechnology\nInnovation District\nTech City, TC 12345"
            },
            "website": {
                "title": "Website"
            },
            "wechat": {
                "title": "WeChat",
                "description": "Scan QR code to add us on WeChat"
            },
            "hours": {
                "title": "Business Hours",
                "business": "Business Sales",
                "support": "Technical Support"
            }
        }
    },
    "customized-service": {
        "title": "Customized Service",
        "subtitle": "Professional ODM/OEM services for embedded computing solutions",
        "badge": "Sales and Support",
        "features": {
            "title": "Why Choose Our Customization Services?",
            "quickProductization": {
                "title": "Quick Productization",
                "description": "Rapid development and deployment of custom solutions"
            },
            "hardwareSoftwareSupport": {
                "title": "Hardware and Software Support",
                "description": "Complete technical support from hardware to software"
            },
            "supplyGuarantee": {
                "title": "Three-tier Supply Guarantee",
                "description": "Reliable supply chain management and quality assurance"
            },
            "costOptimization": {
                "title": "Cost Structure Optimization",
                "description": "Cost-effective solutions tailored to your budget"
            }
        },
        "process": {
            "title": "Custom Design ODM/OEM Service Process",
            "subtitle": "Professional process ensures project success",
            "communication": {
                "title": "Communication",
                "description": "Clarify customer requirements"
            },
            "proposal": {
                "title": "Product Proposal",
                "description": "Provide optimal development solutions"
            },
            "evaluation": {
                "title": "Project Evaluation",
                "description": "Determine product implementation plan"
            },
            "trial": {
                "title": "Product Trial",
                "description": "Provide development samples for customer confirmation"
            },
            "confirmation": {
                "title": "Trial Production Confirmation",
                "description": "Small batch trial production"
            },
            "delivery": {
                "title": "Mass Production Delivery",
                "description": "Fast and perfect delivery service"
            }
        },
        "form": {
            "title": "Apply for Customization Service",
            "subtitle": "Tell us about your project requirements and we'll provide a tailored solution",
            "companyName": "Company Name",
            "contactPerson": "Contact Person",
            "email": "Email Address",
            "phone": "Phone Number",
            "projectType": "Project Type",
            "projectDescription": "Project Description",
            "requirements": "Project Requirements",
            "timeline": "Project Timeline",
            "budget": "Budget Range",
            "additionalInfo": "Additional Information",
            "submit": "Submit Customization Request",
            "submitting": "Submitting...",
            "required": "Required field",
            "successMessage": "Thank you! Your customization request has been submitted successfully. We will contact you within 24 hours.",
            "errorMessage": "Sorry, there was an error submitting your request. Please try again.",
            "expectedQuantity": "Expected Quantity",
            "privacyConsent": "I agree to the privacy policy and consent to the processing of my personal data",
            "placeholders": {
                "companyName": "Enter your company name",
                "contactPerson": "Enter contact person name",
                "email": "Enter email address",
                "phone": "Enter phone number",
                "projectDescription": "Describe your project goals and requirements...",
                "requirements": "Please describe your project requirements, technical specifications, and special features needed...",
                "additionalInfo": "Any additional information or special requirements...",
                "selectProjectType": "Select project type",
                "selectQuantity": "Select quantity range",
                "selectTimeline": "Select timeline"
            },
            "projectTypes": {
                "iot": "IoT Solutions",
                "industrial": "Industrial Automation",
                "ai": "AI/ML Applications",
                "embedded": "Embedded Systems",
                "other": "Other",
                "odm": "ODM (Original Design Manufacturer)",
                "oem": "OEM (Original Equipment Manufacturer)",
                "customBoard": "Custom Board Design",
                "softwareDev": "Software Development",
                "integration": "System Integration"
            },
            "quantities": {
                "1-100": "1-100 units",
                "100-500": "100-500 units",
                "500-1000": "500-1,000 units",
                "1000-5000": "1,000-5,000 units",
                "5000+": "5,000+ units"
            },
            "timelines": {
                "1-3months": "1-3 months",
                "3-6months": "3-6 months",
                "6-12months": "6-12 months",
                "12months": "12+ months",
                "12months+": "12+ months"
            },
            "budgets": {
                "under10k": "Under $10,000",
                "10k-50k": "$10,000 - $50,000",
                "50k-100k": "$50,000 - $100,000",
                "over100k": "Over $100,000"
            }
        }
    },
    "downloads": {
        "title": "Downloads",
        "resources": "Resources",
        "subtitle": "Access drivers, documentation, firmware, and development tools for all Linnuoproducts",
        "searchPlaceholder": "Search files...",
        "totalFiles": "Total Files",
        "categories": {
            "all": "All Categories",
            "drivers": "Drivers",
            "documentation": "Documentation",
            "firmware": "Firmware",
            "software": "Software",
            "specifications": "Specifications",
            "images": "System Images",
            "category": "Category",
            "developmentTools": "Development Tools",
            "libraries": "Libraries"
        },
        "products": {
            "all": "All Products",
            "product": "Product"
        },
        "fileInfo": {
            "version": "Version",
            "size": "Size",
            "date": "Date",
            "downloads": "Downloads",
            "totalFiles": "Total Files"
        },
        "downloadButton": "Download",
        "noResults": "No files found matching your search criteria.",
        "clearFilters": "Clear Filters",
        "allDownloads": "All Downloads",
        "completeCollection": "Complete collection of drivers, documentation, and tools",
        "noDownloadsFound": "No downloads found",
        "tryAdjusting": "Try adjusting your search or filter criteria.",
        "loadMore": "Load More Downloads",
        "needHelp": "Need Help?",
        "cantFind": "Can't find what you're looking for? Our support team is here to help you get the right files for your project.",
        "viewDocumentation": "View Documentation",
        "contactSupport": "Contact Support",
        "featured": "Featured",
        "compatibleWith": "Compatible with",
        "loading": "Loading downloads...",
        "featuredDownloads": "Featured Downloads",
        "mostPopular": "Most popular and recommended downloads"
    },
    "education-research": {
        "hero": {
            "title": "Education & Research",
            "subtitle": "Build powerful educational tools and research platforms for learning and discovery. From interactive learning systems to advanced research equipment, create solutions that inspire innovation and knowledge.",
            "startBuilding": "Start Building",
            "viewDocumentation": "View Documentation"
        },
        "keyCapabilities": {
            "title": "Key Capabilities",
            "subtitle": "Everything you need to build innovative education and research solutions",
            "features": {
                "interactiveLearning": {
                    "title": "Interactive Learning",
                    "description": "Create engaging interactive educational experiences and learning platforms"
                },
                "dataCollection": {
                    "title": "Data Collection",
                    "description": "High-precision data acquisition systems for scientific research and experiments"
                },
                "labAutomation": {
                    "title": "Lab Automation",
                    "description": "Automate laboratory processes and equipment control for improved research efficiency"
                },
                "collaborativePlatforms": {
                    "title": "Collaborative Platforms",
                    "description": "Connected platforms that enable remote learning and research collaboration"
                }
            }
        },
        "featuredProjects": {
            "title": "Featured Projects",
            "subtitle": "Real-world education and research projects you can build today",
            "difficulty": {
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced"
            },
            "projects": {
                "smartClassroomSystem": {
                    "title": "Smart Classroom System",
                    "description": "Complete classroom solution with interactive displays, automated attendance, and intelligent environment control",
                    "duration": "4-5 weeks",
                    "components": "LinnuoSigma, Interactive Displays, Sensor Kit, Camera System",
                    "features": "Interactive Learning, Attendance Tracking, Environment Control, Remote Access"
                },
                "researchDataLogger": {
                    "title": "Research Data Logger",
                    "description": "High-precision data acquisition system for scientific experiments and long-term research projects",
                    "duration": "2-3 weeks",
                    "components": "LinnuoDelta 3, Precision Sensors, Data Storage, Network Module",
                    "features": "High-Precision Measurement, Long-term Logging, Remote Monitoring, Data Analysis"
                },
                "virtualLabPlatform": {
                    "title": "Virtual Lab Platform",
                    "description": "Virtual laboratory environment that enables remote experimentation and collaborative research",
                    "duration": "5-6 weeks",
                    "components": "LinnuoSigma, Lab Equipment Interface, Video System, Cloud Platform",
                    "features": "Remote Experimentation, Real-time Collaboration, Equipment Control, Result Sharing"
                }
            },
            "viewProjectGuide": "View Project Guide"
        },
        "tutorialsResources": {
            "title": "Tutorials & Resources",
            "subtitle": "Learn step-by-step with our comprehensive education and research guides",
            "tutorials": {
                "buildingEducationalTools": {
                    "title": "Building Educational Tools",
                    "description": "Learn how to create interactive educational applications and learning platforms",
                    "type": "Documentation Guide",
                    "duration": "1 hour"
                },
                "scientificDataAcquisition": {
                    "title": "Scientific Data Acquisition",
                    "description": "Set up precise data acquisition systems for research and experimentation",
                    "type": "Video Tutorial",
                    "duration": "1.5 hours"
                },
                "labAutomationSetup": {
                    "title": "Lab Automation Setup",
                    "description": "Implement automation solutions to improve laboratory efficiency and accuracy",
                    "type": "Video Tutorial",
                    "duration": "2 hours"
                }
            },
            "watchTutorial": "Watch Tutorial",
            "downloadGuide": "Download Guide"
        },
        "callToAction": {
            "title": "Ready to Build Your Educational Solution?",
            "subtitle": "Get started with Linnuoand create innovative tools and platforms for learning and research",
            "shopLinnuo": "Shop Linnuo",
            "joinCommunity": "Join Community"
        },
        "common": {
            "components": "Components",
            "features": "Features",
            "duration": "Duration",
            "difficulty": "Difficulty"
        }
    },
    "footer": {
        "description": "Linnuois a leading provider of high-performance single board computers for developers, makers, and innovators worldwide.",
        "quickLinks": "Quick Links",
        "products": "Products",
        "support": "Support",
        "company": "Company",
        "followUs": "Follow Us",
        "newsletter": "Newsletter",
        "newsletterDescription": "Subscribe to our newsletter for the latest updates and announcements.",
        "emailPlaceholder": "Enter your email",
        "subscribe": "Subscribe",
        "allRightsReserved": "All rights reserved.",
        "privacyPolicy": "Privacy Policy",
        "termsOfService": "Terms of Service",
        "contactUs": "Contact Us",
        "businessHours": "Business Hours",
        "technicalSupport": "Technical Support"
    },
    "hero": {
        "title": "Powerful Single Board Computers",
        "subtitle": "Unleash Your Creativity",
        "description": "Linnuooffers high-performance single board computers perfect for developers, makers, and innovators. Build amazing projects with our cutting-edge technology.",
        "getStarted": "Get Started",
        "learnMore": "Learn More",
        "watchVideo": "Watch Video",
        "exploreProducts": "Explore Products",
        "viewDocumentation": "View Documentation",
        "carousel": {
            "loading": "Loading images...",
            "error": "Error loading slides",
            "retry": "Retry",
            "previous": "Previous",
            "next": "Next", 
            "goToSlide": "Go to slide",
            "pause": "Pause",
            "auto": "Auto",
            "resume": "Resume",
            "paused": "Paused",
            "previousImage": "Previous image",
            "nextImage": "Next image",
            "goToImage": "Go to image {{number}}",
            "slides": {
                "LinnuoSigma": "LinnuoSigma - High Performance Computing",
                "developerEcosystem": "Developer Ecosystem - Community and Support",
                "aiMachineLearning": "AI & Machine Learning Solutions",
                "default": "Linnuo Product Showcase"
            }
        }
    },
    "home": {
        "featuredProducts": {
            "title": "Featured Products",
            "subtitle": "Discover our most popular single board computers, designed for makers, developers, and innovators worldwide.",
            "mainTitle": "Featured Products",
            "categories": {
                "universalEmbeddedSeries": "Universal Embedded Series",
                "allInOneIpc": "All-in-One IPC"
            },
            "loading": "Products are currently being loaded. Please check back later.",
            "viewAll": "View All Products"
        },
        "featureHighlights": {
            "title": "Why Choose Linnuo?",
            "subtitle": "Our single board computers combine the power of a full PC with the flexibility of embedded systems, making them perfect for any project.",
            "features": {
                "intelProcessors": {
                    "title": "Intel x86 Processors",
                    "description": "Latest Intel processors with up to 12 cores and 4.6GHz boost frequency for maximum performance."
                },
                "highPerformance": {
                    "title": "High Performance",
                    "description": "Run demanding applications, games, and development tools with desktop-class performance."
                },
                "reliableStable": {
                    "title": "Reliable & Stable",
                    "description": "Industrial-grade components and rigorous testing ensure reliable operation in any environment."
                },
                "developerFriendly": {
                    "title": "Developer Friendly",
                    "description": "Full Windows and Linux support with Arduino compatibility for seamless development."
                },
                "advancedConnectivity": {
                    "title": "Advanced Connectivity",
                    "description": "Wi-Fi 6E, Bluetooth 5.3, USB4, and Gigabit Ethernet for all your connectivity needs."
                },
                "expandableStorage": {
                    "title": "Expandable Storage",
                    "description": "Built-in eMMC storage with M.2 slots for NVMe SSDs and microSD card support."
                },
                "displaySupport": {
                    "title": "4K Display Support",
                    "description": "Dual 4K display output with HDMI 2.1 and DisplayPort for stunning visual experiences."
                },
                "gamingReady": {
                    "title": "Gaming Ready",
                    "description": "Intel Iris Xe graphics with hardware acceleration for gaming and multimedia applications."
                }
            }
        },
        "communitySection": {
            "title": "About Us",
            "subtitle": "We are passionate about empowering creators, developers, and innovators with powerful, accessible single-board computing solutions.",
            "description": {
                "paragraph1": "Linnuo Technology is a leading provider of high-performance single board computers, dedicated to empowering creators, developers, and innovators worldwide. Since our founding, we have been committed to democratizing computing technology and making powerful, accessible solutions available to everyone.",
                "paragraph2": "Our product portfolio includes the Universal Embedded Series and All-in-One IPC solutions, featuring cutting-edge Intel processors, advanced connectivity options, and robust industrial-grade components. From makers building their first projects to enterprises deploying large-scale solutions, our boards deliver the performance and reliability needed for any application.",
                "paragraph3": "With a global community spanning over 100 countries and millions of users worldwide, Linnuo continues to push the boundaries of what's possible in single-board computing. We believe in the power of innovation, quality, and community to drive technological advancement and create a better future for all."
            },
            "stats": {
                "founded": "Founded",
                "globalUsers": "Global Users",
                "countries": "Countries",
                "awards": "Awards"
            },
            "values": {
                "title": "Our Values",
                "subtitle": "The principles that guide everything we do and drive our mission forward.",
                "innovation": {
                    "title": "Innovation",
                    "description": "Pushing the boundaries of single-board computing with cutting-edge technology."
                },
                "quality": {
                    "title": "Quality",
                    "description": "Delivering reliable, high-performance products that exceed expectations."
                },
                "community": {
                    "title": "Community",
                    "description": "Building a global community of makers, developers, and innovators."
                },
                "growth": {
                    "title": "Growth",
                    "description": "Empowering creators to bring their ideas to life and scale their projects."
                }
            },
            "journey": {
                "title": "Our Journey",
                "subtitle": "Key milestones in our mission to democratize computing technology.",
                "milestones": {
                    "founded": {
                        "title": "Company Founded",
                        "description": "Linnuowas established with a vision to democratize computing."
                    },
                    "firstProduct": {
                        "title": "First Product Launch",
                        "description": "Released our first single-board computer, revolutionizing the maker space."
                    },
                    "globalExpansion": {
                        "title": "Global Expansion",
                        "description": "Expanded to serve customers in over 100 countries worldwide."
                    },
                    "nextGeneration": {
                        "title": "Next Generation",
                        "description": "Launched LinnuoSigma, our most powerful board yet."
                    }
                }
            }
        },
        "newsSection": {
            "title": "Latest News",
            "subtitle": "Stay updated with our latest developments",
            "description": "Get the latest updates on product releases, community highlights, and industry insights.",
            "readMore": "Read More",
            "viewAllNews": "View All News",
            "noNews": "No news available at the moment.",
            "loadingNews": "Loading latest news...",
            "minRead": "min read"
        }
    },
    "industrial-automation": {
        "hero": {
            "title": "Industrial Automation",
            "subtitle": "Build intelligent industrial systems and automation solutions. From process control to predictive maintenance, create powerful industrial applications that improve efficiency, quality, and safety.",
            "startBuilding": "Start Building",
            "viewDocumentation": "View Documentation"
        },
        "keyCapabilities": {
            "title": "Key Capabilities",
            "subtitle": "Everything you need to build advanced industrial automation solutions",
            "features": {
                "processControl": {
                    "title": "Process Control",
                    "description": "Real-time control systems for manufacturing processes and industrial equipment"
                },
                "qualityInspection": {
                    "title": "Quality Inspection",
                    "description": "Automated quality control with computer vision and machine learning"
                },
                "predictiveMaintenance": {
                    "title": "Predictive Maintenance",
                    "description": "Monitor equipment health and predict maintenance needs before failures occur"
                },
                "dataAcquisition": {
                    "title": "Data Acquisition",
                    "description": "Collect, process, and analyze industrial sensor data in real-time"
                }
            }
        },
        "featuredProjects": {
            "title": "Featured Projects",
            "subtitle": "Real-world industrial automation projects you can build today",
            "difficulty": {
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced"
            },
            "projects": {
                "smartFactoryControlSystem": {
                    "title": "Smart Factory Control System",
                    "description": "Complete factory automation with real-time monitoring, control, and optimization",
                    "duration": "6-8 weeks",
                    "components": "LinnuoSigma, Industrial I/O, HMI Display, Network Infrastructure",
                    "features": "Real-time Control, SCADA Integration, Remote Monitoring, Data Analytics"
                },
                "qualityInspectionStation": {
                    "title": "Quality Inspection Station",
                    "description": "Automated product quality inspection using AI-powered computer vision",
                    "duration": "3-4 weeks",
                    "components": "LinnuoDelta 3, Industrial Cameras, Lighting System, Conveyor Interface",
                    "features": "Defect Detection, Quality Scoring, Real-time Processing, Data Analytics"
                },
                "predictiveMaintenanceSystem": {
                    "title": "Predictive Maintenance System",
                    "description": "Industrial IoT solution that uses machine learning to predict equipment failures",
                    "duration": "4-5 weeks",
                    "components": "LinnuoSigma, Sensor Kit, Data Logger, Edge Computing Unit",
                    "features": "Anomaly Detection, Predictive Analytics, Real-time Monitoring, Maintenance Scheduling"
                }
            },
            "viewProjectGuide": "View Project Guide"
        },
        "tutorialsResources": {
            "title": "Tutorials & Resources",
            "subtitle": "Learn step-by-step with our comprehensive industrial automation guides",
            "tutorials": {
                "industrialIoTBasics": {
                    "title": "Industrial IoT Basics",
                    "description": "Learn the fundamentals of industrial IoT systems and sensor integration",
                    "type": "Documentation Guide",
                    "duration": "1 hour"
                },
                "buildingControlSystem": {
                    "title": "Building a Control System",
                    "description": "Create a real-time monitoring and control system for industrial processes",
                    "type": "Video Tutorial",
                    "duration": "1.5 hours"
                },
                "implementingPredictiveMaintenance": {
                    "title": "Implementing Predictive Maintenance",
                    "description": "Deploy machine learning-powered predictive maintenance solutions",
                    "type": "Video Tutorial",
                    "duration": "2 hours"
                }
            },
            "watchTutorial": "Watch Tutorial",
            "downloadGuide": "Download Guide"
        },
        "callToAction": {
            "title": "Ready to Build Your Industrial Solution?",
            "subtitle": "Get started with Linnuoand bring intelligent automation and control to your industrial applications",
            "shopLinnuo": "Shop Linnuo",
            "joinCommunity": "Join Community"
        },
        "common": {
            "components": "Components",
            "features": "Features",
            "duration": "Duration",
            "difficulty": "Difficulty"
        }
    },
    "iot-smart-home": {
        "hero": {
            "title": "IoT & Smart Home",
            "subtitle": "Transform your home into an intelligent, connected ecosystem. Build automation systems, monitoring solutions, and smart devices that make life easier and more efficient.",
            "startBuilding": "Start Building",
            "viewDocumentation": "View Documentation"
        },
        "keyCapabilities": {
            "title": "Key Capabilities",
            "subtitle": "Everything you need to build comprehensive IoT and smart home solutions",
            "features": {
                "environmentalMonitoring": {
                    "title": "Environmental Monitoring",
                    "description": "Monitor temperature, humidity, air quality, and other environmental parameters in real-time"
                },
                "securitySystems": {
                    "title": "Security Systems",
                    "description": "Create comprehensive security solutions with cameras, sensors, and automated alerts"
                },
                "smartLighting": {
                    "title": "Smart Lighting",
                    "description": "Automated lighting control with scheduling, motion detection, and energy optimization"
                },
                "connectivityHub": {
                    "title": "Connectivity Hub",
                    "description": "Central hub for connecting and managing all your IoT devices and sensors"
                }
            }
        },
        "featuredProjects": {
            "title": "Featured Projects",
            "subtitle": "Real-world IoT and smart home projects you can build today",
            "difficulty": {
                "beginner": "Beginner",
                "intermediate": "Intermediate",
                "advanced": "Advanced"
            },
            "projects": {
                "smartHomeCentralHub": {
                    "title": "Smart Home Central Hub",
                    "description": "Complete home automation system with voice control, mobile app, and AI-powered scheduling",
                    "duration": "2-3 weeks",
                    "components": "LinnuoSigma, Voice Recognition Module, Various Sensors, Mobile App",
                    "features": "Voice Control, Mobile App, AI Scheduling, Energy Monitoring"
                },
                "environmentalMonitoringStation": {
                    "title": "Environmental Monitoring Station",
                    "description": "Comprehensive system for monitoring air quality, temperature, humidity, and noise levels",
                    "duration": "1-2 weeks",
                    "components": "LinnuoDelta 3, Environmental Sensors, Display Screen, Data Logger",
                    "features": "Real-time Monitoring, Data Logging, Alert System, Remote Access"
                },
                "smartSecuritySystem": {
                    "title": "Smart Security System",
                    "description": "AI-powered security solution with facial recognition, motion detection, and automated alerts",
                    "duration": "2-3 weeks",
                    "components": "LinnuoSigma, IP Cameras, Motion Sensors, Alert System",
                    "features": "Facial Recognition, Motion Detection, Automated Alerts, Remote Monitoring"
                }
            },
            "viewProjectGuide": "View Project Guide"
        },
        "tutorialsResources": {
            "title": "Tutorials & Resources",
            "subtitle": "Learn step-by-step with our comprehensive guides and video tutorials",
            "tutorials": {
                "gettingStartedIoT": {
                    "title": "Getting Started with IoT",
                    "description": "Learn the fundamentals of IoT development and best practices",
                    "type": "Documentation Guide",
                    "duration": "30 minutes"
                },
                "buildingSmartThermostat": {
                    "title": "Building a Smart Thermostat",
                    "description": "Create a smart thermostat that can be controlled remotely",
                    "type": "Video Tutorial",
                    "duration": "45 minutes"
                },
                "advancedHomeSecuritySetup": {
                    "title": "Advanced Home Security Setup",
                    "description": "Implement a complete security system with cameras and sensors",
                    "type": "Video Tutorial",
                    "duration": "1.5 hours"
                }
            },
            "watchTutorial": "Watch Tutorial",
            "downloadGuide": "Download Guide"
        },
        "callToAction": {
            "title": "Ready to Build Your Smart Home?",
            "subtitle": "Get started with Linnuoand transform your home into an intelligent, connected space",
            "shopLinnuo": "Shop Linnuo",
            "joinCommunity": "Join Community"
        },
        "common": {
            "components": "Components",
            "features": "Features",
            "duration": "Duration",
            "difficulty": "Difficulty"
        }
    },
    "nav": {
        "home": "Home",
        "products": "Products",
        "salesAndSupport": "Sales and Support",
        "application": "Application",
        "about": "About",
        "sampleApplication": "Sample Application",
        "afterSalesService": "After-sales Service",
        "downloads": "Downloads",
        "customizedService": "Customized Service",
        "iotSmartHome": "IoT & Smart Home",
        "aiMachineLearning": "AI & Machine Learning",
        "gamingEntertainment": "Gaming & Entertainment",
        "industrialAutomation": "Industrial Automation",
        "educationResearch": "Education & Research",
        "automotiveTransportation": "Automotive & Transportation",
        "aboutUs": "About Us",
        "news": "News",
        "contact": "Contact",
        "search": "Search",
        "language": "Language"
    },
    "news": {
        "title": "News & Updates",
        "subtitle": "Stay Informed",
        "description": "Stay informed with the latest developments, product announcements, community highlights, and industry insights from Linnuo.",
        "readMore": "Read More",
        "loadMore": "Load More Articles",
        "categories": {
            "all": "All",
            "product": "Product",
            "launch": "Launch",
            "awards": "Awards",
            "tutorial": "Tutorial",
            "companyNews": "Company News",
            "technology": "Technology"
        },
        "searchPlaceholder": "Search news and updates...",
        "noNewsFound": "No news found",
        "noNewsDescription": "Try adjusting your search or filter criteria.",
        "loadingNews": "Loading news articles...",
        "minRead": "min read",
        "backToNews": "Back to News List",
        "shareArticle": "Share this article",
        "share": "Share",
        "relatedArticles": "Related Articles",
        "relatedArticlesInDevelopment": "Related articles feature is under development...",
        "publishedOn": "Published on",
        "by": "by",
        "newsletter": {
            "title": "Stay Updated",
            "description": "Subscribe to our newsletter and never miss important updates, product launches, or community highlights.",
            "emailPlaceholder": "Enter your email address",
            "subscribe": "Subscribe",
            "privacyNote": "We respect your privacy. Unsubscribe at any time."
        },
        "loadMoreArticles": "Load More Articles",
        "newsImage": "News Image"
    },
    "products": {
        "title": "Our Products",
        "subtitle": "Discover Innovation",
        "description": "Explore our comprehensive range of single board computers designed for various applications",
        "viewAll": "View All Products",
        "learnMore": "Learn More",
        "specifications": "Specifications",
        "features": "Features",
        "documentation": "Documentation",
        "buyNow": "Buy Now",
        "addToCart": "Add to Cart",
        "categories": {
            "scalableEmbeddedSeries": "Scalable Embedded Series",
            "miniSizeSeries": "Mini Size Series",
            "universalEmbeddedSeries": "Universal Embedded Series",
            "allInOneIpc": "All-in-one IPC"
        },
        "filters": {
            "all": "All Products",
            "category": "Category",
            "priceRange": "Price Range",
            "sortBy": "Sort By",
            "newest": "Newest",
            "popular": "Most Popular",
            "priceAsc": "Price: Low to High",
            "priceDesc": "Price: High to Low"
        },
        "page": {
            "title": "Our Products",
            "subtitle": "Discover our complete range of single board computers and embedded solutions. From high-performance computing to ultra-compact designs, find the perfect solution for your project.",
            "allProducts": "All Products",
            "browseComplete": "Browse our complete product lineup",
            "browseCategory": "Browse products in this category",
            "loading": "Loading products...",
            "noProducts": "No products found in this category.",
            "noImage": "No image"
        },
        "card": {
            "new": "New",
            "featured": "Featured",
            "details": "Details",
            "compare": "Compare",
            "selected": "Selected",
            "selectToCompare": "Select to Compare",
            "display": "Display",
            "moreFeatures": "+{{count}} more features..."
        },
        "productCategories": {
            "scalableEmbeddedSeries": {
                "name": "Scalable Embedded Series",
                "description": "High-performance embedded computing solutions"
            },
            "computeModuleSeries": {
                "name": "Compute Module Series",
                "description": "Compact and powerful compute modules"
            },
            "singleBoardComputer": {
                "name": "Single Board Computer",
                "description": "Complete computing solutions in a single board"
            },
            "developmentKit": {
                "name": "Development Kit",
                "description": "Complete development and prototyping kits"
            }
        },
        "detail": {
            "breadcrumb": {
                "home": "Home",
                "products": "Products"
            },
            "backToProducts": "Back to Products",
            "noImageAvailable": "No image available",
            "keyFeatures": "Key Features",
            "share": "Share",
            "coreSpecs": "Core Specifications",
            "requestSample": "Request Sample",
            "technicalConsult": "Technical Consultation",
            "productImage": "Product Image",
            "highResImage": "High resolution product image",
            "productDetailsPlaceholder": "Product Details",
            "productDetailsNote": "Detailed product information and images are displayed above",
            "needMoreInfo": "Need More Information?",
            "expertSupport": "Our experts are here to help you choose the right solution",
            "viewMoreProducts": "View More Products",
            "download": "Download",
            "warrantySupport": "Warranty & Support",
            "standardSupport": "Standard Support",
            "tabs": {
                "description": "Description",
                "specifications": "Specifications",
                "compatibility": "Compatibility",
                "downloads": "Downloads & Documentation",
                "productDetails": "Product Details",
                "applications": "Applications"
            },
            "specs": {
                "cpu": "CPU",
                "memory": "Memory",
                "storage": "Storage",
                "network": "Network",
                "power": "Power",
                "os": "Operating System",
                "temperature": "Operating Temperature"
            },
            "specsShort": {
                "cpu": "CPU",
                "memory": "RAM",
                "storage": "Storage",
                "network": "Net",
                "power": "PWR",
                "os": "OS",
                "temperature": "Temp",
                "display": "Disp"
            },
            "applications": {
                "smartManufacturing": "Smart Manufacturing",
                "smartManufacturingDesc": "Intelligent production line control and monitoring systems",
                "machineVision": "Machine Vision",
                "machineVisionDesc": "High-performance image processing and analysis solutions",
                "edgeComputing": "Edge Computing",
                "edgeComputingDesc": "Real-time data processing at the network edge"
            },
            "noContent": "No content available",
            "downloadsDescription": "Download drivers, manuals, firmware, and other resources for your {{productName}}.",
            "noDownloads": "No downloads available for this product.",
            "needHelp": "Need Help?",
            "documentation": "Documentation",
            "communityForum": "Community Forum",
            "technicalSupport": "Technical Support"
        },
        "compare": {
            "clearSelection": "Clear Selection",
            "viewComparison": "View Comparison",
            "addProduct": "Add Product",
            "searchPlaceholder": "Search products...",
            "addToCompare": "Add to Compare",
            "noProductsFound": "No products found",
            "maxProductsReached": "Maximum of 4 products can be compared at once",
            "removeToAdd": "Please remove a product to add a new one",
            "noProducts": "No products selected",
            "selectProducts": "Select products from our catalog to start comparing their specifications and features.",
            "title": "Product Comparison",
            "description": "Compare {{count}} selected products",
            "backToProducts": "Back to Products",
            "viewDetails": "View Details",
            "specs": {
                "category": "Category",
                "cpu": "CPU",
                "memory": "Memory",
                "storage": "Storage",
                "network": "Network",
                "display": "Display",
                "power": "Power",
                "os": "Operating System",
                "temperature": "Operating Temperature",
                "dimensions": "Dimensions",
                "weight": "Weight"
            }
        }
    },
    "sample-application": {
        "title": "Sample Application",
        "subtitle": "Request free samples of Linnuosingle-board computers for your project evaluation. Fast approval and shipping process.",
        "description": "Request free samples of Linnuo single-board computers for your project evaluation. Fast approval and shipping process.",
        "thankYou": "Thank You!",
        "successMessage": "Your sample application has been submitted successfully. We will contact you within 1-2 business days.",
        "submitAnother": "Submit Another Request",
        "successTitle": "Application Submitted Successfully!",
        "successDescription": "Your sample application has been submitted successfully. We will contact you within 1-2 business days to confirm sample shipping details.",
        "errorTitle": "Submission Failed",
        "errorDescription": "Sorry, there was an error during the submission process.",
        "retrySubmit": "Try Again",
        "refreshPage": "Refresh Page",
        "browseProducts": "Browse More Products",
        "networkError": "Network error, please check your connection and try again",
        "submitError": "Submission failed, please try again later",
        "form": {
            "name": "Name",
            "company": "Company Name",
            "phoneNumber": "Phone Number",
            "email": "Email",
            "sampleName": "Sample Name",
            "quantity": "Quantity",
            "purpose": "Purpose",
            "selectProduct": "Select a Product",
            "timeOfRequest": "Required Date",
            "requirements": "Requirements",
            "address": "Shipping Address",
            "urgency": "Urgency Level",
            "requestPlaceholder": "Please describe your sample requirements in detail...",
            "submit": "Submit Request",
            "submitting": "Submitting...",
            "required": "Required field",
            "invalidEmail": "Please enter a valid email address",
            "invalidPhone": "Please enter a valid phone number",
            "selectProductRequired": "Please select a product",
            "descriptionRequired": "Please describe your project requirements",
            "successTitle": "Application Submitted Successfully",
            "errorTitle": "Submission Failed",
            "errorMessage": "Sorry, there was an error submitting your application. Please try again.",
            "placeholders": {
                "name": "Enter your name",
                "company": "Enter your company name",
                "phoneNumber": "Enter your phone number",
                "email": "Enter your email address",
                "sampleName": "Enter sample name",
                "quantity": "Enter quantity needed",
                "purpose": "Please describe the intended use of the sample...",
                "requirements": "Please describe your sample requirements in detail...",
                "address": "Please provide detailed shipping address..."
            },
            "urgencyOptions": {
                "normal": "Normal",
                "urgent": "Urgent",
                "very_urgent": "Very Urgent"
            }
        },
        "products": {
            "LinnuoSigma": "LinnuoSigma",
            "LinnuoDelta3": "LinnuoDelta 3",
            "LinnuoMu": "LinnuoMu",
            "LinnuoAlpha": "LinnuoAlpha",
            "LinnuoV1": "LinnuoV1"
        },
        "benefits": {
            "title": "Why Request a Sample?",
            "subtitle": "Benefits of getting a free sample",
            "freeEvaluation": {
                "title": "Free Evaluation",
                "description": "Test if the product meets your project requirements before investing"
            },
            "fastShipping": {
                "title": "Fast Shipping",
                "description": "Once approved, we ship samples within 1-2 business days"
            },
            "technicalSupport": {
                "title": "Technical Support",
                "description": "Get professional support and guidance from our technical team"
            },
            "noCommitment": {
                "title": "No Commitment",
                "description": "Evaluate the sample with no purchase commitment required"
            }
        },
        "requirements": {
            "title": "Application Requirements",
            "subtitle": "To ensure samples are used for appropriate projects",
            "items": [
                "Provide detailed project description",
                "Explain expected use case scenarios",
                "Provide valid contact information",
                "Commit to use for legitimate business or educational purposes"
            ]
        }
    },
    "search": {
        "title": "Search",
        "placeholder": "Search products, news articles...",
        "searchButton": "Search",
        "loading": "Searching...",
        "noResults": "No results found",
        "noResultsDescription": "We couldn't find anything matching.Try different keywords or check your spelling.",
        "resultsCount": "{{count}} results found",
        "filters": {
            "all": "All Results",
            "products": "Products",
            "news": "News Articles"
        },
        "searching": "Searching...",
        "resultsFound": "Found {{count}} result{{count === 1 ? '' : 's'}} for \"{{query}}\"",
        "category": "Category",
        "clearSearch": "Clear search",
        "startSearch": "Start your search",
        "startSearchDescription": "Enter a search term to find products and news articles.",
        "viewMode": {
            "grid": "Grid View",
            "list": "List View"
        },
        "sortBy": {
            "relevance": "Relevance",
            "date": "Date",
            "title": "Title"
        },
        "resultTypes": {
            "product": "Product",
            "news": "News Article"
        },
        "actions": {
            "readMore": "Read More",
            "viewProduct": "View Product",
            "viewArticle": "View Article"
        },
        "suggestions": {
            "title": "Search Suggestions",
            "popularSearches": "Popular Searches",
            "recentSearches": "Recent Searches"
        },
        "navigation": {
            "navigate": "Navigate",
            "select": "Select",
            "close": "Close"
        },
        "viewAllResults": "View All Results"
    }
},
  zh: {
    "about": {
        "title": "关于 Linnuo",
        "intro": "Linnuo是单板计算机技术的领先创新者，赋能创客、开发者和企业将想法变为现实。",
        "mission": {
            "title": "我们的使命",
            "content": "通过提供强大、易用且多功能的单板计算机来民主化计算，促进各行各业和社区的创新。我们致力于打破技术壁垒，让每个人都能轻松接触到先进的计算技术。"
        },
        "story": {
            "title": "我们的故事",
            "content": "Linnuo成立时怀着弥合传统计算与嵌入式系统之间差距的愿景，自成立以来一直处于单板计算机开发的前沿。从最初的创新想法到今天的行业领导地位，我们始终坚持技术创新和用户体验的完美结合。"
        },
        "products": {
            "title": "我们的产品",
            "content": "从强大的LinnuoSigma到紧凑的LinnuoMu，我们的产品线为每个项目规模和复杂性提供解决方案。每一款产品都经过精心设计，确保在性能、稳定性和易用性方面达到行业领先水平。"
        },
        "vision": {
            "title": "我们的愿景",
            "content": "成为全球单板计算机领域的技术引领者，推动嵌入式计算技术的普及和发展，为数字化转型提供强有力的技术支撑。"
        },
        "values": {
            "title": "核心价值观",
            "innovation": {
                "title": "创新驱动",
                "content": "持续的技术创新是我们发展的核心动力"
            },
            "quality": {
                "title": "品质至上",
                "content": "严格的质量控制确保每一款产品的卓越性能"
            },
            "customer": {
                "title": "客户为本",
                "content": "以客户需求为导向，提供最优质的产品和服务"
            },
            "collaboration": {
                "title": "开放合作",
                "content": "与全球合作伙伴共同推动行业发展"
            },
            "partnership": {
                "title": "共创未来",
                "content": "与合作伙伴携手共进，共同开拓市场，创造美好未来，实现互利共赢的发展目标。"
            }
        },
        "team": {
            "title": "我们的团队",
            "content": "Linnuo拥有一支由资深工程师、产品专家和技术顾问组成的专业团队。我们的团队成员来自全球知名科技企业，在嵌入式系统、硬件设计、软件开发等领域拥有丰富的经验。"
        },
        "achievements": {
            "title": "发展成就",
            "stats": {
                "products": {
                    "number": "50+",
                    "label": "产品型号"
                },
                "customers": {
                    "number": "10000+",
                    "label": "全球客户"
                },
                "countries": {
                    "number": "80+",
                    "label": "服务国家"
                },
                "experience": {
                    "number": "15+",
                    "label": "年行业经验"
                }
            }
        },
        "technology": {
            "title": "技术优势",
            "content": "我们在单板计算机设计、嵌入式系统开发、AI边缘计算等领域拥有核心技术优势。通过持续的研发投入和技术创新，我们为客户提供最先进的解决方案。"
        }
    },
    "after-sales": {
        "title": "售后服务",
        "subtitle": "感谢您购买Linnuo产品。为了保护您的正当权益，请详细阅读以下内容，充分了解我们公司提供的完善售后服务。",
        "content": "感谢您购买Linnuo产品。为了保护您的正当权益，请详细阅读以下内容，充分了解我们公司提供的完善售后服务。\n\n1.使用我公司产品前，请务必仔细阅读产品附带的用户手册，并按照说明的使用方法进行操作。客户送修故障产品时，应随产品附上维修表格申请维修。\n\n2.使用我公司产品享受一年保修服务。\nA.一年期限从发货日期开始计算。\nB.非我公司制造的产品及周边产品，除另有规定外，均按原厂提供的保修条件执行。\nC.请客户及经销商在送修前备份硬盘数据，我公司不代为备份、恢复或传输数据。\nD.送修各类产品前，请自行卸载产品中的非法软件，否则由此引起的任何相关法律责任与本公司无关。\nE.一般故障产品，请勿附带任何配件。如需配件，请在维修申请表中详细注明。如未详细说明配件，我公司不负责配件丢失。\n\n3.在以下情况下，即使在保修期内，本公司也不提供上述保修服务：\nA.未按用户手册提供的使用方法操作而造成的损坏。\nB.个人运输、携带过程中因跌落、震动而造成的损坏。\nC.因火灾、地震、洪水、电击或其他自然灾害、污染，或使用错误电源而损坏。\nD.因存储环境不当，如高温、高湿度，或接近挥发性产品而损坏。\nE.因电池磨损、更换新电池、电解液外流而损坏。\nF.如非本公司技术人员维修或改动而造成损坏，本公司将收取额外费用。\nG.产品上附着的产品序列号被更改或破坏。\nH.非我公司列出的保修项目。\n\n4.客户需承担将待维修产品运送到本公司或原经销商的损坏风险和运费。\n\n5.为确保维修效率和质量，请下载维修申请表。本公司的维修政策对已填写维修申请表的客户优先处理。\n\n如果您对我们产品的使用有任何疑问，请随时联系我们的FAE工作人员，或将您的问题发送至邮箱：<EMAIL>。谢谢！",
        "introduction": {
            "title": "服务承诺",
            "content": "感谢您购买Linnuo产品。为了保护您的正当权益，请详细阅读以下内容，充分了解我们公司提供的完善售后服务。"
        },
        "beforeUse": {
            "title": "使用前须知",
            "content": "使用我公司产品前，请务必仔细阅读产品附带的用户手册，并按照说明的使用方法进行操作。客户送修故障产品时，应随产品附上维修表格申请维修。"
        },
        "warrantyService": {
            "title": "一年保修服务",
            "subtitle": "使用我公司产品享受一年保修服务",
            "terms": [
                {
                    "label": "A.",
                    "content": "一年期限从发货日期开始计算。"
                },
                {
                    "label": "B.",
                    "content": "非我公司制造的产品及周边产品，除另有规定外，均按原厂提供的保修条件执行。"
                },
                {
                    "label": "C.",
                    "content": "请客户及经销商在送修前备份硬盘数据，我公司不代为备份、恢复或传输数据。"
                },
                {
                    "label": "D.",
                    "content": "送修各类产品前，请自行卸载产品中的非法软件，否则由此引起的任何相关法律责任与本公司无关。"
                },
                {
                    "label": "E.",
                    "content": "一般故障产品，请勿附带任何配件。如需配件，请在维修申请表中详细注明。如未详细说明配件，我公司不负责配件丢失。"
                }
            ]
        },
        "warrantyExclusions": {
            "title": "保修排除条款",
            "subtitle": "在以下情况下，即使在保修期内，本公司也不提供上述保修服务：",
            "items": [
                {
                    "label": "A.",
                    "content": "未按用户手册提供的使用方法操作而造成的损坏。"
                },
                {
                    "label": "B.",
                    "content": "个人运输、携带过程中因跌落、震动而造成的损坏。"
                },
                {
                    "label": "C.",
                    "content": "因火灾、地震、洪水、电击或其他自然灾害、污染，或使用错误电源而损坏。"
                },
                {
                    "label": "D.",
                    "content": "因存储环境不当，如高温、高湿度，或接近挥发性产品而损坏。"
                },
                {
                    "label": "E.",
                    "content": "因电池磨损、更换新电池、电解液外流而损坏。"
                },
                {
                    "label": "F.",
                    "content": "如非本公司技术人员维修或改动而造成损坏，本公司将收取额外费用。"
                },
                {
                    "label": "G.",
                    "content": "产品上附着的产品序列号被更改或破坏。"
                },
                {
                    "label": "H.",
                    "content": "非我公司列出的保修项目。"
                }
            ]
        },
        "additionalTerms": {
            "title": "其他条款",
            "items": [
                {
                    "number": "4.",
                    "content": "客户需承担将待维修产品运送到本公司或原经销商的损坏风险和运费。"
                },
                {
                    "number": "5.",
                    "content": "为确保维修效率和质量，请下载维修申请表。本公司的维修政策对已填写维修申请表的客户优先处理。"
                }
            ]
        },
        "contactSupport": {
            "title": "技术支持与联系",
            "subtitle": "如果您对我们产品的使用有任何疑问，请随时联系我们的FAE工作人员，或将您的问题发送至邮箱。",
            "content": "如果您对我们产品的使用有任何疑问，请随时联系我们的FAE工作人员，或将您的问题发送至邮箱：<EMAIL>。谢谢！",
            "downloadForm": "下载维修申请表",
            "repairForm": {
                "title": "维修申请表",
                "description": "下载维修申请表享受优先维修服务"
            },
            "emailSupport": {
                "title": "邮件支持",
                "description": "发送您的问题到我们的技术支持邮箱",
                "email": "<EMAIL>"
            },
            "phoneSupport": {
                "title": "技术支持热线",
                "description": "联系我们的FAE技术专家",
                "phone": "+86-152-7791-5606"
            }
        }
    },
    "ai-machine-learning": {
        "hero": {
            "title": "人工智能与机器学习",
            "subtitle": "在边缘构建智能AI应用。利用机器学习、计算机视觉和自然语言处理的力量，创建能够实时学习和适应的智能系统。",
            "startBuilding": "开始构建",
            "viewDocumentation": "查看文档"
        },
        "keyCapabilities": {
            "title": "核心功能",
            "subtitle": "构建先进AI和机器学习解决方案所需的一切",
            "features": {
                "computerVision": {
                    "title": "计算机视觉",
                    "description": "在边缘进行物体检测、面部识别和图像分类"
                },
                "naturalLanguageProcessing": {
                    "title": "自然语言处理",
                    "description": "语音识别、文本分析和对话式AI应用"
                },
                "predictiveAnalytics": {
                    "title": "预测分析",
                    "description": "实时数据分析和机器学习推理用于决策制定"
                },
                "edgeAIInference": {
                    "title": "边缘AI推理",
                    "description": "在本地运行AI模型，实现低延迟、注重隐私的应用"
                }
            }
        },
        "featuredProjects": {
            "title": "精选项目",
            "subtitle": "您今天就可以构建的真实AI和机器学习项目",
            "difficulty": {
                "beginner": "初级",
                "intermediate": "中级",
                "advanced": "高级"
            },
            "projects": {
                "aiPoweredSecuritySystem": {
                    "title": "AI驱动的安全系统",
                    "description": "具有面部识别、行为分析和自动威胁检测的智能监控",
                    "duration": "3-4周",
                    "components": "LinnuoSigma, AI加速器, IP摄像头, Edge TPU",
                    "features": "面部识别, 行为分析, 实时警报, 隐私保护"
                },
                "smartVoiceAssistant": {
                    "title": "智能语音助手",
                    "description": "具有自然语言理解和智能家居控制的AI助手",
                    "duration": "2-3周",
                    "components": "LinnuoDelta 3, 麦克风阵列, 扬声器, AI芯片",
                    "features": "语音识别, 自然语言理解, 智能家居控制, 离线处理"
                },
                "predictiveMaintenanceSystem": {
                    "title": "预测性维护系统",
                    "description": "使用机器学习预测设备故障的工业AI解决方案",
                    "duration": "4-5周",
                    "components": "LinnuoSigma, 传感器套件, 数据记录器, ML加速器",
                    "features": "异常检测, 预测分析, 实时监控, 维护调度"
                }
            },
            "viewProjectGuide": "查看项目指南"
        },
        "tutorialsResources": {
            "title": "教程与资源",
            "subtitle": "通过我们全面的AI和机器学习指南逐步学习",
            "tutorials": {
                "gettingStartedAI": {
                    "title": "AI开发入门",
                    "description": "学习在Linnuo上进行AI开发的基础知识",
                    "type": "文档指南",
                    "duration": "45分钟"
                },
                "buildingComputerVisionApp": {
                    "title": "构建计算机视觉应用",
                    "description": "创建一个实时物体检测和识别系统",
                    "type": "视频教程",
                    "duration": "1小时"
                },
                "deployingMLModels": {
                    "title": "部署机器学习模型",
                    "description": "学习如何在边缘设备上优化和部署ML模型",
                    "type": "视频教程",
                    "duration": "1.5小时"
                }
            },
            "watchTutorial": "观看教程",
            "downloadGuide": "下载指南"
        },
        "callToAction": {
            "title": "准备构建您的AI项目？",
            "subtitle": "开始使用Linnuo，将人工智能的力量带到您的应用中",
            "shopLinnuo": "购买Linnuo",
            "joinCommunity": "加入社区"
        },
        "common": {
            "components": "组件",
            "features": "功能",
            "duration": "持续时间",
            "difficulty": "难度"
        }
    },
    "application": {
        "hero": {
            "title": "应用案例",
            "subtitle": "探索Linnuo单板计算机在各个领域的创新应用，从物联网到人工智能，从工业自动化到教育研究。",
            "exploreProjects": "探索项目",
            "viewDocumentation": "查看文档"
        },
        "categories": {
            "title": "应用领域",
            "subtitle": "Linnuo单板计算机在多个行业和领域中发挥着重要作用",
            "iotSmartHome": {
                "title": "物联网智能家居",
                "description": "构建智能家居系统，实现设备互联和自动化控制",
                "applications": [
                    "智能照明控制",
                    "温度湿度监控",
                    "安防监控系统",
                    "智能门锁",
                    "语音助手集成"
                ]
            },
            "aiMachineLearning": {
                "title": "人工智能与机器学习",
                "description": "利用强大的计算能力进行AI模型训练和推理",
                "applications": [
                    "图像识别",
                    "自然语言处理",
                    "边缘计算",
                    "机器视觉",
                    "深度学习推理"
                ]
            },
            "industrialAutomation": {
                "title": "工业自动化",
                "description": "为工业4.0提供可靠的计算和控制解决方案",
                "applications": [
                    "生产线控制",
                    "设备监控",
                    "质量检测",
                    "预测性维护",
                    "数据采集"
                ]
            },
            "educationResearch": {
                "title": "教育与研究",
                "description": "为学术研究和教学提供灵活的计算平台",
                "applications": [
                    "编程教学",
                    "科研项目",
                    "原型开发",
                    "实验平台",
                    "创客教育"
                ]
            },
            "compactEmbedded": {
                "title": "小尺寸嵌入式",
                "description": "紧凑型设计，适用于空间受限的应用场景",
                "applications": [
                    "紧凑设计",
                    "低功耗",
                    "高集成度",
                    "可靠性高"
                ]
            },
            "energyPower": {
                "title": "能源电力",
                "description": "为能源管理和电力系统提供智能化解决方案",
                "applications": [
                    "能源监控",
                    "智能调度",
                    "故障诊断",
                    "数据分析"
                ]
            },
            "networkCommunication": {
                "title": "网络通信",
                "description": "构建高性能网络设备和通信系统",
                "applications": [
                    "网络路由器",
                    "防火墙设备",
                    "VPN网关",
                    "网络存储",
                    "通信基站"
                ]
            }
        },
        "featuredProjects": {
            "title": "精选项目",
            "subtitle": "查看使用Linnuo单板计算机构建的优秀项目案例",
            "viewAllProjects": "查看所有项目",
            "difficulty": {
                "beginner": "初级",
                "intermediate": "中级",
                "advanced": "高级"
            },
            "projects": {
                "smartMirror": {
                    "title": "智能魔镜",
                    "description": "集成天气、日历、新闻等信息的智能显示镜",
                    "tags": [
                        "物联网",
                        "显示",
                        "传感器",
                        "语音控制"
                    ]
                },
                "droneController": {
                    "title": "无人机控制器",
                    "description": "高性能无人机飞行控制和图像处理系统",
                    "tags": [
                        "飞行控制",
                        "图像处理",
                        "实时系统",
                        "传感器融合"
                    ]
                },
                "iotGateway": {
                    "title": "物联网网关",
                    "description": "连接多种设备的智能物联网数据网关",
                    "tags": [
                        "物联网",
                        "数据处理",
                        "网络",
                        "协议转换"
                    ]
                }
            }
        },
        "callToAction": {
            "title": "开始您的项目",
            "subtitle": "选择合适的Linnuo单板计算机，开始构建您的创新项目"
        },
        "benefits": {
            "industrialAutomation": [
                "提高生产效率和产品质量",
                "降低人工成本和运营风险",
                "实现设备智能化管理",
                "支持工业4.0数字化转型"
            ],
            "iotSmartHome": [
                "设备互联互通",
                "智能化场景控制",
                "远程监控管理",
                "节能环保解决方案"
            ],
            "aiMachineLearning": [
                "强大的AI计算能力",
                "支持多种深度学习框架",
                "边缘计算降低延迟",
                "灵活的模型部署方案"
            ],
            "educationResearch": [
                "丰富的教学资源",
                "开放的开发环境",
                "项目实践平台",
                "创新能力培养"
            ],
            "compactEmbedded": [
                "节省空间",
                "降低功耗",
                "简化部署",
                "提高可靠性"
            ],
            "energyPower": [
                "优化能源使用",
                "提高系统效率",
                "预防故障发生",
                "降低运营成本"
            ],
            "networkCommunication": [
                "高速数据传输",
                "稳定网络连接",
                "安全通信保障",
                "灵活网络配置"
            ]
        },
        "caseStudies": {
            "smartFactory": {
                "title": "智能工厂自动化系统",
                "industry": "智能制造",
                "challenge": "传统制造业面临生产效率低、质量控制难、人工成本高等挑战，急需数字化转型。",
                "solution": "采用Linnuo单板计算机构建智能工厂系统，集成传感器网络、机器视觉和AI算法，实现生产过程的全面自动化和智能化管理。",
                "results": [
                    "生产效率提升40%",
                    "产品质量合格率达到99.5%",
                    "人工成本降低30%",
                    "设备故障率减少60%"
                ],
                "specs": "Linnuo-X1 Pro, 8GB RAM, 64GB eMMC, 工业级温度范围"
            },
            "edgeComputing": {
                "title": "边缘计算数据中心",
                "industry": "边缘计算",
                "challenge": "传统云计算架构在处理实时数据时存在延迟高、带宽成本大的问题，需要在边缘侧部署计算能力。",
                "solution": "使用Linnuo单板计算机构建分布式边缘计算节点，在数据产生的源头进行实时处理和分析，减少数据传输延迟。",
                "results": [
                    "数据处理延迟降低80%",
                    "网络带宽使用减少50%",
                    "系统响应时间提升3倍",
                    "运营成本降低35%"
                ],
                "specs": "Linnuo-Edge, 16GB RAM, 128GB SSD, 多网口配置"
            },
            "machineVision": {
                "title": "机器视觉质检系统",
                "industry": "机器视觉",
                "challenge": "传统人工质检效率低、主观性强、无法24小时连续工作，难以满足现代制造业的质量要求。",
                "solution": "基于Linnuo单板计算机开发高精度机器视觉系统，结合深度学习算法实现产品缺陷的自动检测和分类。",
                "results": [
                    "检测精度达到99.8%",
                    "检测速度提升10倍",
                    "24小时不间断工作",
                    "质检成本降低70%"
                ],
                "specs": "Linnuo-Vision, GPU加速, 4K摄像头支持, 实时图像处理"
            },
            "smartGrid": {
                "title": "智能电网监控平台",
                "industry": "能源电力",
                "challenge": "传统电网缺乏实时监控和智能调度能力，难以应对新能源接入和负荷波动的挑战。",
                "solution": "部署Linnuo单板计算机构建智能电网监控系统，实现电网状态的实时监测、故障预警和智能调度。",
                "results": [
                    "电网稳定性提升45%",
                    "故障响应时间缩短60%",
                    "能源利用效率提高25%",
                    "维护成本降低40%"
                ],
                "specs": "Linnuo-Grid, 工业级设计, 多协议支持, 冗余电源"
            }
        },
        "advantages": {
            "reliability": {
                "title": "可靠性保证",
                "description": "工业级设计，通过严格测试，确保在恶劣环境下稳定运行"
            },
            "customization": {
                "title": "定制化服务",
                "description": "提供灵活的硬件配置和软件定制，满足不同应用场景需求"
            },
            "delivery": {
                "title": "快速交付",
                "description": "完善的供应链体系，确保产品快速交付和技术支持"
            },
            "support": {
                "title": "专业支持",
                "description": "专业的技术团队提供全方位支持，从方案设计到售后服务"
            }
        }
    },
    "common": {
        "loading": "加载中...",
        "error": {
            "title": "出现错误",
            "description": "发生了意外错误，请重试。",
            "retry": "重试",
            "backHome": "返回首页"
        },
        "success": "成功",
        "cancel": "取消",
        "confirm": "确认",
        "save": "保存",
        "edit": "编辑",
        "delete": "删除",
        "close": "关闭",
        "next": "下一页",
        "previous": "上一页",
        "viewMore": "查看更多",
        "showLess": "收起",
        "category": "分类",
        "author": "作者",
        "date": "日期",
        "tags": "标签",
        "featured": "精选",
        "new": "新品",
        "popular": "热门",
        "recommended": "推荐",
        "search": "搜索",
        "language": "语言",
        "home": "首页",
        "features": "功能特性",
        "benefits": "核心优势",
        "notFound": {
            "title": "页面未找到",
            "description": "您要查找的页面不存在或已被移动。",
            "backHome": "返回首页",
            "search": "搜索网站",
            "goBack": "返回上页",
            "helpText": "如果您认为这是一个错误，请联系我们的支持团队。"
        },
        "loadingStates": {
            "loading": "加载中...",
            "loadingData": "正在加载数据...",
            "processing": "处理中...",
            "saving": "保存中...",
            "searching": "搜索中...",
            "submitting": "提交中..."
        }
    },
    "contact": {
        "title": "联系我们",
        "subtitle": "联系我们的团队获取支持、合作伙伴关系或一般咨询。",
        "loadingText": "正在加载联系信息...",
        "postalCode": "邮政编码",
        "form": {
            "title": "发送消息",
            "firstName": "名",
            "lastName": "姓",
            "email": "邮箱",
            "subject": "主题",
            "message": "消息",
            "firstNamePlaceholder": "张",
            "lastNamePlaceholder": "三",
            "emailPlaceholder": "<EMAIL>",
            "subjectPlaceholder": "我们如何帮助您？",
            "messagePlaceholder": "告诉我们更多关于您的咨询...",
            "sendMessage": "发送消息"
        },
        "contactInfo": {
            "business": {
                "title": "商务销售"
            },
            "technical": {
                "title": "技术支持"
            },
            "email": {
                "title": "邮箱",
                "value": "<EMAIL>",
                "description": "一般咨询和支持"
            },
            "community": {
                "title": "社区",
                "value": "加入我们的Discord和论坛"
            },
            "address": {
                "title": "地址",
                "value": "Linnuo科技\n创新区\n科技城, TC 12345"
            },
            "website": {
                "title": "网站"
            },
            "wechat": {
                "title": "微信",
                "description": "扫描二维码添加我们的微信"
            },
            "hours": {
                "title": "营业时间",
                "business": "商务销售",
                "support": "技术支持"
            }
        }
    },
    "customized-service": {
        "title": "定制服务",
        "subtitle": "专业的ODM/OEM嵌入式计算解决方案服务",
        "badge": "销售与支持",
        "features": {
            "title": "为什么选择我们的定制服务？",
            "quickProductization": {
                "title": "快速产品化",
                "description": "定制解决方案的快速开发和部署"
            },
            "hardwareSoftwareSupport": {
                "title": "底层硬件和软件支持",
                "description": "从硬件到软件的完整技术支持"
            },
            "supplyGuarantee": {
                "title": "三级供应保障体系",
                "description": "可靠的供应链管理和质量保证"
            },
            "costOptimization": {
                "title": "优化成本结构",
                "description": "根据您的预算量身定制的经济高效解决方案"
            }
        },
        "process": {
            "title": "非标设计ODM/OEM服务流程",
            "subtitle": "专业流程确保项目成功",
            "communication": {
                "title": "沟通",
                "description": "明确客户需求"
            },
            "proposal": {
                "title": "产品方案",
                "description": "提供最佳开发方案"
            },
            "evaluation": {
                "title": "项目评估",
                "description": "确定产品实施方案"
            },
            "trial": {
                "title": "产品试用",
                "description": "提供开发样品供客户确认"
            },
            "confirmation": {
                "title": "试产确认",
                "description": "小批量试产"
            },
            "delivery": {
                "title": "量产交付",
                "description": "快速完美的交付服务"
            }
        },
        "form": {
            "title": "申请定制服务",
            "subtitle": "告诉我们您的项目需求，我们将提供量身定制的解决方案",
            "companyName": "公司名称",
            "contactPerson": "联系人",
            "email": "邮箱地址",
            "phone": "电话号码",
            "projectType": "项目类型",
            "projectDescription": "项目描述",
            "requirements": "项目需求",
            "timeline": "项目时间",
            "budget": "预算范围",
            "additionalInfo": "附加信息",
            "submit": "提交定制申请",
            "submitting": "提交中...",
            "required": "必填字段",
            "successMessage": "谢谢您！您的定制申请已成功提交。我们将在24小时内与您联系。",
            "errorMessage": "抱歉，提交您的申请时出现错误。请重试。",
            "expectedQuantity": "预期数量",
            "privacyConsent": "我同意隐私政策并同意处理我的个人数据",
            "placeholders": {
                "companyName": "输入您的公司名称",
                "contactPerson": "输入联系人姓名",
                "email": "输入邮箱地址",
                "phone": "输入电话号码",
                "projectDescription": "描述您的项目目标和需求...",
                "requirements": "请描述您的项目需求、技术规格和所需的特殊功能...",
                "additionalInfo": "任何附加信息或特殊要求...",
                "selectProjectType": "选择项目类型",
                "selectQuantity": "选择数量范围",
                "selectTimeline": "选择时间线"
            },
            "projectTypes": {
                "iot": "物联网解决方案",
                "industrial": "工业自动化",
                "ai": "AI/ML应用",
                "embedded": "嵌入式系统",
                "other": "其他",
                "odm": "ODM（原始设计制造商）",
                "oem": "OEM（原始设备制造商）",
                "customBoard": "定制板设计",
                "softwareDev": "软件开发",
                "integration": "系统集成"
            },
            "quantities": {
                "1-100": "1-100 台",
                "100-500": "100-500 台",
                "500-1000": "500-1,000 台",
                "1000-5000": "1,000-5,000 台",
                "5000+": "5,000+ 台"
            },
            "timelines": {
                "1-3months": "1-3个月",
                "3-6months": "3-6个月",
                "6-12months": "6-12个月",
                "12months": "12个月以上",
                "12months+": "12个月以上"
            },
            "budgets": {
                "under10k": "10万元以下",
                "10k-50k": "10万-50万元",
                "50k-100k": "50万-100万元",
                "over100k": "100万元以上"
            }
        }
    },
    "downloads": {
        "title": "下载",
        "resources": "资源",
        "subtitle": "获取所有 Linnuo产品的驱动程序、文档、固件和开发工具",
        "searchPlaceholder": "搜索文件...",
        "totalFiles": "文件总数",
        "categories": {
            "all": "所有分类",
            "drivers": "驱动程序",
            "documentation": "文档资料",
            "firmware": "固件",
            "software": "软件",
            "specifications": "规格说明",
            "images": "系统镜像",
            "category": "分类",
            "developmentTools": "开发工具",
            "libraries": "库文件"
        },
        "products": {
            "all": "所有产品",
            "product": "产品"
        },
        "fileInfo": {
            "version": "版本",
            "size": "大小",
            "date": "日期",
            "downloads": "下载次数",
            "totalFiles": "文件总数"
        },
        "downloadButton": "下载",
        "noResults": "未找到符合搜索条件的文件。",
        "clearFilters": "清除筛选",
        "allDownloads": "所有下载",
        "completeCollection": "驱动程序、文档和工具的完整集合",
        "noDownloadsFound": "未找到下载文件",
        "tryAdjusting": "请尝试调整您的搜索或筛选条件。",
        "loadMore": "加载更多下载",
        "needHelp": "需要帮助？",
        "cantFind": "找不到您要的文件？我们的支持团队将帮助您获取项目所需的正确文件。",
        "viewDocumentation": "查看文档",
        "contactSupport": "联系支持",
        "featured": "推荐",
        "compatibleWith": "兼容",
        "loading": "正在加载下载文件...",
        "featuredDownloads": "推荐下载",
        "mostPopular": "最受欢迎和推荐的下载"
    },
    "education-research": {
        "hero": {
            "title": "教育与研究",
            "subtitle": "为学习和发现构建强大的教育工具和研究平台。从交互式学习系统到先进的研究设备，创建激发创新和知识的解决方案。",
            "startBuilding": "开始构建",
            "viewDocumentation": "查看文档"
        },
        "keyCapabilities": {
            "title": "核心功能",
            "subtitle": "构建创新教育和研究解决方案所需的一切",
            "features": {
                "interactiveLearning": {
                    "title": "交互式学习",
                    "description": "创建引人入胜的交互式教育体验和学习平台"
                },
                "dataCollection": {
                    "title": "数据收集",
                    "description": "用于科学研究和实验的高精度数据采集系统"
                },
                "labAutomation": {
                    "title": "实验室自动化",
                    "description": "自动化实验室流程和设备控制，提高研究效率"
                },
                "collaborativePlatforms": {
                    "title": "协作平台",
                    "description": "支持远程学习和研究协作的连接平台"
                }
            }
        },
        "featuredProjects": {
            "title": "精选项目",
            "subtitle": "您今天就可以构建的真实教育和研究项目",
            "difficulty": {
                "beginner": "初级",
                "intermediate": "中级",
                "advanced": "高级"
            },
            "projects": {
                "smartClassroomSystem": {
                    "title": "智能教室系统",
                    "description": "具有交互式显示、自动出勤和智能环境控制的完整教室解决方案",
                    "duration": "4-5周",
                    "components": "LinnuoSigma, 交互式显示器, 传感器套件, 摄像头系统",
                    "features": "交互式学习, 出勤跟踪, 环境控制, 远程访问"
                },
                "researchDataLogger": {
                    "title": "研究数据记录器",
                    "description": "用于科学实验和长期研究项目的高精度数据采集系统",
                    "duration": "2-3周",
                    "components": "LinnuoDelta 3, 精密传感器, 数据存储, 网络模块",
                    "features": "高精度测量, 长期记录, 远程监控, 数据分析"
                },
                "virtualLabPlatform": {
                    "title": "虚拟实验室平台",
                    "description": "支持远程实验和协作研究的虚拟实验室环境",
                    "duration": "5-6周",
                    "components": "LinnuoSigma, 实验室设备接口, 视频系统, 云平台",
                    "features": "远程实验, 实时协作, 设备控制, 结果共享"
                }
            },
            "viewProjectGuide": "查看项目指南"
        },
        "tutorialsResources": {
            "title": "教程与资源",
            "subtitle": "通过我们全面的教育和研究指南逐步学习",
            "tutorials": {
                "buildingEducationalTools": {
                    "title": "构建教育工具",
                    "description": "学习如何创建交互式教育应用和学习平台",
                    "type": "文档指南",
                    "duration": "1小时"
                },
                "scientificDataAcquisition": {
                    "title": "科学数据采集",
                    "description": "设置精确的数据采集系统用于研究和实验",
                    "type": "视频教程",
                    "duration": "1.5小时"
                },
                "labAutomationSetup": {
                    "title": "实验室自动化设置",
                    "description": "实施自动化解决方案以提高实验室效率和准确性",
                    "type": "视频教程",
                    "duration": "2小时"
                }
            },
            "watchTutorial": "观看教程",
            "downloadGuide": "下载指南"
        },
        "callToAction": {
            "title": "准备构建您的教育解决方案？",
            "subtitle": "开始使用Linnuo，为学习和研究创建创新的工具和平台",
            "shopLinnuo": "购买Linnuo",
            "joinCommunity": "加入社区"
        },
        "common": {
            "components": "组件",
            "features": "功能",
            "duration": "持续时间",
            "difficulty": "难度"
        }
    },
    "footer": {
        "description": "Linnuo是全球开发者、创客和创新者高性能单板计算机的领先提供商。",
        "quickLinks": "快速链接",
        "products": "产品",
        "support": "支持",
        "company": "公司",
        "followUs": "关注我们",
        "newsletter": "新闻通讯",
        "newsletterDescription": "订阅我们的新闻通讯，获取最新更新和公告。",
        "emailPlaceholder": "输入您的邮箱",
        "subscribe": "订阅",
        "allRightsReserved": "版权所有。",
        "privacyPolicy": "隐私政策",
        "termsOfService": "服务条款",
        "contactUs": "联系我们",
        "businessHours": "营业时间",
        "technicalSupport": "技术支持"
    },
    "hero": {
        "title": "强大的单板计算机",
        "subtitle": "释放您的创造力",
        "description": "Linnuo提供高性能单板计算机，完美适合开发者、创客和创新者。使用我们的尖端技术构建令人惊叹的项目。",
        "getStarted": "开始使用",
        "learnMore": "了解更多",
        "watchVideo": "观看视频",
        "exploreProducts": "探索产品",
        "viewDocumentation": "查看文档",
        "carousel": {
            "loading": "正在加载图片...",
            "error": "加载幻灯片时出错",
            "retry": "重试",
            "previous": "上一个",
            "next": "下一个",
            "goToSlide": "转到幻灯片",
            "pause": "暂停",
            "auto": "自动",
            "resume": "恢复",
            "paused": "已暂停",
            "previousImage": "上一张图片",
            "nextImage": "下一张图片",
            "goToImage": "转到第{{number}}张图片",
            "slides": {
                "LinnuoSigma": "LinnuoSigma - 高性能计算",
                "developerEcosystem": "开发者生态系统 - 社区与支持",
                "aiMachineLearning": "人工智能与机器学习解决方案",
                "default": "Linnuo产品展示"
            }
        }
    },
    "home": {
        "featuredProducts": {
            "title": "特色产品",
            "subtitle": "探索我们最受欢迎的单板计算机，专为全球创客、开发者和创新者设计。",
            "mainTitle": "特色产品",
            "categories": {
                "universalEmbeddedSeries": "通用嵌入式系列",
                "allInOneIpc": "一体化工控机"
            },
            "loading": "产品正在加载中，请稍后查看。",
            "viewAll": "查看所有产品"
        },
        "featureHighlights": {
            "title": "为什么选择 Linnuo？",
            "subtitle": "我们的单板计算机将完整PC的强大功能与嵌入式系统的灵活性相结合，使其成为任何项目的完美选择。",
            "features": {
                "intelProcessors": {
                    "title": "英特尔 x86 处理器",
                    "description": "最新的英特尔处理器，最多12核心和4.6GHz加速频率，提供最大性能。"
                },
                "highPerformance": {
                    "title": "高性能",
                    "description": "运行要求苛刻的应用程序、游戏和开发工具，具备桌面级性能。"
                },
                "reliableStable": {
                    "title": "可靠稳定",
                    "description": "工业级组件和严格测试确保在任何环境下都能可靠运行。"
                },
                "developerFriendly": {
                    "title": "开发者友好",
                    "description": "完整的Windows和Linux支持，兼容Arduino，实现无缝开发。"
                },
                "advancedConnectivity": {
                    "title": "先进连接",
                    "description": "Wi-Fi 6E、蓝牙5.3、USB4和千兆以太网，满足您的所有连接需求。"
                },
                "expandableStorage": {
                    "title": "可扩展存储",
                    "description": "内置eMMC存储，配备M.2插槽支持NVMe SSD和microSD卡。"
                },
                "displaySupport": {
                    "title": "4K显示支持",
                    "description": "双4K显示输出，配备HDMI 2.1和DisplayPort，提供令人惊叹的视觉体验。"
                },
                "gamingReady": {
                    "title": "游戏就绪",
                    "description": "英特尔Iris Xe显卡，具备硬件加速功能，适用于游戏和多媒体应用。"
                }
            }
        },
        "communitySection": {
            "title": "关于我们",
            "subtitle": "我们致力于为创客、开发者和创新者提供强大、易用的单板计算解决方案。",
            "description": {
                "paragraph1": "凌诺科技是高性能单板计算机的领先供应商，致力于为全球的创客、开发者和创新者提供支持。自成立以来，我们一直致力于计算技术的民主化，让每个人都能获得强大、易用的解决方案。",
                "paragraph2": "我们的产品组合包括通用嵌入式系列和一体化工控机解决方案，采用尖端的英特尔处理器、先进的连接选项和坚固的工业级组件。从构建第一个项目的创客到部署大规模解决方案的企业，我们的主板为任何应用提供所需的性能和可靠性。",
                "paragraph3": "凭借遍布100多个国家、数百万用户的全球社区，Linnuo继续推动单板计算领域的可能性边界。我们相信创新、质量和社区的力量能够推动技术进步，为所有人创造更美好的未来。"
            },
            "stats": {
                "founded": "成立时间",
                "globalUsers": "全球用户",
                "countries": "国家",
                "awards": "奖项"
            },
            "values": {
                "title": "我们的价值观",
                "subtitle": "指导我们所有行动并推动我们使命前进的原则。",
                "innovation": {
                    "title": "创新",
                    "description": "用尖端技术推动单板计算的边界。"
                },
                "quality": {
                    "title": "质量",
                    "description": "提供可靠、高性能的产品，超越期望。"
                },
                "community": {
                    "title": "社区",
                    "description": "构建全球创客、开发者和创新者社区。"
                },
                "growth": {
                    "title": "成长",
                    "description": "赋能创作者将想法变为现实并扩展项目。"
                }
            },
            "journey": {
                "title": "我们的历程",
                "subtitle": "在我们民主化计算技术使命中的关键里程碑。",
                "milestones": {
                    "founded": {
                        "title": "公司成立",
                        "description": "Linnuo成立，愿景是让计算民主化。"
                    },
                    "firstProduct": {
                        "title": "首款产品发布",
                        "description": "发布我们的第一款单板计算机，革命性地改变了创客空间。"
                    },
                    "globalExpansion": {
                        "title": "全球扩张",
                        "description": "扩展到为全球100多个国家的客户提供服务。"
                    },
                    "nextGeneration": {
                        "title": "下一代产品",
                        "description": "推出LinnuoSigma，我们迄今为止最强大的主板。"
                    }
                }
            }
        },
        "newsSection": {
            "title": "最新新闻",
            "subtitle": "了解我们的最新发展",
            "description": "获取产品发布、社区亮点和行业洞察的最新更新。",
            "readMore": "阅读更多",
            "viewAllNews": "查看所有新闻",
            "noNews": "暂时没有新闻。",
            "loadingNews": "正在加载最新新闻...",
            "minRead": "分钟阅读"
        }
    },
    "industrial-automation": {
        "hero": {
            "title": "工业自动化",
            "subtitle": "构建智能工业系统和自动化解决方案。从过程控制到预测性维护，创建提高效率、质量和安全性的强大工业应用。",
            "startBuilding": "开始构建",
            "viewDocumentation": "查看文档"
        },
        "keyCapabilities": {
            "title": "核心功能",
            "subtitle": "构建先进工业自动化解决方案所需的一切",
            "features": {
                "processControl": {
                    "title": "过程控制",
                    "description": "制造过程和工业设备的实时控制系统"
                },
                "qualityInspection": {
                    "title": "质量检测",
                    "description": "使用计算机视觉和机器学习的自动化质量控制"
                },
                "predictiveMaintenance": {
                    "title": "预测性维护",
                    "description": "监控设备健康状况，在故障发生前预测维护需求"
                },
                "dataAcquisition": {
                    "title": "数据采集",
                    "description": "实时收集、处理和分析工业传感器数据"
                }
            }
        },
        "featuredProjects": {
            "title": "精选项目",
            "subtitle": "您今天就可以构建的真实工业自动化项目",
            "difficulty": {
                "beginner": "初级",
                "intermediate": "中级",
                "advanced": "高级"
            },
            "projects": {
                "smartFactoryControlSystem": {
                    "title": "智能工厂控制系统",
                    "description": "具有实时监控、控制和优化的完整工厂自动化",
                    "duration": "6-8周",
                    "components": "LinnuoSigma, 工业I/O, HMI显示器, 网络基础设施",
                    "features": "实时控制, SCADA集成, 远程监控, 数据分析"
                },
                "qualityInspectionStation": {
                    "title": "质量检测站",
                    "description": "使用AI驱动的计算机视觉进行自动化产品质量检测",
                    "duration": "3-4周",
                    "components": "LinnuoDelta 3, 工业摄像头, 照明系统, 传送带接口",
                    "features": "缺陷检测, 质量评分, 实时处理, 数据分析"
                },
                "predictiveMaintenanceSystem": {
                    "title": "预测性维护系统",
                    "description": "使用机器学习预测设备故障的工业物联网解决方案",
                    "duration": "4-5周",
                    "components": "LinnuoSigma, 传感器套件, 数据记录器, 边缘计算单元",
                    "features": "异常检测, 预测分析, 实时监控, 维护调度"
                }
            },
            "viewProjectGuide": "查看项目指南"
        },
        "tutorialsResources": {
            "title": "教程与资源",
            "subtitle": "通过我们全面的工业自动化指南逐步学习",
            "tutorials": {
                "industrialIoTBasics": {
                    "title": "工业物联网基础",
                    "description": "学习工业物联网系统和传感器集成的基础知识",
                    "type": "文档指南",
                    "duration": "1小时"
                },
                "buildingControlSystem": {
                    "title": "构建控制系统",
                    "description": "创建用于工业过程控制的实时监控和控制系统",
                    "type": "视频教程",
                    "duration": "1.5小时"
                },
                "implementingPredictiveMaintenance": {
                    "title": "实施预测性维护",
                    "description": "部署机器学习驱动的预测性维护解决方案",
                    "type": "视频教程",
                    "duration": "2小时"
                }
            },
            "watchTutorial": "观看教程",
            "downloadGuide": "下载指南"
        },
        "callToAction": {
            "title": "准备构建您的工业解决方案？",
            "subtitle": "开始使用Linnuo，将智能自动化和控制带到您的工业应用中",
            "shopLinnuo": "购买Linnuo",
            "joinCommunity": "加入社区"
        },
        "common": {
            "components": "组件",
            "features": "功能",
            "duration": "持续时间",
            "difficulty": "难度"
        }
    },
    "iot-smart-home": {
        "hero": {
            "title": "物联网与智能家居",
            "subtitle": "将您的家庭转变为智能、互联的生态系统。构建自动化系统、监控解决方案和智能设备，让生活更轻松、更高效。",
            "startBuilding": "开始构建",
            "viewDocumentation": "查看文档"
        },
        "keyCapabilities": {
            "title": "核心功能",
            "subtitle": "构建全面的物联网和智能家居解决方案所需的一切",
            "features": {
                "environmentalMonitoring": {
                    "title": "环境监测",
                    "description": "实时监测温度、湿度、空气质量和其他环境参数"
                },
                "securitySystems": {
                    "title": "安全系统",
                    "description": "创建包含摄像头、传感器和自动警报的综合安全解决方案"
                },
                "smartLighting": {
                    "title": "智能照明",
                    "description": "具有调度、运动检测和能源优化的自动照明控制"
                },
                "connectivityHub": {
                    "title": "连接中心",
                    "description": "连接和管理所有物联网设备和传感器的中央枢纽"
                }
            }
        },
        "featuredProjects": {
            "title": "精选项目",
            "subtitle": "您今天就可以构建的真实物联网和智能家居项目",
            "difficulty": {
                "beginner": "初级",
                "intermediate": "中级",
                "advanced": "高级"
            },
            "projects": {
                "smartHomeCentralHub": {
                    "title": "智能家居中央枢纽",
                    "description": "具有语音控制、移动应用和AI驱动调度的完整家庭自动化系统",
                    "duration": "2-3周",
                    "components": "LinnuoSigma, 语音识别模块, 各种传感器, 移动应用",
                    "features": "语音控制, 移动应用, AI调度, 能源监控"
                },
                "environmentalMonitoringStation": {
                    "title": "环境监测站",
                    "description": "监测空气质量、温度、湿度和噪音水平的综合系统",
                    "duration": "1-2周",
                    "components": "LinnuoDelta 3, 环境传感器, 显示屏, 数据记录器",
                    "features": "实时监测, 数据记录, 警报系统, 远程访问"
                },
                "smartSecuritySystem": {
                    "title": "智能安全系统",
                    "description": "具有面部识别、运动检测和自动警报的AI驱动安全解决方案",
                    "duration": "2-3周",
                    "components": "LinnuoSigma, IP摄像头, 运动传感器, 警报系统",
                    "features": "面部识别, 运动检测, 自动警报, 远程监控"
                }
            },
            "viewProjectGuide": "查看项目指南"
        },
        "tutorialsResources": {
            "title": "教程与资源",
            "subtitle": "通过我们全面的指南和视频教程逐步学习",
            "tutorials": {
                "gettingStartedIoT": {
                    "title": "物联网入门",
                    "description": "学习物联网开发的基础知识和最佳实践",
                    "type": "文档指南",
                    "duration": "30分钟"
                },
                "buildingSmartThermostat": {
                    "title": "构建智能恒温器",
                    "description": "创建一个可以远程控制的智能恒温器",
                    "type": "视频教程",
                    "duration": "45分钟"
                },
                "advancedHomeSecuritySetup": {
                    "title": "高级家庭安全设置",
                    "description": "实施包含摄像头和传感器的完整安全系统",
                    "type": "视频教程",
                    "duration": "1.5小时"
                }
            },
            "watchTutorial": "观看教程",
            "downloadGuide": "下载指南"
        },
        "callToAction": {
            "title": "准备构建您的智能家居？",
            "subtitle": "开始使用Linnuo，将您的家庭转变为智能、互联的空间",
            "shopLinnuo": "购买Linnuo",
            "joinCommunity": "加入社区"
        },
        "common": {
            "components": "组件",
            "features": "功能",
            "duration": "持续时间",
            "difficulty": "难度"
        }
    },
    "nav": {
        "home": "首页",
        "products": "产品",
        "salesAndSupport": "销售与支持",
        "application": "应用",
        "about": "关于",
        "sampleApplication": "样品申请",
        "afterSalesService": "售后服务",
        "downloads": "下载",
        "customizedService": "定制服务",
        "iotSmartHome": "物联网与智能家居",
        "aiMachineLearning": "人工智能与机器学习",
        "gamingEntertainment": "游戏与娱乐",
        "industrialAutomation": "工业自动化",
        "educationResearch": "教育与研究",
        "automotiveTransportation": "汽车与交通",
        "aboutUs": "关于我们",
        "news": "新闻",
        "contact": "联系我们",
        "search": "搜索",
        "language": "语言"
    },
    "news": {
        "title": "新闻与更新",
        "subtitle": "保持关注",
        "description": "了解 Linnuo的最新发展、产品公告、社区亮点和行业洞察。",
        "readMore": "阅读更多",
        "loadMore": "加载更多文章",
        "categories": {
            "all": "全部",
            "product": "产品",
            "launch": "发布",
            "awards": "奖项",
            "tutorial": "教程",
            "companyNews": "公司新闻",
            "technology": "技术"
        },
        "searchPlaceholder": "搜索新闻和更新...",
        "noNewsFound": "未找到新闻",
        "noNewsDescription": "请尝试调整搜索或筛选条件。",
        "loadingNews": "正在加载新闻文章...",
        "minRead": "分钟阅读",
        "backToNews": "返回新闻列表",
        "shareArticle": "分享这篇文章",
        "share": "分享",
        "relatedArticles": "相关文章",
        "relatedArticlesInDevelopment": "相关文章功能正在开发中...",
        "publishedOn": "发布于",
        "by": "作者",
        "newsletter": {
            "title": "保持更新",
            "description": "订阅我们的新闻通讯，不错过重要更新、产品发布或社区亮点。",
            "emailPlaceholder": "输入您的邮箱地址",
            "subscribe": "订阅",
            "privacyNote": "我们尊重您的隐私。随时可以取消订阅。"
        },
        "loadMoreArticles": "加载更多文章",
        "newsImage": "新闻图片"
    },
    "products": {
        "title": "我们的产品",
        "subtitle": "发现创新",
        "description": "探索我们为各种应用设计的全面单板计算机系列",
        "viewAll": "查看所有产品",
        "learnMore": "了解更多",
        "specifications": "技术规格",
        "features": "产品特性",
        "documentation": "技术文档",
        "buyNow": "立即购买",
        "addToCart": "加入购物车",
        "categories": {
            "scalableEmbeddedSeries": "可扩展嵌入式系列",
            "miniSizeSeries": "迷你尺寸系列",
            "universalEmbeddedSeries": "通用嵌入式系列",
            "allInOneIpc": "一体化工控机"
        },
        "filters": {
            "all": "所有产品",
            "category": "分类",
            "priceRange": "价格范围",
            "sortBy": "排序方式",
            "newest": "最新",
            "popular": "最受欢迎",
            "priceAsc": "价格：从低到高",
            "priceDesc": "价格：从高到低"
        },
        "page": {
            "title": "产品中心",
            "subtitle": "专业的单板计算机和嵌入式解决方案，为您的项目提供完美支持。",
            "allProducts": "所有产品",
            "browseComplete": "浏览我们完整的产品阵容",
            "browseCategory": "浏览此类别中的产品",
            "loading": "正在加载产品...",
            "noProducts": "此类别中未找到产品。",
            "noImage": "无图片"
        },
        "card": {
            "new": "新品",
            "featured": "精选",
            "details": "详情",
            "compare": "比较",
            "selected": "已选择",
            "selectToCompare": "选择对比",
            "display": "显示",
            "moreFeatures": "+{{count}} 个更多功能..."
        },
        "productCategories": {
            "scalableEmbeddedSeries": {
                "name": "可扩展嵌入式系列",
                "description": "高性能嵌入式计算解决方案"
            },
            "computeModuleSeries": {
                "name": "计算模块系列",
                "description": "紧凑而强大的计算模块"
            },
            "singleBoardComputer": {
                "name": "单板计算机",
                "description": "单板完整计算解决方案"
            },
            "developmentKit": {
                "name": "开发套件",
                "description": "完整的开发和原型制作套件"
            }
        },
        "detail": {
            "breadcrumb": {
                "home": "首页",
                "products": "产品"
            },
            "backToProducts": "返回产品列表",
            "noImageAvailable": "无可用图片",
            "keyFeatures": "主要特性",
            "share": "分享",
            "coreSpecs": "核心规格",
            "requestSample": "申请样品",
            "technicalConsult": "技术咨询",
            "productImage": "产品图片",
            "highResImage": "高分辨率产品图片",
            "productDetailsPlaceholder": "产品详细信息",
            "productDetailsNote": "详细的产品信息和图片已在上方展示",
            "needMoreInfo": "需要更多信息？",
            "expertSupport": "我们的专家将帮助您选择合适的解决方案",
            "viewMoreProducts": "查看更多产品",
            "download": "下载",
            "warrantySupport": "保修与支持",
            "standardSupport": "标准支持",
            "tabs": {
                "description": "产品描述",
                "specifications": "技术规格",
                "compatibility": "兼容性",
                "downloads": "下载与文档",
                "productDetails": "产品详情",
                "applications": "应用场景"
            },
            "specs": {
                "cpu": "处理器",
                "memory": "内存",
                "storage": "存储",
                "network": "网络",
                "power": "电源",
                "os": "操作系统",
                "temperature": "工作温度"
            },
            "specsShort": {
                "cpu": "CPU",
                "memory": "内存",
                "storage": "存储",
                "network": "网络",
                "power": "电源",
                "os": "系统",
                "temperature": "温度",
                "display": "显示"
            },
            "applications": {
                "smartManufacturing": "智能制造",
                "smartManufacturingDesc": "智能生产线控制和监控系统",
                "machineVision": "机器视觉",
                "machineVisionDesc": "高性能图像处理和分析解决方案",
                "edgeComputing": "边缘计算",
                "edgeComputingDesc": "网络边缘的实时数据处理"
            },
            "noContent": "暂无内容",
            "downloadsDescription": "下载 {{productName}} 的驱动程序、手册、固件和其他资源。",
            "noDownloads": "此产品暂无可下载资源。",
            "needHelp": "需要帮助？",
            "documentation": "技术文档",
            "communityForum": "社区论坛",
            "technicalSupport": "技术支持"
        },
        "compare": {
            "clearSelection": "清除选择",
            "viewComparison": "查看对比",
            "addProduct": "添加产品",
            "searchPlaceholder": "搜索产品...",
            "addToCompare": "添加对比",
            "noProductsFound": "未找到产品",
            "maxProductsReached": "最多只能同时对比 4 个产品",
            "removeToAdd": "请移除一个产品以添加新产品",
            "noProducts": "未选择产品",
            "selectProducts": "从我们的产品目录中选择产品，开始比较它们的规格和功能。",
            "title": "产品对比",
            "description": "对比 {{count}} 个已选择的产品",
            "backToProducts": "返回产品列表",
            "viewDetails": "查看详情",
            "specs": {
                "category": "分类",
                "cpu": "处理器",
                "memory": "内存",
                "storage": "存储",
                "network": "网络",
                "display": "显示",
                "power": "电源",
                "os": "操作系统",
                "temperature": "工作温度",
                "dimensions": "尺寸",
                "weight": "重量"
            }
        }
    },
    "sample-application": {
        "title": "样品申请",
        "subtitle": "申请免费的Linnuo单板计算机样例，用于您的项目评估。快速审批和发货流程。",
        "description": "申请免费的Linnuo单板计算机样例，用于您的项目评估。快速审批和发货流程。",
        "thankYou": "谢谢您！",
        "successMessage": "您的样品申请已成功提交。我们将在1-2个工作日内与您联系。",
        "submitAnother": "提交另一个申请",
        "successTitle": "提交成功！",
        "successDescription": "您的样品申请已成功提交。我们将在1-2个工作日内与您联系，确认样品发送详情。",
        "errorTitle": "提交失败",
        "errorDescription": "抱歉，提交过程中出现了问题",
        "retrySubmit": "重新填写",
        "refreshPage": "刷新页面",
        "browseProducts": "浏览更多产品",
        "networkError": "网络错误，请检查网络连接后重试",
        "submitError": "提交失败，请稍后重试",
        "form": {
            "name": "姓名",
            "company": "公司名称",
            "phoneNumber": "电话号码",
            "email": "邮箱",
            "sampleName": "样品名称",
            "quantity": "申请数量",
            "purpose": "使用用途",
            "selectProduct": "选择产品",
            "timeOfRequest": "需要时间",
            "requirements": "需求描述",
            "address": "收货地址",
            "urgency": "紧急程度",
            "requestPlaceholder": "请详细描述您的样品需求...",
            "submit": "提交申请",
            "submitting": "提交中...",
            "required": "必填字段",
            "invalidEmail": "请输入有效的邮箱地址",
            "invalidPhone": "请输入有效的电话号码",
            "selectProductRequired": "请选择一个产品",
            "descriptionRequired": "请描述您的项目需求",
            "successTitle": "申请提交成功",
            "errorTitle": "提交失败",
            "errorMessage": "抱歉，提交您的申请时出现错误。请重试。",
            "placeholders": {
                "name": "输入您的姓名",
                "company": "输入您的公司名称",
                "phoneNumber": "输入您的电话号码",
                "email": "输入您的邮箱地址",
                "sampleName": "输入样品名称",
                "quantity": "输入申请数量",
                "purpose": "请说明样品的使用用途...",
                "requirements": "请详细描述您的样品需求...",
                "address": "请填写详细的收货地址..."
            },
            "urgencyOptions": {
                "normal": "普通",
                "urgent": "紧急",
                "very_urgent": "非常紧急"
            }
        },
        "products": {
            "LinnuoSigma": "LinnuoSigma",
            "LinnuoDelta3": "LinnuoDelta 3",
            "LinnuoMu": "LinnuoMu",
            "LinnuoAlpha": "LinnuoAlpha",
            "LinnuoV1": "LinnuoV1"
        },
        "benefits": {
            "title": "为什么申请样例？",
            "subtitle": "获得免费样例的优势",
            "freeEvaluation": {
                "title": "免费评估",
                "description": "在投资之前测试产品是否符合您的项目需求"
            },
            "fastShipping": {
                "title": "快速发货",
                "description": "样品申请通过后，我们将在1-2个工作日内发货"
            },
            "technicalSupport": {
                "title": "技术支持",
                "description": "获得我们技术团队的专业支持和指导"
            },
            "noCommitment": {
                "title": "无承诺",
                "description": "评估样例无需任何购买承诺"
            }
        },
        "requirements": {
            "title": "申请要求",
            "subtitle": "为了确保样例用于合适的项目",
            "items": [
                "提供详细的项目描述",
                "说明预期的使用场景",
                "提供有效的联系信息",
                "承诺用于合法的商业或教育目的"
            ]
        }
    },
    "search": {
        "title": "搜索",
        "placeholder": "搜索产品、新闻文章...",
        "searchButton": "搜索",
        "loading": "搜索中...",
        "noResults": "未找到结果",
        "noResultsDescription": "请尝试调整您的搜索词或筛选条件",
        "resultsCount": "找到 {{count}} 个结果",
        "filters": {
            "all": "所有结果",
            "products": "产品",
            "news": "新闻文章"
        },
        "searching": "搜索中...",
        "resultsFound": "找到 {{count}} 个关于\"{{query}}\"的结果",
        "category": "分类",
        "clearSearch": "清除搜索",
        "startSearch": "开始搜索",
        "startSearchDescription": "输入搜索词以查找产品和新闻文章。",
        "viewMode": {
            "grid": "网格视图",
            "list": "列表视图"
        },
        "sortBy": {
            "relevance": "相关性",
            "date": "日期",
            "title": "标题"
        },
        "resultTypes": {
            "product": "产品",
            "news": "新闻文章"
        },
        "actions": {
            "readMore": "阅读更多",
            "viewProduct": "查看产品",
            "viewArticle": "查看文章"
        },
        "suggestions": {
            "title": "搜索建议",
            "popularSearches": "热门搜索",
            "recentSearches": "最近搜索"
        },
        "navigation": {
            "navigate": "导航",
            "select": "选择",
            "close": "关闭"
        },
        "viewAllResults": "查看所有结果"
    }
}
}
