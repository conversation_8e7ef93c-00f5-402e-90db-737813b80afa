import { staticTranslations } from './static-translations'

export type Language = 'en' | 'zh'

// 简单的翻译获取函数，用于服务器组件
// 在静态导出时，默认使用英文，避免使用cookies
export async function getTranslations(defaultLang: Language = 'en') {
  // 在静态导出模式下，不使用cookies，直接使用默认语言
  const lang: Language = defaultLang

  const translations = staticTranslations[lang]

  // 返回一个 t 函数，用于获取翻译
  return function t(key: string): string {
    const keys = key.split('.')
    let result: any = translations

    for (const k of keys) {
      result = result?.[k]
      if (result === undefined) {
        // 如果找不到，返回键的最后一部分
        return keys[keys.length - 1]
      }
    }

    return String(result)
  }
}