{"hero": {"title": "Industrial Automation", "subtitle": "Build intelligent industrial systems and automation solutions. From process control to predictive maintenance, create powerful industrial applications that improve efficiency, quality, and safety.", "startBuilding": "Start Building", "viewDocumentation": "View Documentation"}, "keyCapabilities": {"title": "Key Capabilities", "subtitle": "Everything you need to build advanced industrial automation solutions", "features": {"processControl": {"title": "Process Control", "description": "Real-time control systems for manufacturing processes and industrial equipment"}, "qualityInspection": {"title": "Quality Inspection", "description": "Automated quality control with computer vision and machine learning"}, "predictiveMaintenance": {"title": "Predictive Maintenance", "description": "Monitor equipment health and predict maintenance needs before failures occur"}, "dataAcquisition": {"title": "Data Acquisition", "description": "Collect, process, and analyze industrial sensor data in real-time"}}}, "featuredProjects": {"title": "Featured Projects", "subtitle": "Real-world industrial automation projects you can build today", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "projects": {"smartFactoryControlSystem": {"title": "Smart Factory Control System", "description": "Complete factory automation with real-time monitoring, control, and optimization", "duration": "6-8 weeks", "components": "LinnuoSigma, Industrial I/O, HMI Display, Network Infrastructure", "features": "Real-time Control, SCADA Integration, Remote Monitoring, Data Analytics"}, "qualityInspectionStation": {"title": "Quality Inspection Station", "description": "Automated product quality inspection using AI-powered computer vision", "duration": "3-4 weeks", "components": "LinnuoDelta 3, Industrial Cameras, Lighting System, Conveyor Interface", "features": "Defect Detection, Quality Scoring, Real-time Processing, Data Analytics"}, "predictiveMaintenanceSystem": {"title": "Predictive Maintenance System", "description": "Industrial IoT solution that uses machine learning to predict equipment failures", "duration": "4-5 weeks", "components": "Lin<PERSON>oSigma, Sensor Kit, Data Logger, Edge Computing Unit", "features": "Anomaly Detection, Predictive Analytics, Real-time Monitoring, Maintenance Scheduling"}}, "viewProjectGuide": "View Project Guide"}, "tutorialsResources": {"title": "Tutorials & Resources", "subtitle": "Learn step-by-step with our comprehensive industrial automation guides", "tutorials": {"industrialIoTBasics": {"title": "Industrial IoT Basics", "description": "Learn the fundamentals of industrial IoT systems and sensor integration", "type": "Documentation Guide", "duration": "1 hour"}, "buildingControlSystem": {"title": "Building a Control System", "description": "Create a real-time monitoring and control system for industrial processes", "type": "Video Tutorial", "duration": "1.5 hours"}, "implementingPredictiveMaintenance": {"title": "Implementing Predictive Maintenance", "description": "Deploy machine learning-powered predictive maintenance solutions", "type": "Video Tutorial", "duration": "2 hours"}}, "watchTutorial": "Watch Tutorial", "downloadGuide": "Download Guide"}, "callToAction": {"title": "Ready to Build Your Industrial Solution?", "subtitle": "Get started with Linnuoand bring intelligent automation and control to your industrial applications", "shopLinnuo": "Shop Linnuo", "joinCommunity": "Join Community"}, "common": {"components": "Components", "features": "Features", "duration": "Duration", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}