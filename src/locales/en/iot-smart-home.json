{"hero": {"title": "IoT & Smart Home", "subtitle": "Transform your home into an intelligent, connected ecosystem. Build automation systems, monitoring solutions, and smart devices that make life easier and more efficient.", "startBuilding": "Start Building", "viewDocumentation": "View Documentation"}, "keyCapabilities": {"title": "Key Capabilities", "subtitle": "Everything you need to build comprehensive IoT and smart home solutions", "features": {"environmentalMonitoring": {"title": "Environmental Monitoring", "description": "Monitor temperature, humidity, air quality, and other environmental parameters in real-time"}, "securitySystems": {"title": "Security Systems", "description": "Create comprehensive security solutions with cameras, sensors, and automated alerts"}, "smartLighting": {"title": "Smart Lighting", "description": "Automated lighting control with scheduling, motion detection, and energy optimization"}, "connectivityHub": {"title": "Connectivity Hub", "description": "Central hub for connecting and managing all your IoT devices and sensors"}}}, "featuredProjects": {"title": "Featured Projects", "subtitle": "Real-world IoT and smart home projects you can build today", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "projects": {"smartHomeCentralHub": {"title": "Smart Home Central Hub", "description": "Complete home automation system with voice control, mobile app, and AI-powered scheduling", "duration": "2-3 weeks", "components": "<PERSON><PERSON><PERSON>Sigma, Voice Recognition Module, Various Sensors, Mobile App", "features": "Voice Control, Mobile App, AI Scheduling, Energy Monitoring"}, "environmentalMonitoringStation": {"title": "Environmental Monitoring Station", "description": "Comprehensive system for monitoring air quality, temperature, humidity, and noise levels", "duration": "1-2 weeks", "components": "LinnuoDelta 3, Environmental Sensors, Display Screen, Data Logger", "features": "Real-time Monitoring, Data Logging, Alert System, Remote Access"}, "smartSecuritySystem": {"title": "Smart Security System", "description": "AI-powered security solution with facial recognition, motion detection, and automated alerts", "duration": "2-3 weeks", "components": "LinnuoSigma, IP Cameras, Motion Sensors, Alert System", "features": "Facial Recognition, Motion Detection, Automated Alerts, Remote Monitoring"}}, "viewProjectGuide": "View Project Guide"}, "tutorialsResources": {"title": "Tutorials & Resources", "subtitle": "Learn step-by-step with our comprehensive guides and video tutorials", "tutorials": {"gettingStartedIoT": {"title": "Getting Started with IoT", "description": "Learn the fundamentals of IoT development and best practices", "type": "Documentation Guide", "duration": "30 minutes"}, "buildingSmartThermostat": {"title": "Building a Smart Thermostat", "description": "Create a smart thermostat that can be controlled remotely", "type": "Video Tutorial", "duration": "45 minutes"}, "advancedHomeSecuritySetup": {"title": "Advanced Home Security Setup", "description": "Implement a complete security system with cameras and sensors", "type": "Video Tutorial", "duration": "1.5 hours"}}, "watchTutorial": "Watch Tutorial", "downloadGuide": "Download Guide"}, "callToAction": {"title": "Ready to Build Your Smart Home?", "subtitle": "Get started with <PERSON><PERSON><PERSON><PERSON> transform your home into an intelligent, connected space", "shopLinnuo": "Shop Linnuo", "joinCommunity": "Join Community"}, "common": {"components": "Components", "features": "Features", "duration": "Duration", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}