{"hero": {"title": "AI & Machine Learning", "subtitle": "Build intelligent AI applications at the edge. Harness the power of machine learning, computer vision, and natural language processing to create smart systems that learn and adapt in real-time.", "startBuilding": "Start Building", "viewDocumentation": "View Documentation"}, "keyCapabilities": {"title": "Key Capabilities", "subtitle": "Everything you need to build advanced AI and machine learning solutions", "features": {"computerVision": {"title": "Computer Vision", "description": "Object detection, facial recognition, and image classification at the edge"}, "naturalLanguageProcessing": {"title": "Natural Language Processing", "description": "Voice recognition, text analysis, and conversational AI applications"}, "predictiveAnalytics": {"title": "Predictive Analytics", "description": "Real-time data analysis and machine learning inference for decision making"}, "edgeAIInference": {"title": "Edge AI Inference", "description": "Run AI models locally for low-latency, privacy-focused applications"}}}, "featuredProjects": {"title": "Featured Projects", "subtitle": "Real-world AI and machine learning projects you can build today", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "projects": {"aiPoweredSecuritySystem": {"title": "AI-Powered Security System", "description": "Intelligent surveillance with facial recognition, behavior analysis, and automated threat detection", "duration": "3-4 weeks", "components": "LinnuoSigma, AI Accelerator, IP Cameras, Edge TPU", "features": "Facial Recognition, Behavior Analysis, Real-time Alerts, Privacy Protection"}, "smartVoiceAssistant": {"title": "Smart Voice Assistant", "description": "AI assistant with natural language understanding and smart home control", "duration": "2-3 weeks", "components": "LinnuoDelta 3, Microphone Array, Speakers, AI Chip", "features": "Voice Recognition, Natural Language Understanding, Smart Home Control, Offline Processing"}, "predictiveMaintenanceSystem": {"title": "Predictive Maintenance System", "description": "Industrial AI solution that uses machine learning to predict equipment failures", "duration": "4-5 weeks", "components": "LinnuoSigma, Sensor Kit, Data Logger, ML Accelerator", "features": "Anomaly Detection, Predictive Analytics, Real-time Monitoring, Maintenance Scheduling"}}, "viewProjectGuide": "View Project Guide"}, "tutorialsResources": {"title": "Tutorials & Resources", "subtitle": "Learn step-by-step with our comprehensive AI and machine learning guides", "tutorials": {"gettingStartedAI": {"title": "Getting Started with AI Development", "description": "Learn the fundamentals of AI development on Linnuo", "type": "Documentation Guide", "duration": "45 minutes"}, "buildingComputerVisionApp": {"title": "Building a Computer Vision App", "description": "Create a real-time object detection and recognition system", "type": "Video Tutorial", "duration": "1 hour"}, "deployingMLModels": {"title": "Deploying Machine Learning Models", "description": "Learn how to optimize and deploy ML models on edge devices", "type": "Video Tutorial", "duration": "1.5 hours"}}, "watchTutorial": "Watch Tutorial", "downloadGuide": "Download Guide"}, "callToAction": {"title": "Ready to Build Your AI Project?", "subtitle": "Get started with <PERSON><PERSON><PERSON><PERSON> bring the power of artificial intelligence to your applications", "shopLinnuo": "Shop Linnuo", "joinCommunity": "Join Community"}, "common": {"components": "Components", "features": "Features", "duration": "Duration", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}