'use client'

import { createContext, useContext, useState, use<PERSON><PERSON>back, ReactNode } from 'react'
import { getProducts, getNewsArticles } from '@/data/unified-data-fetcher'
import { useLanguage } from '@/contexts/simple-language-context'

export interface SearchResult {
  id: string
  title: string
  description: string
  url: string
  type: 'product' | 'news'
  category?: string
  image?: string
  highlights?: string[]
}

export interface SearchFilters {
  type: 'all' | 'product' | 'news'
  category?: string
}

interface SearchContextType {
  isSearchOpen: boolean
  searchQuery: string
  searchResults: SearchResult[]
  searchFilters: SearchFilters
  isSearching: boolean
  openSearch: () => void
  closeSearch: () => void
  setSearchQuery: (query: string) => void
  setSearchFilters: (filters: SearchFilters) => void
  performSearch: (query: string, filters?: SearchFilters) => Promise<SearchResult[]>
}

const SearchContext = createContext<SearchContextType | null>(null)

interface SearchProviderProps {
  children: ReactNode
}

export function SearchProvider({ children }: SearchProviderProps) {
  const { language } = useLanguage()
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({ type: 'all' })
  const [isSearching, setIsSearching] = useState(false)

  const openSearch = useCallback(() => {
    setIsSearchOpen(true)
  }, [])

  const closeSearch = useCallback(() => {
    setIsSearchOpen(false)
    setSearchQuery('')
    setSearchResults([])
  }, [])

  const highlightText = (text: string, query: string): string[] => {
    if (!query.trim()) return []
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    const matches = text.match(regex)
    return matches ? Array.from(new Set(matches.map(m => m.toLowerCase()))) : []
  }

  const searchProducts = useCallback(async (query: string, language: 'zh' | 'en' = 'zh'): Promise<SearchResult[]> => {
    if (!query.trim()) return []

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 开始搜索产品，查询词:', query, '语言:', language)
      }
      const products = await getProducts(language)
      if (process.env.NODE_ENV === 'development') {
        console.log('📦 获取到产品数据:', products.length, '个产品')
      }
      
      const filteredProducts = products
        .filter(product => {
          const searchText = `${product.name} ${product.description} ${product.shortDescription} ${product.features?.join(' ') || ''}`.toLowerCase()
          const matches = searchText.includes(query.toLowerCase())
          console.log('🔍 产品匹配:', product.name, '匹配:', matches)
          return matches
        })
        .map(product => ({
          id: `product-${product.id}`,
          title: product.name,
          description: product.shortDescription || product.description,
          url: `/products/${product.slug}`,
          type: 'product' as const,
          category: product.category,
          image: product.images?.[0]?.url,
          highlights: highlightText(`${product.name} ${product.description}`, query)
        }))
      
      console.log('✅ 产品搜索结果:', filteredProducts.length, '个匹配项')
      console.log('🆔 产品ID列表:', filteredProducts.map(p => p.id))
      return filteredProducts
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ 搜索产品失败:', error)
      }
      return []
    }
  }, [])

  const searchNewsArticles = useCallback(async (query: string, language: 'zh' | 'en' = 'zh'): Promise<SearchResult[]> => {
    if (!query.trim()) return []

    try {
      console.log('🗞️ 开始搜索新闻，查询词:', query, '语言:', language)
      const newsArticles = await getNewsArticles(language)
      console.log('📰 获取到新闻数据:', newsArticles.length, '篇文章')
      
      const filteredNews = newsArticles
        .filter(article => {
          const searchText = `${article.title} ${article.excerpt} ${article.content || ''}`.toLowerCase()
          const matches = searchText.includes(query.toLowerCase())
          console.log('🔍 新闻匹配:', article.title, '匹配:', matches)
          return matches
        })
        .map(article => ({
          id: `news-${article.id}`,
          title: article.title,
          description: article.excerpt,
          url: `/news/${article.slug}`,
          type: 'news' as const,
          category: article.category,
          image: article.image || '/images/background/prod_bg.jpg',
          highlights: highlightText(`${article.title} ${article.excerpt}`, query)
        }))
      
      console.log('✅ 新闻搜索结果:', filteredNews.length, '个匹配项')
      console.log('🆔 新闻ID列表:', filteredNews.map(n => n.id))
      return filteredNews
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ 搜索新闻失败:', error)
      }
      return []
    }
  }, [])



  const performSearch = useCallback(async (query: string, filters: SearchFilters = searchFilters): Promise<SearchResult[]> => {
    console.log('🔍 开始执行搜索，查询词:', query, '筛选器:', filters, '语言:', language)
    setIsSearching(true)
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300))

      let results: SearchResult[] = []

      if (filters.type === 'all' || filters.type === 'product') {
        console.log('🔍 搜索产品...')
        const productResults = await searchProducts(query, language)
        results.push(...productResults)
        console.log('📦 产品搜索完成，找到:', productResults.length, '个结果')
      }

      if (filters.type === 'all' || filters.type === 'news') {
        console.log('🔍 搜索新闻...')
        const newsResults = await searchNewsArticles(query, language)
        results.push(...newsResults)
        console.log('📰 新闻搜索完成，找到:', newsResults.length, '个结果')
      }

      // Sort by relevance (simple scoring based on title matches)
      results.sort((a, b) => {
        const aScore = a.title.toLowerCase().includes(query.toLowerCase()) ? 2 : 1
        const bScore = b.title.toLowerCase().includes(query.toLowerCase()) ? 2 : 1
        return bScore - aScore
      })

      console.log('✅ 搜索完成，总共找到:', results.length, '个结果')
      console.log('🔍 最终结果ID列表:', results.map(r => r.id))
      console.log('📊 结果统计:', results.reduce((acc, r) => {
        acc[r.type] = (acc[r.type] || 0) + 1
        return acc
      }, {} as Record<string, number>))
      setSearchResults(results)
      return results
    } finally {
      setIsSearching(false)
    }
  }, [searchProducts, searchNewsArticles, searchFilters, language])

  const contextValue: SearchContextType = {
    isSearchOpen,
    searchQuery,
    searchResults,
    searchFilters,
    isSearching,
    openSearch,
    closeSearch,
    setSearchQuery,
    setSearchFilters,
    performSearch
  }

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  )
}

export function useSearch() {
  const context = useContext(SearchContext)
  if (!context) {
    // Return default values if provider is not available (for SSR compatibility)
    return {
      isSearchOpen: false,
      searchQuery: '',
      searchResults: [],
      searchFilters: { type: 'all' as const },
      isSearching: false,
      openSearch: () => {},
      closeSearch: () => {},
      setSearchQuery: () => {},
      setSearchFilters: () => {},
      performSearch: async () => []
    }
  }
  return context
}
