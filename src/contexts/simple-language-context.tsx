"use client"

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react'
import { staticTranslations } from '@/locales/static-translations'

export type Language = 'en' | 'zh'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => Promise<void>
  t: (key: string, params?: Record<string, string | number>) => string
  isLoading: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// 获取嵌套的翻译值
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// 替换参数的函数
function replaceParams(text: string, params?: Record<string, string | number>): string {
  if (!params) return text
  
  return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return params[key]?.toString() || match
  })
}

export function SimpleLanguageProvider({ children }: { children: React.ReactNode }) {
  // 修复SSR水合错误：客户端立即同步localStorage状态
  const [language, setLanguageState] = useState<Language>(() => {
    // 在客户端立即从localStorage读取语言设置
    if (typeof window !== 'undefined') {
      try {
        const savedLanguage = localStorage.getItem('language') as Language
        if (savedLanguage && ['en', 'zh'].includes(savedLanguage)) {
          if (process.env.NODE_ENV === 'development') {
            console.log('🌐 初始化时恢复语言设置:', savedLanguage)
          }
          return savedLanguage
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('读取localStorage语言设置失败:', error)
        }
      }
    }
    // 服务端或无法读取localStorage时，默认英文
    return 'en'
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [isHydrated, setIsHydrated] = useState(false)
  const [userHasChangedLanguage, setUserHasChangedLanguage] = useState(false)

  // 客户端水合后的补充逻辑（主要处理首次访问标记）
  useEffect(() => {
    if (typeof window !== 'undefined' && !isHydrated) {
      try {
        const hasVisited = localStorage.getItem('linnuo-has-visited') === 'true'
        
        if (!hasVisited) {
          // 首次访问：确保设置正确的标记
          if (process.env.NODE_ENV === 'development') {
            console.log('🌐 首次访问，设置访问标记')
          }
          localStorage.setItem('language', language)
          localStorage.setItem('linnuo-has-visited', 'true')
        }
        
        if (process.env.NODE_ENV === 'development') {
          console.log('🌐 当前语言状态:', language)
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('语言设置水合失败:', error)
        }
        // 确保localStorage同步
        try {
          localStorage.setItem('language', language)
          localStorage.setItem('linnuo-has-visited', 'true')
        } catch {}
      }
      
      setIsHydrated(true)
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 语言上下文水合完成，当前语言:', language)
      }
    }
  }, [isHydrated, language])

  // 使用 useCallback 优化 setLanguage 函数
  const setLanguage = useCallback(async (lang: Language) => {
    if (lang === language) return

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 用户主动切换语言:', language, '->', lang)
    }
    setIsLoading(true)
    setUserHasChangedLanguage(true) // 标记用户已主动切换语言

    // 立即同步更新 localStorage，避免竞态条件
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang)
      if (process.env.NODE_ENV === 'development') {
        console.log('💾 localStorage 已同步更新为:', lang)
      }
    }

    // 使用 requestAnimationFrame 确保状态更新的顺序
    requestAnimationFrame(() => {
      setLanguageState(lang)
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 语言状态已更新为:', lang)
      }

      // 适当延迟，确保语言切换完成
      setTimeout(() => {
        setIsLoading(false)
      }, 300) // 减少到300ms，因为会立即跳转页面
    })
  }, [language])

  // 使用 useMemo 优化翻译函数，直接使用静态翻译
  const t = useMemo(() => {
    return (key: string, params?: Record<string, string | number>): string => {
      try {
        const translations = staticTranslations[language]
        const value = getNestedValue(translations, key)

        if (value !== undefined) {
          const text = typeof value === 'string' ? value : String(value)
          return replaceParams(text, params)
        }

        // 如果没有找到翻译，返回键名的最后一部分
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Translation missing for key: ${key} in language: ${language}`)
        }
        const keyParts = key.split('.')
        return keyParts[keyParts.length - 1]
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Translation error for key:', key, error)
        }
        const keyParts = key.split('.')
        return keyParts[keyParts.length - 1]
      }
    }
  }, [language]) // 只有当语言改变时才重新创建翻译函数

  // 使用 useMemo 优化 context value，避免不必要的重新渲染
  const contextValue = useMemo(() => ({
    language,
    setLanguage,
    t,
    isLoading
  }), [language, setLanguage, t, isLoading])

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
