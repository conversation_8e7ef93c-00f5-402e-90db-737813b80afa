// Image utility functions for optimization and fallbacks

/**
 * Generate a blur data URL for progressive loading
 */
export function generateBlurDataURL(
  width: number = 10,
  height: number = 10,
  color: string = '#f3f4f6'
): string {
  // Create a simple SVG blur placeholder
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="${color}"/>
    </svg>
  `
  
  const base64 = Buffer.from(svg).toString('base64')
  return `data:image/svg+xml;base64,${base64}`
}

/**
 * Generate a placeholder image URL
 */
export function generatePlaceholderURL(
  width: number,
  height: number,
  text?: string,
  bgColor: string = 'f3f4f6',
  textColor: string = '9ca3af'
): string {
  const displayText = text || `${width}×${height}`
  return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodeURIComponent(displayText)}`
}

/**
 * Get optimized image sizes for responsive images
 */
export function getResponsiveSizes(breakpoints: {
  mobile?: string
  tablet?: string
  desktop?: string
  wide?: string
}): string {
  const {
    mobile = '100vw',
    tablet = '50vw',
    desktop = '33vw',
    wide = '25vw'
  } = breakpoints

  return [
    `(max-width: 640px) ${mobile}`,
    `(max-width: 768px) ${tablet}`,
    `(max-width: 1024px) ${desktop}`,
    wide
  ].join(', ')
}

/**
 * Preload an image
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

/**
 * Check if an image URL is valid
 */
export async function isImageValid(src: string): Promise<boolean> {
  try {
    await preloadImage(src)
    return true
  } catch {
    return false
  }
}

/**
 * Get image dimensions
 */
export function getImageDimensions(src: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight })
    }
    img.onerror = reject
    img.src = src
  })
}

/**
 * Convert image to WebP format (client-side)
 */
export function convertToWebP(
  imageFile: File,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      
      if (ctx) {
        ctx.drawImage(img, 0, 0)
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob)
            } else {
              reject(new Error('Failed to convert image'))
            }
          },
          'image/webp',
          quality
        )
      } else {
        reject(new Error('Canvas context not available'))
      }
    }

    img.onerror = reject
    img.src = URL.createObjectURL(imageFile)
  })
}

/**
 * Compress image file
 */
export function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      canvas.width = width
      canvas.height = height

      if (ctx) {
        ctx.drawImage(img, 0, 0, width, height)
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob)
            } else {
              reject(new Error('Failed to compress image'))
            }
          },
          'image/jpeg',
          quality
        )
      } else {
        reject(new Error('Canvas context not available'))
      }
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Generate srcSet for responsive images
 */
export function generateSrcSet(
  baseUrl: string,
  sizes: number[] = [640, 750, 828, 1080, 1200, 1920]
): string {
  return sizes
    .map(size => {
      // For Next.js optimized images
      if (baseUrl.startsWith('/_next/image')) {
        return `${baseUrl}&w=${size} ${size}w`
      }
      // For external URLs, assume they support width parameter
      const separator = baseUrl.includes('?') ? '&' : '?'
      return `${baseUrl}${separator}w=${size} ${size}w`
    })
    .join(', ')
}

/**
 * Image format detection
 */
export function getImageFormat(src: string): string {
  const extension = src.split('.').pop()?.toLowerCase()
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg'
    case 'png':
      return 'image/png'
    case 'webp':
      return 'image/webp'
    case 'avif':
      return 'image/avif'
    case 'svg':
      return 'image/svg+xml'
    case 'gif':
      return 'image/gif'
    default:
      return 'image/jpeg'
  }
}

/**
 * Check browser support for image formats
 */
export function checkImageFormatSupport(): {
  webp: boolean
  avif: boolean
} {
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1

  return {
    webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0,
    avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
  }
}

/**
 * Get optimal image format based on browser support
 */
export function getOptimalImageFormat(originalFormat: string): string {
  const support = checkImageFormatSupport()
  
  if (support.avif) return 'avif'
  if (support.webp) return 'webp'
  
  return originalFormat
}
