// 语言感知的API调用系统
import { fetchFromStrapi, fetchSingleFromStrapi, StrapiResponse, StrapiSingleResponse } from './api'
import type { Language } from '@/contexts/simple-language-context'

// 语言映射：前端语言代码到Strapi locale代码
export const LOCALE_MAP = {
  'en': 'en',
  'zh': 'zh' // 修改为正确的中文locale代码
} as const

// 将前端语言代码转换为Strapi locale代码
export function getLocaleFromLanguage(language: Language): string {
  return LOCALE_MAP[language]
}

// 语言感知的API选项接口
export interface LocaleAwareApiOptions {
  populate?: string | string[]
  filters?: Record<string, any>
  sort?: string | string[]
  pagination?: {
    page?: number
    pageSize?: number
  }
  language?: Language // 使用前端语言代码
  locale?: string // 直接指定Strapi locale代码
  disableAutoLocale?: boolean // 禁用自动locale
}

export interface LocaleAwareSingleApiOptions {
  populate?: string | string[]
  language?: Language // 使用前端语言代码
  locale?: string // 直接指定Strapi locale代码
  disableAutoLocale?: boolean // 禁用自动locale
}

// 语言感知的批量数据获取函数
export async function fetchFromStrapiWithLocale<T>(
  endpoint: string,
  options?: LocaleAwareApiOptions
): Promise<StrapiResponse<T[]>> {
  // 确定要使用的locale
  let targetLocale: string | undefined

  if (!options?.disableAutoLocale) {
    if (options?.locale) {
      // 直接使用指定的locale
      targetLocale = options.locale
    } else if (options?.language) {
      // 从语言代码转换为locale
      targetLocale = getLocaleFromLanguage(options.language)
    } else {
      // 为了避免水合错误，不从localStorage读取语言设置
      // 使用默认语言，语言切换通过其他方式处理
      targetLocale = LOCALE_MAP.en
      console.log('🌐 使用默认语言避免水合错误: en')
    }
  }

  // 构建API选项 - 只有在有targetLocale时才添加locale参数
  const apiOptions: any = {
    populate: options?.populate,
    filters: options?.filters,
    sort: options?.sort,
    pagination: options?.pagination,
    disableAutoLocale: options?.disableAutoLocale
  }

  // 只有在明确指定了locale时才添加locale参数
  if (targetLocale) {
    apiOptions.locale = targetLocale
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 API调用使用locale:', targetLocale)
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 API调用不指定locale，使用默认语言')
    }
  }

  return fetchFromStrapi<T>(endpoint, apiOptions)
}

// 语言感知的单个数据获取函数
export async function fetchSingleFromStrapiWithLocale<T>(
  endpoint: string,
  options?: LocaleAwareSingleApiOptions
): Promise<StrapiSingleResponse<T>> {
  // 确定要使用的locale
  let targetLocale: string | undefined

  if (!options?.disableAutoLocale) {
    if (options?.locale) {
      // 直接使用指定的locale
      targetLocale = options.locale
    } else if (options?.language) {
      // 从语言代码转换为locale
      targetLocale = getLocaleFromLanguage(options.language)
    } else {
      // 为了避免水合错误，直接使用默认语言
      targetLocale = LOCALE_MAP.en
    }
  }

  // 构建API选项
  const apiOptions = {
    populate: options?.populate,
    locale: targetLocale,
    disableAutoLocale: options?.disableAutoLocale
  }

  return fetchSingleFromStrapi<T>(endpoint, apiOptions)
}

// React Hook版本的API调用函数
export function createLocaleAwareApiHooks() {
  return {
    // 使用当前语言上下文的API调用
    fetchWithCurrentLocale: async <T>(
      endpoint: string,
      language: Language,
      options?: Omit<LocaleAwareApiOptions, 'language'>
    ): Promise<StrapiResponse<T[]>> => {
      return fetchFromStrapiWithLocale<T>(endpoint, {
        ...options,
        language
      })
    },

    // 获取单个项目
    fetchSingleWithCurrentLocale: async <T>(
      endpoint: string,
      language: Language,
      options?: Omit<LocaleAwareSingleApiOptions, 'language'>
    ): Promise<StrapiSingleResponse<T>> => {
      return fetchSingleFromStrapiWithLocale<T>(endpoint, {
        ...options,
        language
      })
    }
  }
}

// 便捷函数：获取指定语言的数据
export async function fetchDataInLanguage<T>(
  endpoint: string,
  language: Language,
  options?: Omit<LocaleAwareApiOptions, 'language'>
): Promise<StrapiResponse<T[]>> {
  return fetchFromStrapiWithLocale<T>(endpoint, {
    ...options,
    language
  })
}

// 便捷函数：获取指定语言的单个数据
export async function fetchSingleDataInLanguage<T>(
  endpoint: string,
  language: Language,
  options?: Omit<LocaleAwareSingleApiOptions, 'language'>
): Promise<StrapiSingleResponse<T>> {
  return fetchSingleFromStrapiWithLocale<T>(endpoint, {
    ...options,
    language
  })
}

// 调试函数：显示当前使用的locale
export function debugCurrentLocale(): void {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    const savedLanguage = localStorage.getItem('language') as Language
    const locale = savedLanguage ? LOCALE_MAP[savedLanguage] : LOCALE_MAP.en
    console.log('🌐 Current Language Debug:', {
      savedLanguage,
      locale,
      availableLocales: LOCALE_MAP
    })
  }
}

// 导出类型
export type { Language }
