import axios from 'axios'
import { getApiConfig } from './api-config'

// 获取API配置
const apiConfig = getApiConfig()

// 创建 axios 实例
export const strapiApi = axios.create({
  baseURL: `${apiConfig.strapiUrl}/api`,
  timeout: apiConfig.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(apiConfig.apiToken && {
      Authorization: `Bearer ${apiConfig.apiToken}`,
    }),
  },
  // 支持跨域请求
  withCredentials: false,
})

// API 响应类型
export interface StrapiResponse<T> {
  data: T
  meta: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export interface StrapiSingleResponse<T> {
  data: T
  meta: {}
}

// 语言映射：前端语言代码到Strapi locale代码
const LOCALE_MAP = {
  'en': 'en',
  'zh': 'zh' // 修改为正确的中文locale代码
} as const

// 获取当前语言的locale代码 - 修复水合错误
function getCurrentLocale(): string {
  // 为了避免水合错误，服务端和客户端都使用相同的默认值
  // 在客户端，语言切换会通过其他方式处理
  return LOCALE_MAP.en // 统一默认为英文，避免水合错误
}

// 通用 API 函数
export async function fetchFromStrapi<T>(
  endpoint: string,
  options?: {
    populate?: string | string[]
    filters?: Record<string, any>
    sort?: string | string[]
    pagination?: {
      page?: number
      pageSize?: number
    }
    locale?: string // 新增locale选项
    disableAutoLocale?: boolean // 禁用自动locale
  }
): Promise<StrapiResponse<T[]>> {
  // 检查是否启用了Strapi
  const config = getApiConfig()
  if (!config.enableStrapi) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Strapi is disabled, returning empty data')
    }
    return {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 0,
          pageCount: 0,
          total: 0
        }
      }
    }
  }

  const params = new URLSearchParams()

  // 自动添加locale参数（除非明确禁用）
  if (!options?.disableAutoLocale) {
    const locale = options?.locale || getCurrentLocale()
    params.append('locale', locale)
  }

  if (options?.populate) {
    if (Array.isArray(options.populate)) {
      options.populate.forEach(field => params.append('populate', field))
    } else {
      params.append('populate', options.populate)
    }
  }

  if (options?.filters) {
    Object.entries(options.filters).forEach(([key, value]) => {
      // 对于简单的字符串值，使用 $eq 操作符
      if (typeof value === 'string' || typeof value === 'number') {
        params.append(`filters[${key}][$eq]`, value.toString())
      } else if (typeof value === 'object' && value !== null) {
        // 对于复杂的过滤器对象，递归处理
        Object.entries(value).forEach(([operator, operatorValue]) => {
          if (typeof operatorValue === 'string' || typeof operatorValue === 'number') {
            params.append(`filters[${key}][${operator}]`, operatorValue.toString())
          } else {
            console.warn(`Unsupported filter value type for ${key}.${operator}:`, operatorValue)
          }
        })
      } else {
        console.warn(`Unsupported filter value type for ${key}:`, value)
      }
    })
  }
  
  if (options?.sort) {
    if (Array.isArray(options.sort)) {
      options.sort.forEach(field => params.append('sort', field))
    } else {
      params.append('sort', options.sort)
    }
  }
  
  if (options?.pagination) {
    if (options.pagination.page) {
      params.append('pagination[page]', options.pagination.page.toString())
    }
    if (options.pagination.pageSize) {
      params.append('pagination[pageSize]', options.pagination.pageSize.toString())
    }
  }
  
  const finalUrl = `${endpoint}?${params.toString()}`
  if (process.env.NODE_ENV === 'development') {
    console.log('🌐 构建的 Strapi API URL:', finalUrl)
    console.log('📋 查询参数详情:', Object.fromEntries(params.entries()))
  }
  
  try {
    const response = await strapiApi.get(finalUrl, { next: { revalidate: 3600 } } as any)
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Strapi API 请求成功，状态:', response.status)
      console.log('📦 响应数据概览:', {
        dataCount: response.data?.data?.length || 0,
        hasData: !!response.data?.data,
        meta: response.data?.meta
      })
    }
    return response.data
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error(`Failed to fetch from Strapi endpoint ${endpoint}:`, error)
    }
    // Return empty data structure for build-time resilience
    return {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 0,
          pageCount: 0,
          total: 0
        }
      }
    }
  }
}

export async function fetchSingleFromStrapi<T>(
  endpoint: string,
  options?: {
    populate?: string | string[]
    locale?: string // 新增locale选项
    disableAutoLocale?: boolean // 禁用自动locale
  }
): Promise<StrapiSingleResponse<T>> {
  // 检查是否启用了Strapi
  const config = getApiConfig()
  if (!config.enableStrapi) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Strapi is disabled, throwing error for single fetch')
    }
    throw new Error('Strapi is disabled')
  }

  const params = new URLSearchParams()

  // 自动添加locale参数（除非明确禁用）
  if (!options?.disableAutoLocale) {
    const locale = options?.locale || getCurrentLocale()
    params.append('locale', locale)
  }

  if (options?.populate) {
    if (Array.isArray(options.populate)) {
      options.populate.forEach(field => params.append('populate', field))
    } else {
      params.append('populate', options.populate)
    }
  }

  try {
    const response = await strapiApi.get(`${endpoint}?${params.toString()}`, { next: { revalidate: 3600 } } as any)
    return response.data
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error(`Failed to fetch single from Strapi endpoint ${endpoint}:`, error)
    }
    // Return empty data structure for build-time resilience
    throw error // Re-throw for single items as they should be handled by the caller
  }
}

// 样品申请数据类型
export interface SampleRequestData {
  name: string
  company: string
  email: string
  phone: string
  sampleName: string
  quantity: number
  purpose: string
  requirements: string
  requiredDate: string
  address: string
  urgency: 'normal' | 'urgent' | 'very_urgent'
}

// 样品申请 API 函数
export async function submitSampleRequest(requestData: SampleRequestData) {
  // 检查是否启用了Strapi
  const config = getApiConfig()
  if (!config.enableStrapi) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Strapi is disabled, sample request submission not available')
    }
    return {
      success: false,
      error: {
        message: 'Sample request submission is not available in static mode'
      }
    }
  }

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 提交样品申请到Strapi:', requestData)
    }
    const response = await strapiApi.post('/sample-applications', {
      data: requestData
    })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 样品申请提交成功:', response.data)
    }
    return { success: true, data: response.data }
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 样品申请提交失败:', error)
    }
    console.error('错误详情:', error.response?.data)
    return {
      success: false,
      error: error.response?.data?.error || {
        message: 'Failed to submit request'
      }
    }
  }
}

// 获取样品申请列表
export async function getSampleRequests() {
  // 检查是否启用了Strapi
  const config = getApiConfig()
  if (!config.enableStrapi) {
    console.warn('Strapi is disabled, returning empty sample requests')
    return {
      success: true,
      data: { data: [], meta: {} }
    }
  }

  try {
    const response = await strapiApi.get('/sample-requests')
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error('Failed to fetch sample requests:', error)
    return {
      success: false,
      error: error.response?.data?.error || {
        message: 'Failed to fetch requests'
      }
    }
  }
}

// 下载文件相关类型和函数
export interface StrapiDownloadFile {
  id: number
  documentId: string
  title: string
  description: string
  category: string
  version?: string | null
  published_date?: string | null
  slug: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  file?: Array<{
    id: number
    documentId: string
    name: string
    alternativeText?: string | null
    caption?: string | null
    width?: number | null
    height?: number | null
    formats?: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl?: string | null
    provider: string
    provider_metadata?: any
    createdAt: string
    updatedAt: string
    publishedAt: string
  }>
}

export interface DownloadFile {
  id: string
  name: string
  description: string
  version: string
  size: string
  type: 'driver' | 'documentation' | 'specification' | 'firmware' | 'software' | 'image'
  category: string
  product: string
  downloadUrl: string
  releaseDate: string
  compatibility: string[]
  featured?: boolean
}

// 获取下载文件列表
export async function getDownloadFiles(language?: string): Promise<DownloadFile[]> {
  // 检查是否启用了Strapi
  const config = getApiConfig()
  if (!config.enableStrapi) {
    console.warn('Strapi is disabled, returning empty download files')
    return []
  }

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 获取下载文件列表，语言:', language)
    }

    const { fetchFromStrapiWithLocale } = await import('@/lib/api-with-locale')
    const response = await fetchFromStrapiWithLocale<StrapiDownloadFile>('/downloads', {
      populate: '*',
      language: language as any
    })

    console.log('📦 Strapi下载文件响应:', response)
    console.log('📊 获取到的下载文件数量:', response.data?.length || 0)

    if (!response.data || response.data.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ 未找到下载文件')
      }
      return []
    }

    // 转换Strapi数据为前端格式
    const downloadFiles = response.data.map(transformStrapiDownloadFile)
    
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 下载文件转换完成:', downloadFiles.length, '个文件')
    }

    return downloadFiles
  } catch (error) {
    console.error('❌ 从Strapi获取下载文件时发生错误:', error)
    return []
  }
}

// 转换Strapi下载文件数据为前端格式
function transformStrapiDownloadFile(strapiFile: StrapiDownloadFile): DownloadFile {
  // 获取文件信息
  const file = strapiFile.file?.[0]
  const fileSize = file ? `${(file.size / 1024).toFixed(1)} MB` : 'Unknown'
  const downloadUrl = file?.url || '#'

  // 根据category映射type
  const typeMapping: Record<string, DownloadFile['type']> = {
    'driver': 'driver',
    'drivers': 'driver',
    'docs': 'documentation',
    'documentation': 'documentation',
    'manual': 'documentation',
    'firmware': 'firmware',
    'software': 'software',
    'spec': 'specification',
    'specification': 'specification',
    'image': 'image'
  }

  const type = typeMapping[strapiFile.category.toLowerCase()] || 'documentation'

  // 从文件名或描述推断产品
  const product = extractProductFromTitle(strapiFile.title) || 'General'

  // 从文件类型推断兼容性
  const compatibility = inferCompatibility(file?.ext || '', file?.mime || '')

  return {
    id: strapiFile.documentId,
    name: strapiFile.title,
    description: strapiFile.description,
    version: strapiFile.version || '1.0.0',
    size: fileSize,
    type: type,
    category: capitalizeFirst(strapiFile.category),
    product: product,
    downloadUrl: downloadUrl,
    releaseDate: strapiFile.published_date || strapiFile.publishedAt.split('T')[0],
    compatibility: compatibility,
    featured: false // 可以根据需要添加featured字段到Strapi
  }
}

// 辅助函数：从标题中提取产品名称
function extractProductFromTitle(title: string): string {
  const productPatterns = ['E200', 'E500', 'LinnuoSigma', 'LinnuoDelta', 'LinnuoMu', 'LinnuoAlpha', 'LinnuoV1']

  for (const pattern of productPatterns) {
    if (title.toLowerCase().includes(pattern.toLowerCase())) {
      return pattern
    }
  }

  return 'General'
}

// 辅助函数：根据文件类型推断兼容性
function inferCompatibility(ext: string, mime: string): string[] {
  if (ext === '.exe' || mime.includes('msdownload')) {
    return ['Windows 11', 'Windows 10']
  } else if (ext === '.deb' || ext === '.rpm') {
    return ['Ubuntu 22.04', 'Linux']
  } else if (ext === '.dmg') {
    return ['macOS']
  } else if (ext === '.pdf' || mime.includes('pdf')) {
    return ['All Platforms']
  } else {
    return ['Windows 11', 'Windows 10', 'Linux', 'macOS']
  }
}

// 辅助函数：首字母大写
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

// 错误处理
strapiApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Strapi API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 通用 API 错误处理 - 改进错误处理和重试逻辑
export function handleApiError(error: any, context: string): never {
  console.error(`API Error in ${context}:`, error)

  if (error.response) {
    // 服务器响应了错误状态码
    const status = error.response.status
    const message = error.response.data?.error?.message || error.response.statusText

    switch (status) {
      case 404:
        throw new Error(`Resource not found: ${message}`)
      case 403:
        throw new Error(`Access forbidden: ${message}`)
      case 429:
        throw new Error(`Rate limit exceeded: ${message}`)
      case 500:
        throw new Error(`Server error: ${message}`)
      case 502:
      case 503:
      case 504:
        throw new Error(`Service unavailable: ${message}`)
      default:
        throw new Error(`API error (${status}): ${message}`)
    }
  } else if (error.request) {
    // 请求发出但没有收到响应
    if (error.code === 'ECONNABORTED') {
      throw new Error(`Request timeout: The server took too long to respond`)
    } else if (error.code === 'ENOTFOUND') {
      throw new Error(`Network error: Unable to resolve server address`)
    } else {
      throw new Error(`Network error: Unable to reach the server`)
    }
  } else {
    // 其他错误
    throw new Error(`Request error: ${error.message}`)
  }
}

// API重试机制
export async function apiWithRetry<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall()
    } catch (error: any) {
      lastError = error

      // 如果是客户端错误（4xx），不重试
      if (error.response?.status >= 400 && error.response?.status < 500) {
        throw error
      }

      // 如果是最后一次尝试，抛出错误
      if (attempt === maxRetries) {
        throw error
      }

      // 等待后重试
      console.warn(`API call failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`)
      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= 2 // 指数退避
    }
  }

  throw lastError
}
