import { Metadata } from 'next'
import { Product } from '@/types/product'
import { NewsArticle } from '@/lib/api/news'

// 基础SEO配置
export const seoConfig = {
  siteName: 'Linnuo',
  siteUrl: 'https://Linnuo-clone.vercel.app',
  defaultTitle: 'Linnuo - x86 Windows/Linux Single Board Computers',
  defaultDescription: 'Linnuo is a complete Windows/Linux device in a single board computer that can run almost any x86 software. Perfect for IoT, robotics, and embedded applications.',
  defaultImage: '/images/og-image.jpg',
  twitterHandle: '@LinnuoCN',
  locale: 'en_US',
  alternateLocales: ['zh_CN'],
}

// 生成页面标题
export function generatePageTitle(title: string, includeDefault = true): string {
  if (!includeDefault) return title
  return title === seoConfig.defaultTitle ? title : `${title} | ${seoConfig.siteName}`
}

// 生成页面描述
export function generatePageDescription(description: string, maxLength = 160): string {
  if (description.length <= maxLength) return description
  return description.substring(0, maxLength - 3).trim() + '...'
}

// 生成产品页面SEO
export function generateProductSEO(product: Product): Metadata {
  const title = generatePageTitle(product.name)
  const description = generatePageDescription(product.description || product.shortDescription || '')
  const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]
  const imageUrl = mainImage?.url || seoConfig.defaultImage

  return {
    title,
    description,
    keywords: [
      product.name,
      product.category,
      'single board computer',
      'SBC',
      'x86',
      'embedded',
      'IoT',
      'Linnuo'
    ],
    openGraph: {
      title,
      description,
      type: 'website',
      url: `${seoConfig.siteUrl}/products/${product.slug}`,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
      siteName: seoConfig.siteName,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: seoConfig.twitterHandle,
    },
    alternates: {
      canonical: `/products/${product.slug}`,
      languages: {
        'en': `/en/products/${product.slug}`,
        'zh': `/zh/products/${product.slug}`,
      },
    },
  }
}

// 生成新闻文章SEO
export function generateNewsSEO(article: NewsArticle): Metadata {
  const title = generatePageTitle(article.title)
  const description = generatePageDescription(article.excerpt || '')
  const imageUrl = article.image || seoConfig.defaultImage

  return {
    title,
    description,
    keywords: [
      ...article.tags,
      article.category,
      'Linnuo',
      'news',
      'single board computer'
    ],
    authors: [{ name: article.author }],
    openGraph: {
      title,
      description,
      type: 'article',
      url: `${seoConfig.siteUrl}/news/${article.slug}`,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: article.title,
        },
      ],
      siteName: seoConfig.siteName,
      publishedTime: article.date,
      modifiedTime: article.date,
      authors: [article.author],
      section: article.category,
      tags: article.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: seoConfig.twitterHandle,
    },
    alternates: {
      canonical: `/news/${article.slug}`,
    },
  }
}

// 生成分类页面SEO
export function generateCategorySEO(category: string, type: 'products' | 'news'): Metadata {
  const categoryName = category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')
  const title = generatePageTitle(`${categoryName} ${type === 'products' ? 'Products' : 'News'}`)
  const description = generatePageDescription(
    `Explore ${categoryName} ${type} from Linnuo. ${type === 'products' 
      ? 'High-performance single board computers for various applications.' 
      : 'Latest news and updates about Linnuo products and technology.'}`
  )

  return {
    title,
    description,
    keywords: [
      categoryName,
      type,
      'Linnuo',
      'single board computer',
      'SBC',
      'embedded'
    ],
    openGraph: {
      title,
      description,
      type: 'website',
      url: `${seoConfig.siteUrl}/${type}?category=${category}`,
      images: [
        {
          url: seoConfig.defaultImage,
          width: 1200,
          height: 630,
          alt: `${categoryName} ${type}`,
        },
      ],
      siteName: seoConfig.siteName,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [seoConfig.defaultImage],
      creator: seoConfig.twitterHandle,
    },
    alternates: {
      canonical: `/${type}?category=${category}`,
    },
  }
}

// 生成面包屑导航结构化数据
export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `${seoConfig.siteUrl}${item.url}`
    }))
  }
}

// 生成FAQ结构化数据
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}
