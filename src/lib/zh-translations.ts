// 纯中文网站 - 无需翻译系统，直接使用中文字符串
export const ZH_TEXTS = {
  // 导航
  home: '首页',
  products: '产品',
  about: '关于我们',
  news: '新闻',
  contact: '联系我们',
  application: '应用案例',
  support: '技术支持',
  downloads: '下载中心',
  search: '搜索',
  
  // 产品分类
  'categories.scalableEmbeddedSeries': '可扩展嵌入式系列',
  'categories.miniSizeSeries': '迷你尺寸系列',
  'categories.universalEmbeddedSeries': '通用嵌入式系列',
  'categories.allInOneIpc': '一体化工控机',
  
  // 应用案例
  'iotSmartHome': '物联网与智能家居',
  'aiMachineLearning': 'AI与机器学习',
  'industrialAutomation': '工业自动化',
  'educationResearch': '教育与研究',
  'gamingEntertainment': '游戏娱乐',
  'automotiveTransportation': '汽车交通',
  
  // 销售与支持
  'salesAndSupport': '销售与支持',
  'sampleApplication': '样品申请',
  'afterSalesService': '售后服务',
  'customizedService': '定制服务',
  
  // 通用
  'viewAll': '查看全部',
  'aboutUs': '关于我们',
  'allRightsReserved': '版权所有',
  'company': '公司',
  
  // 搜索
  'filters.all': '全部',
  'filters.products': '产品',
  'filters.news': '新闻',
  'placeholder': '搜索产品、新闻...',
  'startSearch': '开始搜索',
  'startSearchDescription': '输入关键词搜索产品和新闻',
  
  // 导航
  'navigation.navigate': '导航',
  'navigation.select': '选择',
  'navigation.close': '关闭',
  
  // 首页
  'title': 'LattePanda - 单板计算机领导者',
  'description': '专业的单板计算机解决方案',
  
  // 表单
  'form.name': '姓名',
  'form.company': '公司',
  'form.email': '邮箱',
  'form.submit': '提交',
  'form.placeholders.name': '请输入姓名',
  'form.placeholders.company': '请输入公司名称',
  'form.placeholders.email': '请输入邮箱地址',
}

// 简单的获取函数，直接返回中文文本
export function getZhText(key: string): string {
  return ZH_TEXTS[key as keyof typeof ZH_TEXTS] || key
}

// 兼容性函数
export function t(key: string): string {
  return getZhText(key)
}

export const useTranslations = () => ({ t })
export const useHeroTranslations = () => ({ t })
export const useNavigationTranslations = () => ({ t })
export const useProductTranslations = () => ({ t })
export const useProductsTranslations = () => ({ t })
export const useNewsTranslations = () => ({ t })
export const useFooterTranslations = () => ({ t })
export const useCommonTranslations = () => ({ t })
export const useContactTranslations = () => ({ t })
export const useAboutTranslations = () => ({ t })
export const useApplicationTranslations = () => ({ t })
export const useAfterSalesTranslations = () => ({ t })
export const useSampleApplicationTranslations = () => ({ t })
export const useCustomizedServiceTranslations = () => ({ t })
export const useDownloadsTranslations = () => ({ t })
export const useSearchTranslations = () => ({ t })
export const useIoTSmartHomeTranslations = () => ({ t })
export const useAIMachineLearningTranslations = () => ({ t })
export const useIndustrialAutomationTranslations = () => ({ t })
export const useEducationResearchTranslations = () => ({ t })
export const useHomeTranslations = () => ({ t })
