/**
 * 图片优化工具函数
 * 包含预加载、性能监控、格式检测等功能
 */

// 预加载单个图片
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      resolve()
      return
    }

    const img = new window.Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

// 批量预加载图片
export function preloadImages(srcs: string[]): Promise<PromiseSettledResult<void>[]> {
  return Promise.allSettled(srcs.map(preloadImage))
}

// 预加载关键图片（首屏图片）
export function preloadCriticalImages(srcs: string[]): void {
  if (typeof window === 'undefined') return

  srcs.forEach(src => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    document.head.appendChild(link)
  })
}

// 检测浏览器支持的图片格式
export function getSupportedImageFormats(): {
  webp: boolean
  avif: boolean
  heic: boolean
} {
  if (typeof window === 'undefined') {
    return { webp: false, avif: false, heic: false }
  }

  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1

  return {
    webp: canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0,
    avif: canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0,
    heic: 'createImageBitmap' in window
  }
}

// 生成响应式图片的 srcSet
export function generateSrcSet(baseSrc: string, sizes: number[] = [320, 640, 768, 1024, 1280, 1920]): string {
  return sizes
    .map(size => {
      const extension = baseSrc.split('.').pop()
      const nameWithoutExt = baseSrc.replace(`.${extension}`, '')
      return `${nameWithoutExt}-${size}w.${extension} ${size}w`
    })
    .join(', ')
}

// 生成模糊占位符 Data URL
export function generateBlurDataURL(width: number = 10, height: number = 10, color: string = '#f3f4f6'): string {
  if (typeof window === 'undefined') {
    // SSR 时返回 SVG Data URL
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${color}"/>
      </svg>
    `
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`
  }

  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  
  if (ctx) {
    ctx.fillStyle = color
    ctx.fillRect(0, 0, width, height)
  }
  
  return canvas.toDataURL()
}

// 图片性能监控
export class ImagePerformanceMonitor {
  private static instance: ImagePerformanceMonitor
  private metrics: Map<string, {
    startTime: number
    endTime?: number
    duration?: number
    size?: number
  }> = new Map()

  static getInstance(): ImagePerformanceMonitor {
    if (!ImagePerformanceMonitor.instance) {
      ImagePerformanceMonitor.instance = new ImagePerformanceMonitor()
    }
    return ImagePerformanceMonitor.instance
  }

  startTracking(src: string): void {
    this.metrics.set(src, {
      startTime: Date.now()
    })

    if (typeof window !== 'undefined' && 'performance' in window) {
      try {
        performance.mark(`image-start-${src}`)
      } catch (error) {
        // 忽略性能API错误
      }
    }
  }

  endTracking(src: string, size?: number): void {
    const metric = this.metrics.get(src)
    if (!metric) return

    const endTime = Date.now()
    const duration = endTime - metric.startTime

    this.metrics.set(src, {
      ...metric,
      endTime,
      duration,
      size
    })

    if (typeof window !== 'undefined' && 'performance' in window) {
      try {
        performance.mark(`image-end-${src}`)
        performance.measure(`image-load-${src}`, `image-start-${src}`, `image-end-${src}`)
      } catch (error) {
        // 忽略性能API错误
      }
    }

    // 开发环境下输出性能信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`Image loaded: ${src}`, {
        duration: `${duration}ms`,
        size: size ? `${(size / 1024).toFixed(2)}KB` : 'unknown'
      })
    }
  }

  getMetrics(): Array<{
    src: string
    duration?: number
    size?: number
  }> {
    return Array.from(this.metrics.entries()).map(([src, metric]) => ({
      src,
      duration: metric.duration,
      size: metric.size
    }))
  }

  getAverageLoadTime(): number {
    const completedMetrics = Array.from(this.metrics.values()).filter(m => m.duration)
    if (completedMetrics.length === 0) return 0

    const totalTime = completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0)
    return Math.round(totalTime / completedMetrics.length)
  }

  getSlowestImage(): { src: string; duration: number } | null {
    let slowest: { src: string; duration: number } | null = null

    for (const [src, metric] of this.metrics.entries()) {
      if (metric.duration && (!slowest || metric.duration > slowest.duration)) {
        slowest = { src, duration: metric.duration }
      }
    }

    return slowest
  }

  clear(): void {
    this.metrics.clear()
  }
}

// 图片懒加载观察器
export class LazyImageObserver {
  private static instance: LazyImageObserver
  private observer: IntersectionObserver | null = null
  private callbacks: Map<Element, () => void> = new Map()

  static getInstance(): LazyImageObserver {
    if (!LazyImageObserver.instance) {
      LazyImageObserver.instance = new LazyImageObserver()
    }
    return LazyImageObserver.instance
  }

  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const callback = this.callbacks.get(entry.target)
              if (callback) {
                callback()
                this.unobserve(entry.target)
              }
            }
          })
        },
        {
          rootMargin: '100px' // 提前100px开始加载
        }
      )
    }
  }

  observe(element: Element, callback: () => void): void {
    if (!this.observer) return

    this.callbacks.set(element, callback)
    this.observer.observe(element)
  }

  unobserve(element: Element): void {
    if (!this.observer) return

    this.observer.unobserve(element)
    this.callbacks.delete(element)
  }

  disconnect(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.callbacks.clear()
    }
  }
}

// 获取图片的最优格式
export function getOptimalImageFormat(originalSrc: string): string {
  const formats = getSupportedImageFormats()
  const baseSrc = originalSrc.replace(/\.(jpg|jpeg|png|webp|avif)$/i, '')
  
  if (formats.avif) {
    return `${baseSrc}.avif`
  } else if (formats.webp) {
    return `${baseSrc}.webp`
  }
  
  return originalSrc
}

// 图片压缩质量建议
export function getRecommendedQuality(imageType: 'photo' | 'illustration' | 'icon'): number {
  switch (imageType) {
    case 'photo':
      return 85
    case 'illustration':
      return 90
    case 'icon':
      return 95
    default:
      return 85
  }
}
