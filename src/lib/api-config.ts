// API配置管理器 - 支持前后端分离部署

export interface ApiConfig {
  baseUrl: string
  strapiUrl: string
  apiToken?: string
  timeout: number
  enableStrapi: boolean
  deploymentTarget: 'development' | 'cloudflare-pages' | 'vercel' | 'netlify'
}

// 获取部署目标
function getDeploymentTarget(): ApiConfig['deploymentTarget'] {
  if (typeof window === 'undefined') {
    // 服务端检测
    return process.env.NEXT_PUBLIC_DEPLOYMENT_TARGET as ApiConfig['deploymentTarget'] || 'development'
  }
  
  // 客户端检测
  const hostname = window.location.hostname
  if (hostname.includes('.pages.dev') || hostname.includes('cloudflare')) {
    return 'cloudflare-pages'
  }
  if (hostname.includes('.vercel.app') || hostname.includes('.vercel.com')) {
    return 'vercel'
  }
  if (hostname.includes('.netlify.app')) {
    return 'netlify'
  }
  return 'development'
}

// 获取API配置
export function getApiConfig(): ApiConfig {
  const deploymentTarget = getDeploymentTarget()
  const isProduction = process.env.NODE_ENV === 'production'

  // 基础配置
  const config: ApiConfig = {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api/strapi',
    strapiUrl: process.env.NEXT_PUBLIC_STRAPI_URL || '/api/strapi',
    apiToken: process.env.NEXT_PUBLIC_STRAPI_API_TOKEN,
    timeout: isProduction ? 15000 : 10000, // 增加超时时间
    enableStrapi: process.env.NEXT_PUBLIC_ENABLE_STRAPI === 'true',
    deploymentTarget
  }

  // 根据部署目标调整配置
  switch (deploymentTarget) {
    case 'cloudflare-pages':
      // Cloudflare Pages特定配置
      config.timeout = 15000 // 更长的超时时间
      // 在构建时禁用Strapi，运行时通过环境变量启用
      if (typeof window !== 'undefined') {
        config.enableStrapi = true // 客户端运行时启用
      }
      break
      
    case 'vercel':
      // Vercel特定配置
      config.timeout = 20000 // 更长的超时时间
      config.enableStrapi = process.env.NEXT_PUBLIC_ENABLE_STRAPI === 'true'
      break

    case 'development':
      config.enableStrapi = process.env.NEXT_PUBLIC_ENABLE_STRAPI !== 'false'
      break

    default:
      // 其他部署平台的默认配置
      break
  }

  return config
}

// 获取完整的API URL
export function getApiUrl(endpoint: string): string {
  const config = getApiConfig()
  const baseUrl = config.strapiUrl.replace(/\/$/, '') // 移除末尾斜杠
  const cleanEndpoint = endpoint.replace(/^\//, '') // 移除开头斜杠
  
  return `${baseUrl}/api/${cleanEndpoint}`
}

// 检查API是否可用
export async function checkApiHealth(): Promise<boolean> {
  const config = getApiConfig()
  
  if (!config.enableStrapi) {
    return false
  }

  try {
    const response = await fetch(`${config.strapiUrl}/api/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000), // 5秒超时
    })
    
    return response.ok
  } catch (error) {
    console.warn('API health check failed:', error)
    return false
  }
}

// 导出配置实例
export const apiConfig = getApiConfig()

// 日志记录函数
export function logApiConfig() {
  if (process.env.NODE_ENV === 'development') {
    console.log('API Configuration:', {
      deploymentTarget: apiConfig.deploymentTarget,
      enableStrapi: apiConfig.enableStrapi,
      strapiUrl: apiConfig.strapiUrl,
      hasApiToken: !!apiConfig.apiToken,
      timeout: apiConfig.timeout
    })
  }
}
