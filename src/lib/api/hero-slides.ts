import { fetchFromStrapi } from '../api'
import { fetchFromStrapiWithLocale, type Language } from '../api-with-locale'
import { appConfig } from '@/config/app'

// Strapi v5 轮播图数据类型
export interface StrapiHeroSlide {
  id: number
  documentId: string
  title: string
  alt: string
  clickUrl: string
  order: number
  isActive: boolean
  descriptioon: string // 注意拼写错误，匹配您的schema
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image: {
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    caption: string | null
    width: number
    height: number
    formats: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl: string | null
    provider: string
    provider_metadata: any
    createdAt: string
    updatedAt: string
    publishedAt: string
  }
}

// 前端轮播图类型 - 统一接口定义
export interface HeroSlide {
  id: string
  title: string
  subtitle: string
  description: string
  image: string
  ctaText: string
  ctaLink: string
  order: number
  active?: boolean
}

// 转换 Strapi v5 数据到前端格式
export function transformStrapiHeroSlide(strapiSlide: StrapiHeroSlide): HeroSlide {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔄 转换API轮播图:', strapiSlide.title)
  }

  // 安全地获取图片URL
  let imageUrl = '/images/background/hero-bg.jpg' // 默认图片
  if (strapiSlide.image && strapiSlide.image.url) {
    // 如果URL是相对路径，添加Strapi基础URL
    imageUrl = strapiSlide.image.url.startsWith('http')
        ? strapiSlide.image.url
        : `${appConfig.strapi.url}${strapiSlide.image.url}`

    if (process.env.NODE_ENV === 'development') {
      console.log('📸 API轮播图图片URL:', imageUrl)
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️ API轮播图没有图片，使用默认图片')
    }
  }

  const transformedSlide = {
    id: strapiSlide.documentId,
    title: strapiSlide.title || '',
    subtitle: strapiSlide.alt || '', // 使用alt作为subtitle
    description: strapiSlide.descriptioon || '', // 注意拼写错误，匹配您的schema
    image: imageUrl,
    ctaText: 'Learn More',
    ctaLink: strapiSlide.clickUrl || '#',
    order: strapiSlide.order || 0,
    active: strapiSlide.isActive || false
  }

  if (process.env.NODE_ENV === 'development') {
    console.log('✅ API轮播图转换完成:', {
      title: transformedSlide.title,
      image: transformedSlide.image,
      active: transformedSlide.active,
      order: transformedSlide.order
    })
  }

  return transformedSlide
}

// API 函数
export async function getHeroSlides(language?: Language): Promise<HeroSlide[]> {
  try {
    const response = await fetchFromStrapiWithLocale<StrapiHeroSlide>('hero-slides', {
      populate: ['image'],
      sort: ['order:asc'],
      language: language // 传递语言参数
    });
    
    const transformedSlides = response.data.map(transformStrapiHeroSlide);
    return transformedSlides;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('获取轮播图数据失败:', error)
    };
    return [];
  }
}

export async function getActiveSlides(language?: Language): Promise<HeroSlide[]> {
  return getHeroSlides(language)
}

export async function getSlideById(id: string, language?: Language): Promise<HeroSlide | null> {
  try {
    const response = await fetchFromStrapiWithLocale<StrapiHeroSlide>('/hero-slides', {
      populate: ['image'],
      filters: { id },
      language
    })

    if (response.data.length === 0) {
      return null
    }

    return transformStrapiHeroSlide(response.data[0])
  } catch (error) {
    console.error('Error fetching slide by id:', error)
    return null
  }
}
