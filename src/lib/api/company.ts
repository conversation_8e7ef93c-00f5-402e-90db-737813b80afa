import { fetchFrom<PERSON>trapi, fetchSingleFromStrapi } from '@/lib/api'
import { fetchFromStrapiWithLocale, fetchSingleFromStrapiWithLocale, type Language } from '@/lib/api-with-locale'

// 公司信息接口定义
export interface Company {
  id: string
  documentId: string
  companyName: string
  companySlogan: string
  companyDescription: string
  businessPhone: string
  businessHours: string
  businessEmail: string
  techPhone: string
  techHours: string
  techEmail: string
  address: string
  detailedAddress: string
  postalCode: string
  logo?: {
    url: string
    alternativeText?: string
  }
  wechatQR?: {
    url: string
    alternativeText?: string
  }
  contactQR?: {
    url: string
    alternativeText?: string
  }
  createdAt: string
  updatedAt: string
  publishedAt: string
}

// Strapi返回的公司数据结构 (Strapi 5.x格式)
export interface StrapiCompany {
  id: number
  documentId: string
  companyName: string
  companySlogan: string
  companyDescription: string
  businessPhone: string
  businessHours: string
  businessEmail: string
  techPhone: string
  techHours: string
  techEmail: string
  address: string
  detailedAddress: string
  postalCode: string
  logo?: {
    url: string
    alternativeText?: string
  } | null
  wechatQR?: {
    url: string
    alternativeText?: string
  } | null
  contactQR?: {
    url: string
    alternativeText?: string
  } | null
  createdAt: string
  updatedAt: string
  publishedAt: string
}

// 转换Strapi数据为前端使用的格式
function transformStrapiCompany(strapiCompany: StrapiCompany): Company {
  return {
    id: strapiCompany.id.toString(),
    documentId: strapiCompany.documentId,
    companyName: strapiCompany.companyName,
    companySlogan: strapiCompany.companySlogan,
    companyDescription: strapiCompany.companyDescription,
    businessPhone: strapiCompany.businessPhone,
    businessHours: strapiCompany.businessHours,
    businessEmail: strapiCompany.businessEmail,
    techPhone: strapiCompany.techPhone,
    techHours: strapiCompany.techHours,
    techEmail: strapiCompany.techEmail,
    address: strapiCompany.address,
    detailedAddress: strapiCompany.detailedAddress,
    postalCode: strapiCompany.postalCode,
    logo: strapiCompany.logo || undefined,
    wechatQR: strapiCompany.wechatQR || undefined,
    contactQR: strapiCompany.contactQR || undefined,
    createdAt: strapiCompany.createdAt,
    updatedAt: strapiCompany.updatedAt,
    publishedAt: strapiCompany.publishedAt
  }
}

// 默认公司信息（作为fallback）
const defaultCompanyInfo: Company = {
  id: '0',
  documentId: 'default',
  companyName: 'Linnuo',
  companySlogan: 'Embedded Computing Solutions',
  companyDescription: 'Linnuo is a leading provider of high-performance single board computers designed for developers, makers, and professionals.',
  businessPhone: '+86-152-7791-5606',
  businessHours: '(Mon-Fri 9:00-18:00)',
  businessEmail: '<EMAIL>',
  techPhone: '+86-755-8888-8888',
  techHours: '(Mon-Fri 9:00-18:00)',
  techEmail: '<EMAIL>',
  address: 'Shenzhen, Guangdong, China',
  detailedAddress: 'Linnuo Technology Co., Ltd.\nInnovation District\nShenzhen, Guangdong Province, China',
  postalCode: '518000',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  publishedAt: new Date().toISOString()
}

// API 函数

/**
 * 获取公司信息
 * 返回第一个发布的公司信息记录
 */
export async function getCompanyInfo(language?: Language): Promise<Company> {
  try {
    const response = await fetchFromStrapiWithLocale<StrapiCompany>('/companies', {
      populate: ['logo', 'wechatQR', 'contactQR'],
      sort: ['createdAt:asc'],
      pagination: {
        pageSize: 1
      },
      language
    })

    if (response.data && response.data.length > 0) {
      return transformStrapiCompany(response.data[0])
    }

    if (process.env.NODE_ENV === 'development') {
      console.warn('No company data found in Strapi, using default data')
    }
    return defaultCompanyInfo
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching company info from Strapi:', error)
    }
    return defaultCompanyInfo
  }
}

/**
 * 根据ID获取特定公司信息
 */
export async function getCompanyById(id: string, language?: Language): Promise<Company | null> {
  try {
    const response = await fetchSingleFromStrapiWithLocale<StrapiCompany>(`/companies/${id}`, {
      populate: ['logo', 'wechatQR', 'contactQR'],
      language
    })

    return transformStrapiCompany(response.data)
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error(`Error fetching company with ID ${id}:`, error)
    }
    return null
  }
}

/**
 * 获取所有公司信息
 */
export async function getAllCompanies(language?: Language): Promise<Company[]> {
  try {
    const response = await fetchFromStrapiWithLocale<StrapiCompany>('/companies', {
      populate: ['logo', 'wechatQR', 'contactQR'],
      sort: ['createdAt:asc'],
      language
    })

    return response.data.map(transformStrapiCompany)
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching all companies from Strapi:', error)
    }
    return [defaultCompanyInfo]
  }
}
