import { fetchFromStrapiWithLocale, type Language } from '../api-with-locale'

// Strapi 产品分类数据接口
export interface StrapiProductCategory {
  id: number
  documentId?: string
  name: string
  slug: string
  level: number
  description?: string
  createdAt: string
  updatedAt: string
  publishedAt?: string
  locale?: string
}

// 前端使用的产品分类接口
export interface ProductCategory {
  id: number
  name: string
  slug: string
  level: number
  description?: string
}

// 转换Strapi数据为前端格式
function transformStrapiProductCategory(strapiCategory: StrapiProductCategory): ProductCategory {
  // 如果slug为null，则根据名称生成slug
  const slug = strapiCategory.slug || strapiCategory.name
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .replace(/\s+/g, '-')
  
  return {
    id: strapiCategory.id,
    name: strapiCategory.name,
    slug,
    level: strapiCategory.level,
    description: strapiCategory.description
  }
}

// 获取所有产品分类
export async function getProductCategories(language: Language = 'zh'): Promise<ProductCategory[]> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 getProductCategories 开始执行，语言:', language)
  }
  
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 准备调用 fetchFromStrapiWithLocale...')
    }

    const response = await fetchFromStrapiWithLocale<StrapiProductCategory>('/product-categories', {
      populate: '*',
      sort: ['level:asc', 'name:asc'], // 按级别和名称排序
      language
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('📦 Strapi API 响应:', response)
      console.log('📊 获取到的原始分类数量:', response.data?.length || 0)
    }

    const transformedCategories = response.data.map(transformStrapiProductCategory)
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 转换后的分类数量:', transformedCategories.length)
      console.log('🎯 转换后的分类数据:', transformedCategories)
    }

    return transformedCategories
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 从Strapi获取产品分类时发生错误:', error)
    }
    return []
  }
}

// 根据slug获取产品分类详情
export async function getProductCategoryBySlug(slug: string, language: Language = 'zh'): Promise<ProductCategory | null> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 getProductCategoryBySlug 开始执行，参数:', { slug, language })
  }

  try {
    const response = await fetchFromStrapiWithLocale<StrapiProductCategory>('/product-categories', {
      populate: '*',
      filters: {
        slug: { $eq: slug }
      },
      language
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('📦 Strapi API 响应:', response)
    }
    
    if (response.data.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ 产品分类未找到:', slug)
      }
      return null
    }

    const category = transformStrapiProductCategory(response.data[0])
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 产品分类详情获取成功:', category.name)
    }
    return category
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 产品分类详情获取失败:', error)
    }
    return null
  }
}