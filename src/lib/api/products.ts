import { fetchFromStrapi, fetchSingleFromStrapi, StrapiResponse, StrapiSingleResponse } from '../api'
import { fetchFromStrapiWithLocale, fetchSingleFromStrapiWithLocale, type Language } from '../api-with-locale'
import { appConfig } from '@/config/app'
import { Product } from '@/types/product'

// Strapi 产品数据接口 - 修复以匹配实际API结构
export interface StrapiProduct {
  id: number
  documentId?: string
  product_name: string
  slug: string
  full_description?: string
  Short_description?: string // 注意大写S，匹配Strapi字段名
  faBuStatus: 'Draft' | 'Published'
  createdAt: string
  updatedAt: string
  publishedAt?: string
  locale?: string

  // 产品分类关联字段
  product_category?: {
    id: number
    documentId?: string
    name: string
    slug: string
    level: number
    description?: string
    createdAt: string
    updatedAt: string
    publishedAt?: string
    locale?: string
  }

  // 图片字段 - 修复结构以匹配Strapi v5
  image?: Array<{
    id: number
    documentId?: string
    name: string
    alternativeText?: string | null
    caption?: string | null
    width?: number
    height?: number
    formats?: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl?: string | null
    provider: string
    provider_metadata?: any
    createdAt: string
    updatedAt: string
    publishedAt?: string
  }>

  fengmiantu?: {
    id: number
    documentId?: string
    name: string
    alternativeText?: string | null
    caption?: string | null
    width?: number
    height?: number
    formats?: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl?: string | null
    provider: string
    provider_metadata?: any
    createdAt: string
    updatedAt: string
    publishedAt?: string
  }
  
  // 技术规格字段 - ProductCard中使用的字段
  cpuLeiXing?: string // CPU类型
  neiCun?: string // 内存
  wangKa?: string // 网卡
  xianShiJieKou?: string // 显示接口
  powerType?: string // 电源类型
  operating_system?: string // 操作系统
  operating_temperature?: string // 工作温度
  
  // 富文本内容字段 - ProductDetailView中使用
  features?: string // 特性描述（富文本）
  applications?: string // 应用场景（富文本）
  
  // 下载文件 - ProductDetailView中使用，修复结构
  downloads?: Array<{
    id: number
    documentId?: string
    name: string
    alternativeText?: string | null
    caption?: string | null
    width?: number
    height?: number
    formats?: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl?: string | null
    provider: string
    provider_metadata?: any
    createdAt: string
    updatedAt: string
    publishedAt?: string
  }>
}

// 转换 Strapi v5 数据到前端格式 - 修复数据结构匹配
export function transformStrapiProduct(strapiProduct: StrapiProduct): Product {
  // 处理简短描述 - 修复字段名
  let shortDesc = ''
  if (strapiProduct.Short_description) { // 注意大写S
    // 如果是富文本，提取纯文本
    if (typeof strapiProduct.Short_description === 'string') {
      shortDesc = strapiProduct.Short_description.replace(/<[^>]*>/g, '').trim()
    }
  }

  // 如果没有简短描述，从完整描述中截取
  if (!shortDesc && strapiProduct.full_description) {
    const fullDescText = typeof strapiProduct.full_description === 'string'
      ? strapiProduct.full_description.replace(/<[^>]*>/g, '').trim()
      : ''
    shortDesc = fullDescText.length > 150
      ? fullDescText.substring(0, 150) + '...'
      : fullDescText
  }

  // 处理图片数组 - 修复图片URL处理逻辑并去除重复
  const images: any[] = []
  const seenImageIds = new Set<number>() // 用于去重

  // 添加详情页图片
  if (strapiProduct.image && strapiProduct.image.length > 0) {
    strapiProduct.image.forEach((img, index) => {
      // 检查是否已经添加过这个图片ID
      if (seenImageIds.has(img.id)) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`⚠️ 发现重复图片 ID: ${img.id}, 跳过`)
        }
        return
      }
      seenImageIds.add(img.id)

      let imageUrl = img.url

      // 修复图片URL处理逻辑
      if (!imageUrl.startsWith('http')) {
        // 如果URL不是完整的，添加Strapi基础URL
        const strapiUrl = appConfig.strapi.url.replace(/\/$/, '') // 移除末尾斜杠
        imageUrl = imageUrl.startsWith('/') ? `${strapiUrl}${imageUrl}` : `${strapiUrl}/${imageUrl}`
      }

      images.push({
        id: img.id.toString(),
        url: imageUrl,
        alt: img.alternativeText || strapiProduct.product_name,
        type: (images.length === 0 ? 'main' : 'gallery') as 'main' | 'gallery' | 'thumbnail',
        order: images.length + 1
      })
    })

    if (process.env.NODE_ENV === 'development') {
      console.log(`📸 处理图片: 原始${strapiProduct.image.length}张，去重后${images.length}张`)
    }
  }

  // 如果没有详情页图片，使用封面图 - 修复URL处理
  if (images.length === 0 && strapiProduct.fengmiantu) {
    let imageUrl = strapiProduct.fengmiantu.url

    // 修复封面图URL处理逻辑
    if (!imageUrl.startsWith('http')) {
      const strapiUrl = appConfig.strapi.url.replace(/\/$/, '') // 移除末尾斜杠
      imageUrl = imageUrl.startsWith('/') ? `${strapiUrl}${imageUrl}` : `${strapiUrl}/${imageUrl}`
    }

    images.push({
      id: strapiProduct.fengmiantu.id.toString(),
      url: imageUrl,
      alt: strapiProduct.fengmiantu.alternativeText || strapiProduct.product_name,
      type: 'main' as 'main' | 'gallery' | 'thumbnail',
      order: 1
    })
  }

  // 构建规格数据
  const specifications = []
  if (strapiProduct.cpuLeiXing || strapiProduct.neiCun || strapiProduct.wangKa ||
      strapiProduct.xianShiJieKou || strapiProduct.powerType ||
      strapiProduct.operating_system || strapiProduct.operating_temperature) {

    const specItems = []
    if (strapiProduct.cpuLeiXing) specItems.push({ label: 'CPU类型', value: strapiProduct.cpuLeiXing })
    if (strapiProduct.neiCun) specItems.push({ label: '内存', value: strapiProduct.neiCun })
    if (strapiProduct.wangKa) specItems.push({ label: '网卡', value: strapiProduct.wangKa })
    if (strapiProduct.xianShiJieKou) specItems.push({ label: '显示接口', value: strapiProduct.xianShiJieKou })
    if (strapiProduct.powerType) specItems.push({ label: '电源类型', value: strapiProduct.powerType })
    if (strapiProduct.operating_system) specItems.push({ label: '操作系统', value: strapiProduct.operating_system })
    if (strapiProduct.operating_temperature) specItems.push({ label: '工作温度', value: strapiProduct.operating_temperature })

    specifications.push({
      category: '技术规格',
      items: specItems
    })
  }



  // 生成 slug：如果 Strapi 中没有 slug，则基于产品名称生成
  const slug = strapiProduct.slug || strapiProduct.product_name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')

  // 处理产品分类关联数据 - 与分类slug生成逻辑保持一致
  let category = 'scalable-embedded-series' // 默认分类
  if (strapiProduct.product_category) {
    if (strapiProduct.product_category.slug) {
      category = strapiProduct.product_category.slug
    } else {
      // 如果slug为null，根据名称生成slug（与product-categories.ts中的逻辑一致）
      category = strapiProduct.product_category.name
        .toLowerCase()
        .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
        .replace(/^-+|-+$/g, '')
        .replace(/\s+/g, '-')
    }
  }
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🏷️ 产品分类处理:', {
      productName: strapiProduct.product_name,
      originalSlug: strapiProduct.product_category?.slug,
      categoryName: strapiProduct.product_category?.name,
      finalCategory: category
    })
  }

  return {
    id: strapiProduct.id.toString(),
    name: strapiProduct.product_name,
    slug: slug,
    description: strapiProduct.full_description || '',
    shortDescription: shortDesc,
    category: category as any,
    status: strapiProduct.faBuStatus === 'Published' ? 'available' : 'coming-soon' as any,
    images,
    specifications,
    features: [],
    tags: [],
    releaseDate: strapiProduct.publishedAt || new Date().toISOString(),
    isNew: false,
    isFeatured: false,
    compatibility: [],
    warranty: '1 year limited warranty',
    support: [],
    // 技术规格字段 - ProductCard中使用
    cpuLeiXing: strapiProduct.cpuLeiXing,
    neiCun: strapiProduct.neiCun,
    wangKa: strapiProduct.wangKa,
    xianShiJieKou: strapiProduct.xianShiJieKou,
    powerType: strapiProduct.powerType,
    operating_system: strapiProduct.operating_system,
    operating_temperature: strapiProduct.operating_temperature,
    // ProductDetailView中使用的字段
    detailTabs: {
      description: strapiProduct.full_description || '',
      specifications: strapiProduct.features || '',
      compatibility: strapiProduct.applications || '',
      downloads: [] // 添加空的downloads数组以满足类型要求
    },
    downloadLinks: strapiProduct.downloads?.map((file) => {
      // 修复下载文件URL处理
      let fileUrl = file.url
      if (!fileUrl.startsWith('http')) {
        const strapiUrl = appConfig.strapi.url.replace(/\/$/, '')
        fileUrl = fileUrl.startsWith('/') ? `${strapiUrl}${fileUrl}` : `${strapiUrl}/${fileUrl}`
      }

      return {
        id: file.id.toString(),
        name: file.name,
        url: fileUrl,
        type: 'manual' as const,
        size: file.size?.toString() || 'Unknown',
        version: '1.0',
        platform: []
      }
    }) || [],
    createdAt: strapiProduct.createdAt,
    updatedAt: strapiProduct.updatedAt
  }
}

// API 函数
export async function getProducts(options?: {
  category?: string
  featured?: boolean
  limit?: number
  language?: Language
  disableAutoLocale?: boolean
}): Promise<Product[]> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 getProducts 开始执行，参数:', options)
  }
  
  try {
    // 注意：Strapi中没有category字段，所以我们先获取所有产品，然后在前端进行分类过滤
    const filters: Record<string, any> = {}

    // 暂时移除category过滤，因为Strapi模型中没有这个字段
    // if (options?.category) {
    //   filters.category = options.category
    // }

    // 注意：Strapi中没有isFeatured字段，所以我们移除这个过滤条件
    // 将在前端进行特色产品的筛选
    // if (options?.featured !== undefined) {
    //   filters.isFeatured = options.featured
    // }

    if (process.env.NODE_ENV === 'development') {
      console.log('📋 构建的过滤器:', filters)
      console.log('🌐 准备调用 fetchFromStrapiWithLocale...')
    }

    const response = await fetchFromStrapiWithLocale<StrapiProduct>('/products', {
      populate: '*', // 获取所有关联数据，包括image, fengmiantu, downloads等
      filters,
      sort: ['createdAt:desc'],
      pagination: {
        pageSize: options?.limit || 100
      },
      language: options?.language, // 传递语言参数
      disableAutoLocale: options?.disableAutoLocale // 传递disableAutoLocale参数
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('📦 Strapi API 响应:', response)
      console.log('📊 获取到的原始产品数量:', response.data?.length || 0)
    }

    const transformedProducts = response.data.map(transformStrapiProduct)
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 转换后的产品数量:', transformedProducts.length)
      console.log('🎯 转换后的产品数据:', transformedProducts)
    }

    // 在前端进行分类过滤
    if (options?.category && options.category !== 'all') {
      const filteredProducts = transformedProducts.filter(product => {
        // 使用产品的分类信息进行匹配
        if (!product.category) {
          if (process.env.NODE_ENV === 'development') {
            console.log('⚠️ 产品没有分类信息:', product.name)
          }
          return false
        }

        // 直接匹配分类slug
        const productCategorySlug = product.category.toLowerCase()
        const targetCategory = options.category.toLowerCase()
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔍 产品 "${product.name}" 的分类: "${productCategorySlug}", 目标分类: "${targetCategory}"`)
        }
        
        return productCategorySlug === targetCategory
      })

      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 分类过滤 "${options.category}": ${filteredProducts.length} 个产品`)
        console.log('📋 过滤后的产品:', filteredProducts.map(p => ({ name: p.name, category: p.category })))
      }
      return filteredProducts
    }

    return transformedProducts
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ 从Strapi获取产品时发生错误:', error)
    }
    return []
  }
}

export async function getProductBySlug(slug: string, language?: Language): Promise<Product | null> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 getProductBySlug 开始执行，参数:', { slug, language })
  }

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('📞 准备调用 fetchFromStrapiWithLocale...')
    }
    // 由于 Strapi 中没有 slug 字段，我们获取所有产品然后在前端匹配
    const response = await fetchFromStrapiWithLocale<StrapiProduct>('/products', {
      populate: '*', // 获取所有关联数据
      language // 传递语言参数
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('📦 Strapi API 响应:', response)
      console.log('📊 获取到的产品数量:', response.data?.length || 0)
    }

    if (response.data.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ 指定语言未找到产品，尝试获取其他语言版本')
      }

      // 尝试获取任意语言版本的产品
      try {
        const fallbackResponse = await fetchFromStrapiWithLocale<StrapiProduct>('/products', {
          populate: '*',
          filters: {
            slug: { $eq: slug }
          },
          disableAutoLocale: true // 禁用自动locale，获取所有语言版本
        })

        if (fallbackResponse.data.length > 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ 找到其他语言版本的产品:', fallbackResponse.data.length, '个')
          }
          // 使用第一个找到的产品
          const fallbackProduct = fallbackResponse.data[0]
          if (process.env.NODE_ENV === 'development') {
            console.log('🔄 使用后备产品:', {
              id: fallbackProduct.id,
              name: fallbackProduct.product_name,
              locale: fallbackProduct.locale
            })
          }

          const transformedProduct = transformStrapiProduct(fallbackProduct)
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ 后备产品转换完成')
          }
          return transformedProduct
        }
      } catch (fallbackError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 获取后备产品失败:', fallbackError)
        }
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('❌ 完全未找到匹配的产品')
      }
      return null
    }

    // 生成 slug 匹配函数
    const generateSlug = (productName: string) => {
      return productName
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // 查找匹配的产品 - 通过生成的 slug 或 ID 匹配
    const foundProduct = response.data.find(product => {
      const generatedSlug = generateSlug(product.product_name)
      const productSlug = product.slug || generatedSlug
      const idMatch = product.id.toString() === slug

      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 检查产品匹配:', {
          productId: product.id,
          productName: product.product_name,
          originalSlug: product.slug,
          generatedSlug: generatedSlug,
          finalSlug: productSlug,
          requestedSlug: slug,
          slugMatch: productSlug === slug,
          idMatch: idMatch,
          match: productSlug === slug || idMatch
        })
      }

      return productSlug === slug || idMatch
    })

    if (!foundProduct) {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ 未找到匹配的产品')
        console.log('📋 可用的产品信息:', response.data.map(p => ({
          id: p.id,
          name: p.product_name,
          originalSlug: p.slug,
          generatedSlug: generateSlug(p.product_name)
        })))
      }
      return null
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 找到匹配的产品:', {
        id: foundProduct.id,
        name: foundProduct.product_name,
        originalSlug: foundProduct.slug,
        generatedSlug: generateSlug(foundProduct.product_name)
      })
    }

    const transformedProduct = transformStrapiProduct(foundProduct)

    // 验证转换后的产品数据
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 产品转换完成:', {
        id: transformedProduct.id,
        name: transformedProduct.name,
        slug: transformedProduct.slug,
        imageCount: transformedProduct.images.length,
        hasDescription: !!transformedProduct.description,
        hasShortDescription: !!transformedProduct.shortDescription
      })
    }

    return transformedProduct
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ getProductBySlug 发生错误:', error)
    }
    return null
  }
}

export async function getFeaturedProducts(language?: Language): Promise<Product[]> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🌟 getFeaturedProducts 开始执行，语言:', language)
  }
  try {
    // 由于Strapi中没有isFeatured字段，我们获取所有产品并返回前6个作为特色产品
    if (process.env.NODE_ENV === 'development') {
      console.log('📞 调用 getProducts，参数:', { limit: 6, language })
    }
    const allProducts = await getProducts({ limit: 6, language })
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ getFeaturedProducts 完成，返回产品数量:', allProducts.length)
    }
    return allProducts
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ getFeaturedProducts 发生错误:', error)
    }
    return []
  }
}

export async function getProductsByCategory(category: string, language?: Language): Promise<Product[]> {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 getProductsByCategory 开始执行，分类:', category, '语言:', language)
  }
  
  try {
    // 首先尝试获取指定语言的产品
    let products = await getProducts({ language })
    
    // 如果指定语言没有产品，尝试获取英文版本
    if (products.length === 0 && language !== 'en') {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ 当前语言没有产品数据，尝试获取英文版本')
      }
      products = await getProducts({ language: 'en' })
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 获取到的产品总数:', products.length)
    }
    
    // 过滤指定分类的产品 - 支持多种匹配方式
    const filteredProducts = products.filter(product => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 检查产品 ${product.name} 的分类: ${product.category}`)
      }
      
      // 1. 直接匹配分类slug
      if (product.category === category) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 直接匹配成功')
        }
        return true
      }
      
      // 2. 如果分类是从名称生成的slug，尝试匹配原始名称
      // 将分类名称转换为slug格式进行比较
      const categorySlug = product.category?.toLowerCase()
        .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
        .replace(/^-+|-+$/g, '')
        .replace(/\s+/g, '-')
      
      if (categorySlug === category) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 生成slug匹配成功')
        }
        return true
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ 分类不匹配')
      }
      return false
    })
    
    if (process.env.NODE_ENV === 'development') {
      console.log('📋 过滤后的产品数量:', filteredProducts.length)
    }
    return filteredProducts
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ getProductsByCategory 错误:', error)
    }
    return []
  }
}
