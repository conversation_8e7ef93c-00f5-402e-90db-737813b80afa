import { fetchFromStrapi } from '../api'
import { fetchFromStrapiWithLocale, fetchSingleFromStrapiWithLocale, type Language } from '../api-with-locale'

// Strapi v5 新闻数据类型 (匹配实际的 new-article 结构)
export interface StrapiNewsArticle {
  id: number
  documentId: string
  title: string
  slug: string
  excerpt: string
  content: string
  category?: string
  tags?: string
  author?: string
  publishedDate?: string
  isFeatured?: boolean
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: {
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    caption: string | null
    width: number
    height: number
    formats: any
    hash: string
    ext: string
    mime: string
    size: number
    url: string
    previewUrl: string | null
    provider: string
    provider_metadata: any
    createdAt: string
    updatedAt: string
    publishedAt: string
  } | null
  localizations: any[]
}

// 前端新闻类型
export interface NewsArticle {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  image: string
  category: string
  author: string
  readTime: string
  date: string
  featured: boolean
  tags: string[]
  link: string
}

// 转换 Strapi v5 数据到前端格式 - 修复图片URL处理
export function transformStrapiNewsArticle(strapiArticle: StrapiNewsArticle): NewsArticle {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔄 转换新闻文章:', strapiArticle.title)
  }

  // 处理图片URL - 修复为单个图片对象而不是数组
  let imageUrl = '/images/background/prod_bg.jpg' // 默认图片
  if (strapiArticle.image) {
    imageUrl = strapiArticle.image.url

    // 如果URL不是完整的，添加Strapi基础URL
    if (!imageUrl.startsWith('http')) {
      const strapiUrl = (process.env.NEXT_PUBLIC_STRAPI_URL || 'https://vivid-pleasure-04cb3dbd82.strapiapp.com').replace(/\/$/, '')
      imageUrl = imageUrl.startsWith('/') ? `${strapiUrl}${imageUrl}` : `${strapiUrl}/${imageUrl}`
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('📸 图片URL:', imageUrl)
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️ 未找到图片，使用默认图片')
    }
  }

  // 计算阅读时间（基于内容长度的简单估算）
  const readTime = Math.max(1, Math.ceil((strapiArticle.content?.length || 0) / 1000)) + ' min read'

  // 处理标签 - 从tags字段或category字段获取
  const tags: string[] = []
  if (strapiArticle.tags) {
    // 如果tags是字符串，按逗号分割
    const tagList = typeof strapiArticle.tags === 'string'
      ? strapiArticle.tags.split(',').map(tag => tag.trim())
      : [strapiArticle.tags]
    tags.push(...tagList)
  }
  if (strapiArticle.category && !tags.includes(strapiArticle.category)) {
    tags.push(strapiArticle.category)
  }

  // 使用publishedDate字段，如果没有则使用publishedAt
  const publishDate = strapiArticle.publishedDate || strapiArticle.publishedAt.split('T')[0]

  const transformedArticle = {
    id: strapiArticle.id,
    title: strapiArticle.title,
    slug: strapiArticle.slug || strapiArticle.id.toString(), // 优先使用 Strapi 的 slug，如果没有则使用 ID
    excerpt: strapiArticle.excerpt,
    content: strapiArticle.content,
    image: imageUrl,
    category: strapiArticle.category || 'General',
    author: strapiArticle.author || 'Linnuo Team',
    readTime,
    date: publishDate,
    featured: strapiArticle.isFeatured || false,
    tags,
    link: `/news/${strapiArticle.slug || strapiArticle.id}` // 优先使用 Strapi 的 slug，如果没有则使用 ID
  }

  if (process.env.NODE_ENV === 'development') {
    console.log('✅ 转换完成:', {
      title: transformedArticle.title,
      category: transformedArticle.category,
      featured: transformedArticle.featured,
      imageUrl: transformedArticle.image
    })
  }

  return transformedArticle
}

// API 函数
export async function getNewsArticles(options?: {
  featured?: boolean
  category?: string
  limit?: number
  language?: Language
}): Promise<NewsArticle[]> {
  try {
    const filters: Record<string, any> = {}

    if (options?.category) {
      filters.category = options.category
    }

    const response = await fetchFromStrapiWithLocale<StrapiNewsArticle>('/new-articles', { // 更新API路径
      populate: ['image'],
      filters,
      sort: ['publishedAt:desc'],
      pagination: {
        pageSize: options?.limit || 20
      },
      language: options?.language
    })

    return response.data.map(transformStrapiNewsArticle)
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching news articles:', error)
    }
    return []
  }
}

export async function getNewsArticleBySlug(slug: string, language?: Language): Promise<NewsArticle | null> {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 getNewsArticleBySlug 开始执行，参数:', { slug, language })
    }

    // 首先尝试通过 slug 字段查找
    try {
      const responseBySlug = await fetchFromStrapiWithLocale<StrapiNewsArticle>('/new-articles', {
        populate: ['image'],
        filters: {
          slug: {
            $eq: slug
          }
        },
        language
      })

      if (responseBySlug.data && responseBySlug.data.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 通过 slug 找到新闻:', {
            id: responseBySlug.data[0].id,
            title: responseBySlug.data[0].title,
            slug: responseBySlug.data[0].slug
          })
        }
        return transformStrapiNewsArticle(responseBySlug.data[0])
      }
    } catch (slugError) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ 通过 slug 查找失败，尝试通过 ID 查找')
      }
    }

    // 如果通过 slug 找不到，尝试通过 ID 查找
    const articleId = parseInt(slug)
    if (!isNaN(articleId)) {
      const responseById = await fetchSingleFromStrapiWithLocale<StrapiNewsArticle>(`/new-articles/${articleId}`, {
        populate: ['image'],
        language
      })

      if (responseById.data) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 通过 ID 找到新闻:', {
            id: responseById.data.id,
            title: responseById.data.title,
            slug: responseById.data.slug
          })
        }
        return transformStrapiNewsArticle(responseById.data)
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('❌ 未找到指定的新闻:', slug)
    }
    return null
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ getNewsArticleBySlug 发生错误:', error)
    }
    return null
  }
}

export async function getFeaturedNews(language?: Language): Promise<NewsArticle[]> {
  return getNewsArticles({ featured: true, limit: 3, language })
}

export async function getNewsByCategory(category: string, language?: Language): Promise<NewsArticle[]> {
  return getNewsArticles({ category, language })
}
