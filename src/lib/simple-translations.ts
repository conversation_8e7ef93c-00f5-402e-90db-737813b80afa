// 简化的中文翻译常量
export const TRANSLATIONS = {
  // 导航
  nav: {
    home: '首页',
    products: '产品',
    about: '关于我们',
    news: '新闻',
    contact: '联系我们',
    application: '应用案例',
    support: '技术支持',
    downloads: '下载中心'
  },
  
  // 首页
  hero: {
    title: 'LattePanda - 单板计算机领导者',
    subtitle: '强大的性能，无限的可能',
    description: '专业的单板计算机解决方案，适用于物联网、人工智能、工业自动化等领域',
    learnMore: '了解更多',
    viewProducts: '查看产品'
  },
  
  // 产品
  products: {
    title: '产品中心',
    description: '探索我们的单板计算机产品线',
    features: '产品特性',
    specifications: '技术规格',
    viewDetails: '查看详情',
    compare: '产品对比',
    addToCompare: '添加对比',
    removeFromCompare: '移除对比'
  },
  
  // 新闻
  news: {
    title: '新闻资讯',
    readMore: '阅读更多',
    publishedAt: '发布时间',
    category: '分类',
    author: '作者',
    relatedNews: '相关新闻'
  },
  
  // 通用
  common: {
    loading: '加载中...',
    error: '出错了',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    more: '更多',
    less: '收起',
    viewAll: '查看全部',
    backToTop: '返回顶部'
  },
  
  // 联系我们
  contact: {
    title: '联系我们',
    name: '姓名',
    email: '邮箱',
    message: '留言',
    send: '发送',
    address: '地址',
    phone: '电话',
    businessHours: '营业时间'
  },
  
  // 页脚
  footer: {
    company: '公司信息',
    products: '产品',
    support: '技术支持',
    legal: '法律信息',
    copyright: '版权所有'
  }
}

// 简单的翻译函数
export function t(key: string): string {
  const keys = key.split('.')
  let value: any = TRANSLATIONS
  
  for (const k of keys) {
    value = value?.[k]
    if (!value) {
      console.warn(`Translation missing for key: ${key}`)
      return key
    }
  }
  
  return typeof value === 'string' ? value : key
}

// 兼容性导出
export const useTranslations = () => ({ t })
