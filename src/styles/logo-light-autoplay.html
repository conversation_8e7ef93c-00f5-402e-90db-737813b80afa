<!DOCTYPE html>
<html>
<head>
    <title>Linnuo Technologty,Welcome!</title>
    <style>
        body {
            background: #fff;
            margin: 0;
            padding: 20px;
        }
        
        .logo-container {
            display: inline-block;
        }
        
        .logo-container svg {
            width: 300px;
            height: auto;
        }
        
        .animated-text {
            fill: rgba(0, 0, 0, 0);
            stroke: #000;
            stroke-width: 1;
            stroke-dasharray: 0 100%;
            stroke-dashoffset: 100%;
            animation: logoReveal 3s ease-in-out 0.5s forwards;
        }
        
        /* 为不同的文字元素添加延迟，创造逐个显示的效果 */
        .animated-text:nth-child(1) { animation-delay: 0.5s; }
        .animated-text:nth-child(2) { animation-delay: 0.6s; }
        .animated-text:nth-child(3) { animation-delay: 0.7s; }
        .animated-text:nth-child(4) { animation-delay: 0.8s; }
        .animated-text:nth-child(5) { animation-delay: 0.9s; }
        .animated-text:nth-child(6) { animation-delay: 1.0s; }
        .animated-text:nth-child(7) { animation-delay: 1.1s; }
        .animated-text:nth-child(8) { animation-delay: 1.2s; }
        .animated-text:nth-child(9) { animation-delay: 1.3s; }
        .animated-text:nth-child(10) { animation-delay: 1.4s; }
        .animated-text:nth-child(11) { animation-delay: 1.5s; }
        .animated-text:nth-child(12) { animation-delay: 1.6s; }
        .animated-text:nth-child(13) { animation-delay: 1.7s; }
        .animated-text:nth-child(14) { animation-delay: 1.8s; }
        .animated-text:nth-child(15) { animation-delay: 1.9s; }
        .animated-text:nth-child(16) { animation-delay: 2.0s; }
        .animated-text:nth-child(17) { animation-delay: 2.1s; }
        .animated-text:nth-child(18) { animation-delay: 2.2s; }
        .animated-text:nth-child(19) { animation-delay: 2.3s; }
        .animated-text:nth-child(20) { animation-delay: 2.4s; }
        .animated-text:nth-child(21) { animation-delay: 0.2s; }
        .animated-text:nth-child(22) { animation-delay: 0.3s; }
        .animated-text:nth-child(23) { animation-delay: 0.4s; }
        .animated-text:nth-child(24) { animation-delay: 0.5s; }
        .animated-text:nth-child(25) { animation-delay: 0.6s; }
        
        @keyframes logoReveal {
            0% {
                fill: rgba(0, 0, 0, 0);
                stroke: #000;
                stroke-dashoffset: 100%;
                stroke-dasharray: 0 100%;
                stroke-width: 1;
            }
            50% {
                fill: rgba(0, 0, 0, 0);
                stroke: #000;
                stroke-width: 1.5;
            }
            70% {
                fill: rgba(0, 0, 0, 0);
                stroke: #000;
                stroke-width: 2;
            }
            100% {
                fill: rgba(0, 0, 0, 1);
                stroke: rgba(0, 0, 0, 0);
                stroke-dashoffset: 0;
                stroke-dasharray: 100% 0;
                stroke-width: 0;
            }
        }
        
        .cls-3 {
            fill: #e60012;
        }
        
        /* 可选：hover时重新播放动画 */
        .logo-container:hover .animated-text {
            animation: logoReveal 2s ease-in-out forwards;
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="250 70 350 80">
            <!-- 小字体文字 "INTELLIGENT CONTROL" -->
            <path class="animated-text" d="M316.08,139.8v9.57h-1.36V139.8Z" />
            <path class="animated-text" d="M319.68,149.37V139.8h1.62c2.15,3,5.23,6.93,5.77,7.92h0c-.09-1.25-.07-2.76-.07-4.31V139.8h1.26v9.57h-1.53c-1.42-1.91-5.17-7.07-5.86-8.18h0c.08,1.23.08,2.59.08,4.28v3.9Z" />
            <path class="animated-text" d="M334.35,141h-3.64V139.8h8.65V141h-3.64v8.42h-1.37Z" />
            <path class="animated-text" d="M348.86,144.92h-5.74v3.31h6.34l-.18,1.14h-7.51V139.8h7.4v1.14h-6.05v2.83h5.74Z" />
            <path class="animated-text" d="M352.24,139.8h1.34v8.42h5.77l-.2,1.15h-6.91Z" />
            <path class="animated-text" d="M361.65,139.8H363v8.42h5.77l-.21,1.15h-6.91Z" />
            <path class="animated-text" d="M372.42,139.8v9.57h-1.35V139.8Z" />
            <path class="animated-text" d="M384.94,149.37h-1a10.23,10.23,0,0,1-.11-1.41c-.64,1-1.61,1.62-3.49,1.62a4.6,4.6,0,0,1-4.87-5,4.82,4.82,0,0,1,5.11-5c2.86,0,4.09,1.46,4.44,2.87H383.6a3,3,0,0,0-3.06-1.76,3.52,3.52,0,0,0-3.67,3.83c0,2.25,1.28,3.92,3.63,3.92,2,0,3.13-1,3.13-2.9v-.29h-3.1v-1.13h4.41Z" />
            <path class="animated-text" d="M395.28,144.92h-5.74v3.31h6.34l-.19,1.14h-7.5V139.8h7.39v1.14h-6v2.83h5.74Z" />
            <path class="animated-text" d="M398.65,149.37V139.8h1.62c2.16,3,5.23,6.93,5.78,7.92h0c-.08-1.25-.07-2.76-.07-4.31V139.8h1.27v9.57h-1.53c-1.43-1.91-5.18-7.07-5.86-8.18h0c.07,1.23.07,2.59.07,4.28v3.9Z" />
            <path class="animated-text" d="M413.33,141h-3.65V139.8h8.66V141h-3.65v8.42h-1.36Z" />
            <path class="animated-text" d="M434.2,146.57c-.54,1.64-1.74,3-4.47,3a4.67,4.67,0,0,1-4.91-5,4.82,4.82,0,0,1,5-5c2.76,0,4.06,1.49,4.44,3.06h-1.39a3,3,0,0,0-3.07-1.93c-2.2,0-3.53,1.62-3.53,3.83s1.32,3.89,3.54,3.89a2.92,2.92,0,0,0,3-1.88Z" />
            <path class="animated-text" d="M446.47,144.56a5,5,0,1,1-10.05,0,5,5,0,0,1,10.05,0Zm-8.62,0a3.62,3.62,0,0,0,3.63,3.92c2.34,0,3.56-1.78,3.56-3.85a3.61,3.61,0,0,0-3.65-3.86A3.54,3.54,0,0,0,437.85,144.52Z" />
            <path class="animated-text" d="M449.42,149.37V139.8H451c2.16,3,5.23,6.93,5.77,7.92h0c-.08-1.25-.07-2.76-.07-4.31V139.8H458v9.57H456.5c-1.42-1.91-5.17-7.07-5.86-8.18h0c.07,1.23.07,2.59.07,4.28v3.9Z" />
            <path class="animated-text" d="M464.1,141h-3.65V139.8h8.65V141h-3.64v8.42H464.1Z" />
            <path class="animated-text" d="M472.86,145.25v4.12h-1.34V139.8h4.57c2,0,3.28.9,3.28,2.6a2.17,2.17,0,0,1-1.75,2.28c.63.18,1.49.66,1.49,2.33v.42a8.87,8.87,0,0,0,.12,1.94h-1.39a7.55,7.55,0,0,1-.12-1.94v-.32c0-1.41-.55-1.86-2.23-1.86Zm0-1.11h2.89c1.49,0,2.23-.52,2.23-1.66s-.74-1.57-2.18-1.57h-2.94Z" />
            <path class="animated-text" d="M491.93,144.56a5,5,0,1,1-10.05,0,5,5,0,0,1,10.05,0Zm-8.62,0a3.62,3.62,0,0,0,3.63,3.92c2.34,0,3.56-1.78,3.56-3.85a3.61,3.61,0,0,0-3.65-3.86A3.54,3.54,0,0,0,483.31,144.52Z" />
            <path class="animated-text" d="M494.88,139.8h1.35v8.42H502l-.2,1.15h-6.92Z" />
            
            <!-- 大字体文字 "INNUO" -->
            <path class="animated-text" d="M319.34,78v47.94H309.27V78Z" />
            <path class="animated-text" d="M332.34,125.89V78h12c12.6,18.77,21.21,30.18,24.12,35.63h.14c-.46-5.42-.39-13.94-.39-21.79V78h9.26v47.94H366.2C360.85,118,344.4,93.49,341.36,88h-.14c.39,6,.39,12.8.39,21.31v16.6Z" />
            <path class="animated-text" d="M390.43,125.89V78h11.95c12.6,18.77,21.2,30.18,24.12,35.63h.13c-.45-5.42-.38-13.94-.38-21.79V78h9.26v47.94H424.29C418.93,118,402.48,93.49,399.44,88h-.13c.38,6,.38,12.8.38,21.31v16.6Z" />
            <path class="animated-text" d="M458.05,78V105.1c0,11,6,13.82,12.34,13.82,7.08,0,11.63-3.17,11.63-13.82V78h10.06v27.38c0,14.88-8.16,21.57-22,21.57-13.48,0-22.1-6.33-22.1-21.23V78Z" />
            <path class="animated-text" d="M552.76,101.76c0,13.37-9,25.14-26,25.14-16,0-25.4-11.1-25.4-25.11,0-13.22,9.86-24.86,25.83-24.86C543.51,76.93,552.76,88.61,552.76,101.76ZM512,101.63c0,9.46,5.14,17.29,15.29,17.29,10.32,0,14.81-8.39,14.81-17,0-9-4.82-17-15.26-17C516.94,84.92,512,92.89,512,101.63Z" />
            
            <!-- 红色装饰 -->
            <polygon class="cls-3" points="266.28 76.7 266.28 113.81 263.62 111.15 263.62 76.7 266.28 76.7" />
            <polygon class="cls-3" points="270.29 76.7 270.29 117.82 267.66 115.18 267.66 76.7 270.29 76.7" />
            <polygon class="cls-3" points="274.3 76.7 274.3 121.82 271.66 119.19 271.66 76.7 274.3 76.7" />
            <polygon class="cls-3" points="278.33 76.7 278.33 125.86 275.68 123.21 275.68 76.7 278.33 76.7" />
            <polygon class="cls-3" points="293.32 113.84 278.34 113.84 278.34 111.18 290.66 111.18 293.32 113.84" />
            <polygon class="cls-3" points="297.33 117.86 278.34 117.86 278.34 115.2 294.67 115.2 297.33 117.86" />
            <polygon class="cls-3" points="301.35 121.87 278.34 121.87 278.34 119.21 298.69 119.21 301.35 121.87" />
            <polygon class="cls-3" points="305.36 125.89 278.34 125.89 278.34 123.23 302.7 123.23 305.36 125.89" />
        </svg>
    </div>
</body>
</html>