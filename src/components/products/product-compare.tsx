'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Product } from '@/types/product'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  Plus, 
  Check, 
  Minus, 
  Star,
  ArrowLeft,
  Eye,
  ShoppingCart
} from 'lucide-react'
import { useProductsTranslations } from '@/hooks/use-translations'

interface ProductCompareProps {
  products: Product[]
  onRemoveProduct?: (productId: string) => void
  onAddProduct?: () => void
  maxProducts?: number
}

export function ProductCompare({
  products,
  onRemoveProduct,
  onAddProduct,
  maxProducts = 4
}: ProductCompareProps) {
  const { t } = useProductsTranslations()
  // 定义规格对比项目
  const specItems = [
    { key: 'category', label: t('compare.specs.category'), getValue: (product: Product) => product.category?.replace(/-/g, ' ') || '-' },
    { key: 'cpuLeiXing', label: t('compare.specs.cpu'), getValue: (product: Product) => product.cpuLeiXing || '-' },
    { key: 'neiCun', label: t('compare.specs.memory'), getValue: (product: Product) => product.neiCun || '-' },
    // { key: 'cunChu', label: t('compare.specs.storage'), getValue: (product: Product) => product.cunChu || '-' },
    { key: 'wangKa', label: t('compare.specs.network'), getValue: (product: Product) => product.wangKa || '-' },
    { key: 'xianShiJieKou', label: t('compare.specs.display'), getValue: (product: Product) => product.xianShiJieKou || '-' },
    { key: 'powerType', label: t('compare.specs.power'), getValue: (product: Product) => product.powerType || '-' },
    { key: 'operating_system', label: t('compare.specs.os'), getValue: (product: Product) => product.operating_system || '-' },
    { key: 'operating_temperature', label: t('compare.specs.temperature'), getValue: (product: Product) => product.operating_temperature || '-' },
    // { key: 'dimensions', label: t('compare.specs.dimensions'), getValue: (product: Product) => product.dimensions || '-' },
    // { key: 'weight', label: t('compare.specs.weight'), getValue: (product: Product) => product.weight || '-' }
  ]

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📊</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('compare.noProducts')}</h3>
        <p className="text-gray-600 mb-6">
          {t('compare.selectProducts')}
        </p>
        <Button onClick={onAddProduct}>
          <Plus className="w-4 h-4 mr-2" />
          {t('compare.addProduct')}
        </Button>
      </div>
    )
  }

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-1">{t('compare.title')}</h1>
              <p className="text-gray-600">
                {t('compare.description', { count: products.length })}
              </p>
            </div>
            <Button variant="outline" asChild>
              <Link href="/products" className="flex items-center">
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t('compare.backToProducts')}
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">


        {/* Comparison Table */}
         <div className="bg-white rounded-lg shadow-sm overflow-hidden">
           <div className="flex">
             {/* Left Sidebar - Spec Items */}
             <div className="w-64 bg-blue-600 text-white">
               {/* Product Row Header */}
               <div className="p-4 border-b border-blue-500">
                 <h3 className="font-semibold text-lg">{t('compare.products')}</h3>
               </div>
               <div className="p-4 border-b border-blue-500">
                 <h3 className="font-semibold text-lg">{t('compare.specifications')}</h3>
               </div>
               <div className="divide-y divide-blue-500">
                 {specItems.map((item) => (
                   <div key={item.key} className="p-4 hover:bg-blue-700 transition-colors">
                     <div className="font-medium text-sm">{item.label}</div>
                   </div>
                 ))}
               </div>
             </div>

             {/* Right Content - Product Comparison */}
             <div className="flex-1 overflow-x-auto">
               {/* Product Row */}
               <div className="flex border-b border-gray-200 bg-gray-50">
                 {products.map((product) => (
                   <div key={product.id} className="flex-1 min-w-48 p-4 text-center border-r border-gray-200 last:border-r-0 relative">
                     {onRemoveProduct && (
                       <Button
                         variant="ghost"
                         size="sm"
                         className="absolute top-2 right-2 z-10 p-1 h-auto bg-white shadow-sm hover:bg-gray-100"
                         onClick={() => onRemoveProduct(product.id)}
                       >
                         <X className="w-3 h-3" />
                       </Button>
                     )}
                     
                     {/* Product Image */}
                     <div className="w-20 h-20 bg-white mb-2 overflow-hidden rounded-lg border mx-auto">
                        {product.images && product.images.length > 0 ? (
                          <Image
                            src={product.images[0].url}
                            alt={product.images[0].alt}
                            width={80}
                            height={80}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <div className="text-2xl">📦</div>
                          </div>
                        )}
                      </div>
                      
                      <div className="font-semibold text-gray-900 text-sm mb-2">{product.name}</div>
                      
                      <Button asChild size="sm" className="w-full text-xs py-1">
                        <Link href={`/products/${product.slug}`}>
                          <Eye className="w-3 h-3 mr-1" />
                          {t('compare.viewDetails')}
                        </Link>
                      </Button>
                   </div>
                 ))}
                 
                 {/* Add Product Cell */}
                  {products.length < maxProducts && onAddProduct && (
                    <div className="flex-1 min-w-48 p-4 text-center border-r border-gray-200 last:border-r-0">
                      <div className="flex flex-col items-center justify-center h-full min-h-[100px]">
                        <Button
                          variant="ghost"
                          onClick={onAddProduct}
                          className="flex flex-col items-center gap-1"
                        >
                          <Plus className="w-5 h-5 text-gray-400" />
                          <span className="text-gray-600 text-xs">{t('compare.addProduct')}</span>
                        </Button>
                      </div>
                    </div>
                  )}
               </div>

               {/* Section Header */}
               <div className="flex border-b border-gray-200 bg-blue-50">
                 {Array.from({ length: products.length + (products.length < maxProducts && onAddProduct ? 1 : 0) }).map((_, index) => (
                   <div key={index} className="flex-1 min-w-48 p-3 text-center border-r border-gray-200 last:border-r-0">
                     <div className="font-medium text-gray-700 text-sm">{t('compare.specifications')}</div>
                   </div>
                 ))}
               </div>

               {/* Spec Rows */}
               <div className="divide-y divide-gray-200">
                 {specItems.map((item, index) => (
                   <div key={item.key} className={`flex ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
                     {products.map((product) => (
                       <div key={product.id} className="flex-1 min-w-48 p-4 text-center border-r border-gray-200 last:border-r-0">
                         <div className="text-sm text-gray-700">
                           {item.getValue(product)}
                         </div>
                       </div>
                     ))}
                     
                     {/* Empty cell for add product column */}
                     {products.length < maxProducts && onAddProduct && (
                       <div className="flex-1 min-w-48 p-4 text-center border-r border-gray-200 last:border-r-0">
                         <div className="text-sm text-gray-400">-</div>
                       </div>
                     )}
                   </div>
                 ))}
               </div>
             </div>
           </div>
         </div>
      </div>
    </div>
  )
}
