'use client'

import React, { useState, useEffect, useRef, useMemo } from 'react'
import { useProductsTranslations } from '@/hooks/use-translations'
import { 
  FileText, 
  Monitor, 
  Download, 
  Cpu,
  HardDrive,
  Zap,
  Thermometer,
  Wifi,
  Package,
  Settings,
  Image as ImageIcon,
  Share2,
  ChevronLeft,
  ChevronRight,
  MemoryStick,
  Database,
  Network,
  Power,
  Gauge,
  Usb,
  Server,
  HardDriveIcon
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import Link from 'next/link'
import type { Product, ProductDownload } from '@/types/product'
import RichTextRenderer from '@/components/RichTextRenderer'

interface ModernProductDetailProps {
  product: Product
  detailTabs: {
    description?: string | Record<string, any>
    specifications?: string | Record<string, any>
    compatibility?: string | string[] | Record<string, any>
  }
  downloads: ProductDownload[]
}

type TabType = 'images' | 'specifications' | 'applications' | 'downloads'

// 产品图片处理
const getProductImages = (product: Product) => {
  if (product.images && product.images.length > 0) {
    return product.images.map(img => img.url)
  }
  
  return [
    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop'
  ]
}

// 产品规格处理
const getProductSpecifications = (product: Product, t: any) => {
  const specs: Array<{ label: string; value: string; icon: any }> = []
  
  product.specifications?.forEach(category => {
    category.items.forEach(item => {
      let icon = Monitor
      
      const label = item.label.toLowerCase()
      // CPU 相关
      if (label.includes('cpu') || label.includes('processor') || label.includes('芯片')) icon = Cpu
      // 内存相关
      else if (label.includes('memory') || label.includes('ram') || label.includes('内存')) icon = MemoryStick
      // 存储相关
      else if (label.includes('storage') || label.includes('disk') || label.includes('ssd') || label.includes('存储')) icon = Database
      // 网络相关
      else if (label.includes('network') || label.includes('ethernet') || label.includes('网络') || label.includes('网卡')) icon = Network
      // 电源相关
      else if (label.includes('power') || label.includes('voltage') || label.includes('电源') || label.includes('功耗')) icon = Power
      // 操作系统
      else if (label.includes('os') || label.includes('system') || label.includes('操作系统')) icon = Package
      // 温度相关
      else if (label.includes('temperature') || label.includes('temp') || label.includes('温度')) icon = Thermometer
      // 显示接口
      else if (label.includes('display') || label.includes('video') || label.includes('hdmi') || label.includes('vga') || label.includes('显示')) icon = Monitor
      // USB接口
      else if (label.includes('usb') || label.includes('接口')) icon = Usb
      // 性能相关
      else if (label.includes('performance') || label.includes('speed') || label.includes('频率') || label.includes('性能')) icon = Gauge
      // 服务器相关
      else if (label.includes('server') || label.includes('服务器')) icon = Server
      
      specs.push({
        label: item.label,
        value: item.unit ? `${item.value} ${item.unit}` : item.value,
        icon
      })
    })
  })
  
  if (specs.length === 0) {
    return [
      { label: t('detail.specs.cpu'), value: 'Intel Core i5', icon: Cpu },
      { label: t('detail.specs.memory'), value: '8GB DDR4', icon: MemoryStick },
      { label: t('detail.specs.storage'), value: '256GB SSD', icon: Database },
      { label: t('detail.specs.network'), value: 'Gigabit Ethernet', icon: Network },
      { label: t('detail.specs.power'), value: 'DC 12V-24V', icon: Power },
      { label: t('detail.specs.os'), value: 'Windows/Linux', icon: Package },
      { label: t('detail.specs.temperature'), value: '-20°C ~ +70°C', icon: Thermometer },
    ]
  }
  
  return specs
}

// 应用场景数据
const getApplications = (t: any) => [
  {
    title: t('detail.applications.smartManufacturing'),
    description: t('detail.applications.smartManufacturingDesc'),
    image: 'https://images.unsplash.com/photo-1565106430482-8f6e74349ca1?w=400&h=300&fit=crop'
  },
  {
    title: t('detail.applications.machineVision'),
    description: t('detail.applications.machineVisionDesc'),
    image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop'
  },
  {
    title: t('detail.applications.edgeComputing'),
    description: t('detail.applications.edgeComputingDesc'),
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop'
  }
]

const ModernProductDetail: React.FC<ModernProductDetailProps> = ({ product, detailTabs, downloads }) => {
  const { t } = useProductsTranslations()
  const [activeTab, setActiveTab] = useState<TabType>('images')
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const [showMagnifier, setShowMagnifier] = useState(false)
  const [magnifierPosition, setMagnifierPosition] = useState({ x: 0, y: 0 })
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 })
  
  const imageRef = useRef<HTMLImageElement>(null)
  
  // 获取产品数据
  const productImages = useMemo(() => getProductImages(product), [product])
  const specifications = useMemo(() => getProductSpecifications(product, t), [product, t])
  const applications = useMemo(() => getApplications(t), [t])

  // 检测移动设备
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const tabs = [
    { id: 'images', label: t('detail.tabs.productDetails'), icon: ImageIcon },
    { id: 'specifications', label: t('detail.tabs.specifications'), icon: Monitor },
    { id: 'applications', label: t('detail.tabs.applications'), icon: Settings },
    { id: 'downloads', label: t('detail.tabs.downloads'), icon: Download }
  ]

  // 图片切换
  const handleImageChange = (index: number) => {
    if (index !== selectedImageIndex) {
      setSelectedImageIndex(index)
      if (isMobile && 'vibrate' in navigator) {
        navigator.vibrate(30)
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 面包屑导航 */}
      <div className="bg-white">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-primary">{t('detail.breadcrumb.home')}</Link>
            <span>/</span>
            <Link href="/products" className="hover:text-primary">{t('detail.breadcrumb.products')}</Link>
            <span>/</span>
            <span className="text-gray-900">{product.name}</span>
          </nav>
        </div>
      </div>

      {/* 产品头部区域 */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 产品图片 */}
            <div>
              {/* 主图片 */}
              <div className="mb-4 relative">
                <div
                  className={`relative overflow-hidden shadow-lg ${!isMobile ? 'cursor-crosshair' : ''}`}
                  style={{ aspectRatio: '4/3' }}
                  onMouseEnter={() => !isMobile && setShowMagnifier(true)}
                  onMouseLeave={() => !isMobile && setShowMagnifier(false)}
                  onMouseMove={(e) => {
                    if (!imageRef.current || isMobile) return

                    const rect = imageRef.current.getBoundingClientRect()
                    const x = e.clientX - rect.left
                    const y = e.clientY - rect.top

                    const imgNaturalWidth = imageRef.current.naturalWidth
                    const imgNaturalHeight = imageRef.current.naturalHeight
                    const imgDisplayWidth = rect.width
                    const imgDisplayHeight = rect.height

                    const scaleX = imgNaturalWidth / imgDisplayWidth
                    const scaleY = imgNaturalHeight / imgDisplayHeight

                    setMagnifierPosition({ x, y })
                    setImagePosition({ x: x * scaleX, y: y * scaleY })
                  }}
                >
                  {/* 骨架屏背景 */}
                  <div className="absolute inset-0 bg-gray-200">
                    <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"></div>
                  </div>

                  {/* 主图片 */}
                  <img
                    ref={imageRef}
                    src={productImages[selectedImageIndex]}
                    alt={product.name}
                    className="absolute inset-0 w-full h-full object-cover"
                    style={{
                      opacity: '1',
                      transition: 'opacity 0.2s ease-out',
                      backfaceVisibility: 'hidden',
                      WebkitBackfaceVisibility: 'hidden',
                      transform: 'translateZ(0)'
                    }}
                    loading="eager"
                    decoding="async"
                  />

                  {/* 放大镜 - 仅在桌面端显示 */}
                  {showMagnifier && !isMobile && (
                    <div
                      className="absolute pointer-events-none border-2 border-white shadow-lg"
                      style={{
                        left: magnifierPosition.x - 75,
                        top: magnifierPosition.y - 75,
                        width: '150px',
                        height: '150px',
                        borderRadius: '50%',
                        backgroundImage: `url(${productImages[selectedImageIndex]})`,
                        backgroundSize: `${imageRef.current?.naturalWidth || 0}px ${imageRef.current?.naturalHeight || 0}px`,
                        backgroundPosition: `-${imagePosition.x - 75}px -${imagePosition.y - 75}px`,
                        backgroundRepeat: 'no-repeat',
                        transform: 'scale(2) translateZ(0)',
                        transformOrigin: 'center',
                        zIndex: 20,
                        willChange: 'transform'
                      }}
                    />
                  )}
                </div>
              </div>

              {/* 缩略图 */}
              {productImages.length > 1 && (
                <div className={`flex space-x-2 overflow-x-auto ${isMobile ? 'pb-2' : ''} scrollbar-hide`}>
                  {productImages.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => handleImageChange(index)}
                      className={`flex-shrink-0 ${isMobile ? 'w-16 h-16' : 'w-20 h-20'} overflow-hidden border-2 ${
                        index === selectedImageIndex
                          ? 'border-primary shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                      } transition-all duration-200 cursor-pointer relative`}
                      style={{ aspectRatio: '1/1' }}
                    >
                      <img
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        className="absolute inset-0 w-full h-full object-cover"
                        loading={index < 3 ? "eager" : "lazy"}
                        decoding="async"
                      />

                      {/* 选中状态指示器 */}
                      {index === selectedImageIndex && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="w-4 h-4 bg-primary flex items-center justify-center">
                            <div className="w-2 h-2 bg-white"></div>
                          </div>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* 产品信息 */}
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{product.name}</h1>

              {/* 产品描述 */}
              <div className="mb-8">
                <div className="text-lg text-gray-600 leading-relaxed">
                  {product.shortDescription}
                </div>
              </div>

              {/* 核心规格 */}
              <div className="bg-muted/50 p-4 mb-8 rounded-lg">
                <h3 className="text-lg font-semibold text-foreground mb-3">{t('detail.coreSpecs')}</h3>
                <div className="space-y-2">
                  {specifications.slice(0, isMobile ? 4 : 6).map((spec, index) => {
                    const IconComponent = spec.icon || Cpu
                    return (
                      <div key={index} className="flex items-center bg-card p-2 rounded-md shadow-sm hover:shadow-md transition-shadow border border-border">
                        <div className="w-6 h-6 flex-shrink-0 mr-3 bg-primary/10 flex items-center justify-center rounded">
                          <IconComponent className="w-4 h-4 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0 flex items-center justify-between">
                          <span className="text-sm text-muted-foreground mr-2">{spec.label}:</span>
                          <span className={`font-medium text-foreground ${isMobile ? 'text-sm break-words' : 'text-sm'}`}>{spec.value}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild className="flex-1 bg-primary hover:bg-primary/90">
                  <Link href="/support">
                    {t('detail.requestSample')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="flex-1">
                  <Link href="/contact">
                    {t('detail.technicalConsult')}
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 标签导航 */}
      <section className="sticky top-[64px] z-40 w-full bg-white shadow-lg">
        <div className="w-full">
          <div className="max-w-none">
            <div className="grid grid-cols-4 h-14">
              {tabs.map((tab) => {
                const IconComponent = tab.icon || ImageIcon
                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id as TabType)
                      const targetSection = document.querySelector(`#${tab.id}`)
                      if (targetSection) {
                        const offsetTop = targetSection.getBoundingClientRect().top + window.scrollY
                        const mainNavHeight = 64
                        const tabNavHeight = 56
                        const scrollTo = offsetTop - mainNavHeight - tabNavHeight - 16
                        window.scrollTo({
                          top: scrollTo,
                          behavior: 'smooth'
                        })
                      }
                    }}
                    className={`flex items-center justify-center font-medium transition-all duration-200 relative ${isMobile ? 'text-xs' : 'text-sm'} ${
                      activeTab === tab.id
                        ? 'text-white bg-primary'
                        : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <div className="flex items-center">
                      <IconComponent className={`${isMobile ? 'w-3 h-3 mr-1' : 'w-4 h-4 mr-2'}`} />
                      <span className="font-semibold">{tab.label}</span>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      </section>

      {/* 标签内容区域 */}
      <section id="tab-content" className="py-0">
        <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8">
          {/* 产品详情区域 */}
          <div id="images" className="scroll-mt-16">
            <div>
              <div className="text-primary py-6 px-6">
                <h2 className="text-2xl font-bold text-center">{t('detail.tabs.productDetails')}</h2>
              </div>
              {detailTabs.description ? (
                <div className="bg-white overflow-hidden">
                  <div className="prose prose-lg max-w-none p-6">
                    {typeof detailTabs.description === 'string' ? (
                      <RichTextRenderer
                        content={detailTabs.description}
                        variant="default"
                        className=""
                      />
                    ) : (
                      <div className="space-y-4">
                        {Object.entries(detailTabs.description).map(([key, value]) => (
                          <div key={key}>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">{key}</h3>
                            <div className="text-gray-700">
                              {typeof value === 'string' ? (
                                <RichTextRenderer
                                  content={value}
                                  variant="default"
                                  className=""
                                />
                              ) : (
                                <pre className="whitespace-pre-wrap">{JSON.stringify(value, null, 2)}</pre>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-white overflow-hidden p-6">
                  <div className="text-center text-gray-600">
                    <p className="text-lg mb-4">{t('detail.productDetailsPlaceholder')}</p>
                    <p className="text-sm">{t('detail.productDetailsNote')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 技术规格区域 */}
          <div id="specifications" className="scroll-mt-32">
            <div className="bg-white overflow-hidden"
            >
              <div className="text-primary py-4 px-6">
                <h2 className="text-2xl font-bold text-center">{t('detail.tabs.specifications')}</h2>
              </div>
              <div className="p-6">
                {detailTabs.specifications ? (
                  <div className="prose prose-lg max-w-none">
                    {typeof detailTabs.specifications === 'string' ? (
                      <RichTextRenderer
                        content={detailTabs.specifications}
                        variant="default"
                        className=""
                      />
                    ) : (
                      <div className="space-y-4">
                        {Object.entries(detailTabs.specifications).map(([key, value]) => (
                          <div key={key}>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">{key}</h3>
                            <div className="text-gray-700">
                              {typeof value === 'string' ? (
                                <RichTextRenderer
                                  content={value}
                                  variant="default"
                                  className=""
                                />
                              ) : (
                                <pre className="whitespace-pre-wrap">{JSON.stringify(value, null, 2)}</pre>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'}`}>
                    {specifications.map((spec, index) => {
                      const IconComponent = spec.icon || Cpu
                      return (
                        <div key={index} className="flex items-center p-4 border border-gray-200 hover:border-primary/30 hover:shadow-md transition-all">
                          <div className="w-8 h-8 flex-shrink-0 mr-4 bg-primary/10 flex items-center justify-center">
                            <IconComponent className="w-5 h-5 text-primary" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm text-gray-500 mb-1">{spec.label}</div>
                            <div className="font-semibold text-gray-900 text-base">{spec.value}</div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 应用场景区域 */}
          <div id="applications" className="scroll-mt-32">
            <div>
              <div className="text-primary py-4 px-6">
                <h2 className="text-2xl font-bold text-center">{t('detail.tabs.applications')}</h2>
              </div>

              <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-3'}`}>
                {applications.map((app, index) => (
                  <div key={index} className="bg-white overflow-hidden shadow-sm">
                    <div className="relative">
                      <img
                        src={app.image}
                        alt={app.title}
                        className={`w-full object-cover ${isMobile ? 'h-40' : 'h-48'}`}
                        loading="eager"
                      />
                    </div>
                    <div className={`${isMobile ? 'p-4' : 'p-6'}`}>
                      <h4 className={`font-semibold text-gray-900 mb-3 ${isMobile ? 'text-lg' : 'text-xl'}`}>{app.title}</h4>
                      <p className="text-gray-600">{app.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 下载区域 */}
          <div id="downloads" className="scroll-mt-32">
            <div className="bg-white overflow-hidden">
              <div className="text-primary py-4 px-6">
                <h2 className="text-2xl font-bold text-center">{t('detail.tabs.downloads')}</h2>
              </div>
              <div className="p-6">
                {downloads && downloads.length > 0 ? (
                  <div className="space-y-4">
                    {downloads.map((file, index) => (
                      <div key={file.id || index} className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-primary/10 flex items-center justify-center mr-4">
                            <FileText className="w-6 h-6 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{file.title}</h4>
                            <p className="text-sm text-gray-600">
                              {file.type} · {file.version} · {file.fileSize}
                            </p>
                          </div>
                        </div>
                        <Button asChild className="bg-primary hover:bg-primary/90">
                          <a href={file.fileUrl} download className="flex items-center">
                            <Download className="w-4 h-4 mr-2" />
                            {t('detail.download')}
                          </a>
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-500 mb-2">{t('detail.noDownloads')}</h4>
                    <p className="text-gray-400">{t('detail.noDownloadsDesc')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关产品CTA */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-gradient-to-r from-primary to-primary/80 p-8 text-white">
            <h2 className="text-3xl font-bold mb-4">{t('detail.needMoreInfo')}</h2>
            <p className="text-xl mb-6 text-primary-foreground/80">
              {t('detail.expertSupport')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild className="bg-white text-primary hover:bg-gray-100">
                <Link href="/support">
                  {t('detail.requestSample')}
                </Link>
              </Button>
              <Button asChild variant="outline" className="bg-white/20 text-white border-white hover:bg-white hover:text-primary">
                <Link href="/products">
                  {t('detail.viewMoreProducts')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default ModernProductDetail
