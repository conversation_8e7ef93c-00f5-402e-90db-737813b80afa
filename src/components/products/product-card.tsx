'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { OptimizedImage } from '@/components/ui/optimized-image'
import { useImageOptimization } from '@/components/providers/image-optimization-provider'
import { Product } from '@/types/product'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Eye } from 'lucide-react'
import { useProductsTranslations } from '@/hooks/use-translations'

interface ProductCardProps {
  product: Product
  showCompareButton?: boolean
  onCompare?: (product: Product) => void
  isSelected?: boolean
  onSelect?: () => void
  maxSelected?: boolean
}

export function ProductCard({ product, showCompareButton = false, onCompare, isSelected = false, onSelect, maxSelected = false }: ProductCardProps) {
  const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]
  const secondImage = product.images?.[1]
  const [currentImage, setCurrentImage] = useState(mainImage)
  const [isImageLoading, setIsImageLoading] = useState(false)
  const { preloadImage } = useImageOptimization()
  const { t } = useProductsTranslations()

  // Handle hover preloading and image switching
  const handleMouseEnter = () => {
    if (secondImage?.url) {
      setIsImageLoading(true)
      preloadImage(secondImage.url)
        .then(() => {
          setCurrentImage(secondImage)
          setTimeout(() => setIsImageLoading(false), 100)
        })
        .catch(() => {
          setCurrentImage(secondImage)
          setIsImageLoading(false)
        })
    }
  }

  // Handle mouse leave - restore main image
  const handleMouseLeave = () => {
    setIsImageLoading(true)
    setCurrentImage(mainImage)
    setTimeout(() => setIsImageLoading(false), 100)
  }

  // Handle product selection
  const handleSelect = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (onSelect && (!maxSelected || isSelected)) {
      onSelect()
    }
  }


  return (
    <Card
      className={`group hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-300 border flex flex-col h-full bg-gradient-to-br from-white via-gray-50/30 to-blue-50/20 hover:from-blue-50/20 hover:via-white hover:to-indigo-50/20 overflow-hidden backdrop-blur-sm cursor-pointer ${
        isSelected 
          ? 'border-blue-500 shadow-lg ring-2 ring-blue-200' 
          : 'border-gray-200/50 hover:border-blue-300/50'
      } ${
        maxSelected && !isSelected ? 'opacity-50' : ''
      }`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}

    >
      {/* Product Image */}
      <CardHeader className="p-3 pb-2">
        <div className="relative overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-inner">
          {/* Selection indicator */}
          {isSelected && (
            <div className="absolute top-1.5 right-1.5 z-10">
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          )}
          <div className="aspect-[3/2] relative">
            {currentImage ? (
              <Image
                src={currentImage.url}
                alt={currentImage.alt}
                fill
                className={`object-cover group-hover:scale-110 transition-all duration-500 ease-in-out transform ${
                  isImageLoading ? 'opacity-70' : 'opacity-100'
                }`}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={false}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <div className="text-4xl mb-2">📱</div>
                  <div className="text-sm">{t("page.noImage")}</div>
                </div>
              </div>
            )}
          </div>

          {/* Badges */}
          {(product.isNew || product.isFeatured) && (
            <div className="absolute top-2 right-2 flex flex-col gap-1.5">
              {product.isNew && (
                <Badge className="bg-gradient-to-r from-green-500 to-green-600 text-white text-xs px-2 py-1 shadow-lg border-0 font-medium">
                  {t("card.new")}
                </Badge>
              )}
              {product.isFeatured && (
                <Badge className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-2 py-1 shadow-lg border-0 font-medium">
                  {t("card.featured")}
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      {/* Product Info */}
      <CardContent className="p-3 pt-1.5 flex-1 flex flex-col">
        {/* Product Name */}
        <div className="mb-3">
          <h3 className="text-heading-5 text-gray-900 group-hover:text-blue-700 transition-all duration-300 leading-tight">
            <Link
              href={`/products/${product.slug}`}
              className="block relative"
            >
              <span className="bg-gradient-to-r from-gray-900 to-gray-700 group-hover:from-blue-700 group-hover:to-indigo-700 bg-clip-text text-transparent transition-all duration-300">
                {product.name}
              </span>
            </Link>
          </h3>
          <div className="w-12 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 mt-2 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
        </div>

        {/* Product Technical Specifications */}
        <div className="flex-1">
          <div className="p-1.5 space-y-1 transition-all duration-300">
            {/* CPU类型 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="text-body-small text-gray-800 truncate">
                {product.cpuLeiXing || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 内存 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.neiCun || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 网卡 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.wangKa || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 显示接口 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.xianShiJieKou || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 电源类型 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.powerType || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 操作系统 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.operating_system || <span className="text-gray-400">-</span>}
              </span>
            </div>

            {/* 工作温度 */}
            <div className="flex items-center py-0.5 hover:bg-white/50 px-2 transition-colors duration-200">
              <span className="font-semibold text-gray-800 text-xs truncate">
                {product.operating_temperature || <span className="text-gray-400">-</span>}
              </span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Action Buttons */}
      <CardFooter className="p-3 pt-0 flex gap-2" onClick={(e) => e.stopPropagation()}>
        <Button
          asChild
          className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold transition-all duration-200 border-0"
        >
          <Link href={`/products/${product.slug}`} className="flex items-center justify-center gap-1.5 py-2">
            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span className="text-xs">{t("card.details")}</span>
          </Link>
        </Button>

        {showCompareButton ? (
          <Button 
            variant="default" 
            className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold transition-all duration-200 border-0 py-2"
            data-select-button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onCompare?.(product)
            }}
          >
            <svg className="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span className="text-xs">{t("card.compare")}</span>
          </Button>
        ) : (
          <Button 
            variant={isSelected ? "outline" : "default"}
            className={`flex-1 font-semibold transition-all duration-200 py-2 ${
              isSelected 
                ? 'border-blue-500 text-blue-600 hover:bg-blue-50' 
                : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white border-0'
            } ${
              maxSelected && !isSelected ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            data-select-button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (!maxSelected || isSelected) {
                onSelect?.()
              }
            }}
            disabled={maxSelected && !isSelected}
          >
            <svg className="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span className="text-xs">{isSelected ? t('card.selected') : t('card.selectToCompare')}</span>
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
