'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Product } from '@/types/product'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { X, Plus } from 'lucide-react'

interface ProductComparisonProps {
  products: Product[]
  allProducts: Product[]
  onProductsChange: (products: Product[]) => void
}

export function ProductComparison({ products, allProducts, onProductsChange }: ProductComparisonProps) {
  const [availableProducts] = useState(allProducts)
  const maxProducts = 3

  const addProduct = (productId: string) => {
    const product = availableProducts.find(p => p.id === productId)
    if (product && !products.find(p => p.id === productId) && products.length < maxProducts) {
      onProductsChange([...products, product])
    }
  }

  const removeProduct = (productId: string) => {
    onProductsChange(products.filter(p => p.id !== productId))
  }

  const getComparisonData = () => {
    if (products.length === 0) return []

    const allSpecs = new Set<string>()
    products.forEach(product => {
      product.specifications.forEach(spec => {
        spec.items.forEach(item => {
          allSpecs.add(`${spec.category}:${item.label}`)
        })
      })
    })

    return Array.from(allSpecs).map(specKey => {
      const [category, label] = specKey.split(':')
      const values = products.map(product => {
        const spec = product.specifications.find(s => s.category === category)
        const item = spec?.items.find(i => i.label === label)
        return item ? `${item.value}${item.unit ? ` ${item.unit}` : ''}` : '-'
      })
      return { category, label, values }
    })
  }

  const comparisonData = getComparisonData()
  const categories = Array.from(new Set(comparisonData.map(item => item.category)))

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">⚖️</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No products selected</h3>
        <p className="text-gray-600 mb-6">
          Select products from the dropdown below to start comparing.
        </p>
        <div className="max-w-md mx-auto">
          <Select onValueChange={addProduct}>
            <SelectTrigger>
              <SelectValue placeholder="Select a product to compare" />
            </SelectTrigger>
            <SelectContent>
              {availableProducts.map(product => (
                <SelectItem key={product.id} value={product.id}>
                  {product.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Product Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Selected Products ({products.length}/{maxProducts})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            {products.map(product => (
              <div key={product.id} className="flex items-center bg-gray-100 p-2">
                <span className="text-sm font-medium">{product.name}</span>
                <button
                  onClick={() => removeProduct(product.id)}
                  className="ml-2 text-gray-500 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
            {products.length < maxProducts && (
              <Select onValueChange={addProduct}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Add product" />
                </SelectTrigger>
                <SelectContent>
                  {availableProducts
                    .filter(p => !products.find(selected => selected.id === p.id))
                    .map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Product Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map(product => {
          const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]

          return (
            <Card key={product.id} className="relative">
              <CardHeader className="p-0">
                <div className="aspect-video bg-gray-100 relative overflow-hidden">
                  {mainImage ? (
                    <Image
                      src={mainImage.url}
                      alt={mainImage.alt}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <div className="text-center">
                        <div className="text-4xl mb-2">📱</div>
                        <div className="text-sm">No image</div>
                      </div>
                    </div>
                  )}
                  
                  {/* Badges */}
                  <div className="absolute top-2 left-2 flex flex-col gap-1">
                    {product.isNew && (
                      <Badge className="bg-green-500 text-white text-xs">New</Badge>
                    )}
                    {product.isFeatured && (
                      <Badge className="bg-orange-500 text-white text-xs">Featured</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                <p className="text-gray-600 text-sm mb-3">{product.shortDescription}</p>
                


                {/* Action Button */}
                <Button asChild className="w-full bg-orange-500 hover:bg-orange-600">
                  <Link href={`/products/${product.slug}`} className="flex items-center justify-center">
                    View Details
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Specifications Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Specifications Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-semibold">Specification</th>
                  {products.map(product => (
                    <th key={product.id} className="text-left p-3 font-semibold min-w-48">
                      {product.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {categories.map(category => (
                  <React.Fragment key={category}>
                    <tr className="bg-gray-50">
                      <td colSpan={products.length + 1} className="p-3 font-semibold text-gray-900">
                        {category}
                      </td>
                    </tr>
                    {comparisonData
                      .filter(item => item.category === category)
                      .map((item, index) => (
                        <tr key={`${category}-${index}`} className="border-b border-gray-100">
                          <td className="p-3 text-gray-600">{item.label}</td>
                          {item.values.map((value, valueIndex) => (
                            <td key={valueIndex} className="p-3 font-medium">
                              {value}
                            </td>
                          ))}
                        </tr>
                      ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
