'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

import { X } from 'lucide-react'

interface ProductFiltersProps {
  categories: Array<{ id: string; name: string; count: number }>
  features: string[]
  selectedCategory: string
  selectedFeatures: string[]
  onCategoryChange: (category: string) => void
  onFeaturesChange: (features: string[]) => void
}

export function ProductFilters({
  categories,
  features,
  selectedCategory,
  selectedFeatures,
  onCategoryChange,
  onFeaturesChange
}: ProductFiltersProps) {

  const handleFeatureChange = (feature: string, checked: boolean) => {
    const newFeatures = checked
      ? [...selectedFeatures, feature]
      : selectedFeatures.filter(f => f !== feature)
    onFeaturesChange(newFeatures)
  }

  const clearFilters = () => {
    onCategoryChange('all')
    onFeaturesChange([])
  }

  return (
    <div className="space-y-6">
      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Categories</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="all-categories"
              checked={selectedCategory === 'all'}
              onCheckedChange={() => onCategoryChange('all')}
            />
            <Label htmlFor="all-categories" className="flex-1 cursor-pointer">
              All Categories
            </Label>
          </div>
          {categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={category.id}
                checked={selectedCategory === category.id}
                onCheckedChange={() => onCategoryChange(category.id)}
              />
              <Label htmlFor={category.id} className="flex-1 cursor-pointer">
                {category.name}
                <span className="text-gray-500 ml-1">({category.count})</span>
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {features.slice(0, 10).map((feature) => (
            <div key={feature} className="flex items-center space-x-2">
              <Checkbox
                id={feature}
                checked={selectedFeatures.includes(feature)}
                onCheckedChange={(checked) => handleFeatureChange(feature, checked as boolean)}
              />
              <Label htmlFor={feature} className="flex-1 cursor-pointer">
                {feature}
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>



      {/* Clear Filters */}
      <Button 
        variant="outline" 
        onClick={clearFilters}
        className="w-full"
      >
        <X className="w-4 h-4 mr-2" />
        Clear All Filters
      </Button>
    </div>
  )
}
