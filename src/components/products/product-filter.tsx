'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
// import { productCategories } from '@/data/products' // 已删除，使用本地定义
import { Cpu, Zap, Package, Monitor, Filter, X } from 'lucide-react'

// 本地产品分类定义
const productCategories = [
  { id: 'single-board-computers', name: 'Single Board Computers', icon: 'Cpu' },
  { id: 'accessories', name: 'Accessories', icon: 'Package' },
  { id: 'displays', name: 'Displays', icon: 'Monitor' },
  { id: 'power', name: 'Power Solutions', icon: 'Zap' }
]

interface ProductFilterProps {
  totalProducts: number
  filteredProducts: number
}

export function ProductFilter({ totalProducts, filteredProducts }: ProductFilterProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all')
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])

  const categoryIcons = {
    'scalable-embedded-series': Cpu,
    'mini-size-series': Zap,
    'universal-embedded-series': Package,
    'all-in-one-ipc': Monitor
  }

  // Common features for filtering
  const commonFeatures = [
    'Intel Celeron processor',
    'Intel Atom processor',
    'Wi-Fi 802.11ac',
    'Wi-Fi 802.11n',
    'Bluetooth 5.0',
    'Bluetooth 4.0',
    'USB 3.0 ports',
    'GPIO pins',
    'Arduino Leonardo compatible',
    'M.2 expansion'
  ]


  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams()

    if (selectedCategory !== 'all') {
      params.set('category', selectedCategory)
    }

    if (selectedFeatures.length > 0) {
      params.set('features', selectedFeatures.join(','))
    }

    const queryString = params.toString()
    const newUrl = queryString ? `/products?${queryString}` : '/products'

    router.push(newUrl, { scroll: false })
  }, [selectedCategory, selectedFeatures, router])

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
  }

  const handleFeatureToggle = (feature: string) => {
    setSelectedFeatures(prev => 
      prev.includes(feature) 
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    )
  }

  const clearAllFilters = () => {
    setSelectedCategory('all')
    setSelectedFeatures([])
  }

  const hasActiveFilters = selectedCategory !== 'all' || selectedFeatures.length > 0

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Filter Products</h3>
            <Badge variant="secondary" className="ml-2">
              {filteredProducts} of {totalProducts} products
            </Badge>
          </div>
          
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="text-gray-600 hover:text-gray-900"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>

        <div className="space-y-6">
          {/* Category Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Product Category</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('all')}
                className="text-sm"
              >
                All Categories
              </Button>
              {productCategories.map((category) => {
                const IconComponent = categoryIcons[category.id as keyof typeof categoryIcons]
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleCategoryChange(category.id)}
                    className="text-sm flex items-center gap-1"
                  >
                    {IconComponent && <IconComponent className="h-3 w-3" />}
                    {category.name}
                  </Button>
                )
              })}
            </div>
          </div>
          
        </div>
      </CardContent>
    </Card>
  )
}
