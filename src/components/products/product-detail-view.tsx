'use client'

import { Product } from '@/types/product'
import ModernProductDetail from './modern-product-detail'
import { useProductsTranslations } from '@/hooks/use-translations'

interface ProductDetailViewProps {
  product: Product
}

export function ProductDetailView({ product }: ProductDetailViewProps) {
  // 转换下载链接格式
  const downloads = (product.downloadLinks || []).map((link, index) => ({
    id: `download-${index}`,
    title: link.name,
    description: `${link.type} file for ${product.name}`,
    type: link.type,
    fileUrl: link.url,
    fileName: link.name,
    fileSize: link.size || 'Unknown size',
    version: link.version || '1.0',
    releaseDate: new Date().toISOString(),
    platform: link.platform
  }))

  return (
    <ModernProductDetail
      product={product}
      detailTabs={product.detailTabs || {
        description: product.description || product.shortDescription,
        specifications: product.specifications ?
          product.specifications.map(spec =>
            spec.items.map(item => `${item.label}: ${item.value}${item.unit ? ' ' + item.unit : ''}`).join('\n')
          ).join('\n\n') :
          'No specifications available',
        compatibility: product.compatibility || []
      }}
      downloads={downloads}
    />
  )
}
