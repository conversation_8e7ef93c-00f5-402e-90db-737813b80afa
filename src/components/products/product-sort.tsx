'use client'

import { ProductSort as ProductSortType } from '@/types/product'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ProductSortProps {
  sort: ProductSortType
  onSortChange: (sort: ProductSortType) => void
}

const sortOptions = [
  { value: 'name-asc', label: 'Name (A-Z)', field: 'name' as const, direction: 'asc' as const },
  { value: 'name-desc', label: 'Name (Z-A)', field: 'name' as const, direction: 'desc' as const },
  { value: 'releaseDate-desc', label: 'Newest First', field: 'releaseDate' as const, direction: 'desc' as const },
  { value: 'releaseDate-asc', label: 'Oldest First', field: 'releaseDate' as const, direction: 'asc' as const },
  { value: 'popularity-desc', label: 'Most Popular', field: 'popularity' as const, direction: 'desc' as const },
]

export function ProductSort({ sort, onSortChange }: ProductSortProps) {
  const currentValue = `${sort.field}-${sort.direction}`

  const handleSortChange = (value: string) => {
    const option = sortOptions.find(opt => opt.value === value)
    if (option) {
      onSortChange({
        field: option.field,
        direction: option.direction
      })
    }
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 whitespace-nowrap">Sort by:</span>
      <Select value={currentValue} onValueChange={handleSortChange}>
        <SelectTrigger className="w-48">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
