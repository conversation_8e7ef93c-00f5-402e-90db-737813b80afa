'use client'

import React, { useState, useEffect, useRef, useCallback, memo, useMemo } from 'react'
import { useProductsTranslations } from '@/hooks/use-translations'
import {
  FileText,
  Monitor,
  Download,
  Cpu,
  HardDrive,
  Zap,
  Thermometer,
  Wifi,
  Package,
  Settings,
  Image as ImageIcon,
  ChevronLeft,
  ChevronRight,
  Share2,
  Laptop
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import type { Product, ProductDownload } from '@/types/product'

interface ProductDetailTabsProps {
  product: Product
  detailTabs: {
    description?: string | Record<string, any>
    specifications?: string | Record<string, any>
    compatibility?: string | string[] | Record<string, any>
  }
  downloads: ProductDownload[]
}

type TabType = 'images' | 'specifications' | 'applications' | 'downloads' | 'description' | 'compatibility'

const HEADER_HEIGHT = 80
const SCROLL_OFFSET = 20

// 产品图片处理
const getProductImages = (product: Product) => {
  if (product.images && product.images.length > 0) {
    return product.images.map(img => img.url)
  }

  return [
    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop'
  ]
}

// 产品规格处理
const getProductSpecifications = (product: Product, t: any) => {
  const specs: Array<{ label: string; value: string; icon: any }> = []

  // 从产品规格中提取信息
  product.specifications?.forEach(category => {
    category.items.forEach(item => {
      let icon = Monitor // 默认图标

      // 根据标签选择合适的图标
      const label = item.label.toLowerCase()
      if (label.includes('cpu') || label.includes('processor')) icon = Cpu
      else if (label.includes('memory') || label.includes('ram')) icon = HardDrive
      else if (label.includes('storage') || label.includes('disk')) icon = HardDrive
      else if (label.includes('network') || label.includes('ethernet')) icon = Wifi
      else if (label.includes('power') || label.includes('voltage')) icon = Zap
      else if (label.includes('os') || label.includes('system')) icon = Package
      else if (label.includes('temperature') || label.includes('temp')) icon = Thermometer

      specs.push({
        label: item.label,
        value: item.unit ? `${item.value} ${item.unit}` : item.value,
        icon
      })
    })
  })

  // 如果没有规格数据，返回默认规格
  if (specs.length === 0) {
    return [
      { label: t('detail.specs.cpu'), value: 'Intel Core i5', icon: Cpu },
      { label: t('detail.specs.memory'), value: '8GB DDR4', icon: HardDrive },
      { label: t('detail.specs.storage'), value: '256GB SSD', icon: HardDrive },
      { label: t('detail.specs.network'), value: 'Gigabit Ethernet', icon: Wifi },
      { label: t('detail.specs.power'), value: 'DC 12V-24V', icon: Zap },
      { label: t('detail.specs.os'), value: 'Windows/Linux', icon: Package },
      { label: t('detail.specs.temperature'), value: '-20°C ~ +70°C', icon: Thermometer },
    ]
  }

  return specs
}

// 应用场景数据
const getApplications = (t: any) => [
  {
    title: t('detail.applications.smartManufacturing'),
    description: t('detail.applications.smartManufacturingDesc'),
    image: 'https://images.unsplash.com/photo-1565106430482-8f6e74349ca1?w=400&h=300&fit=crop'
  },
  {
    title: t('detail.applications.machineVision'),
    description: t('detail.applications.machineVisionDesc'),
    image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop'
  },
  {
    title: t('detail.applications.edgeComputing'),
    description: t('detail.applications.edgeComputingDesc'),
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop'
  }
]

const downloadTypeColors = {
  driver: 'bg-blue-100 text-blue-800 border-blue-200',
  software: 'bg-green-100 text-green-800 border-green-200',
  firmware: 'bg-purple-100 text-purple-800 border-purple-200',
  manual: 'bg-orange-100 text-orange-800 border-orange-200',
  datasheet: 'bg-gray-100 text-gray-800 border-gray-200',
  other: 'bg-gray-100 text-gray-800 border-gray-200'
} as const

const ProductDetailTabs = memo(({ product, detailTabs, downloads }: ProductDetailTabsProps) => {
  const { t } = useProductsTranslations()
  const [activeTab, setActiveTab] = useState<TabType>('images')
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [isSticky, setIsSticky] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showMagnifier, setShowMagnifier] = useState(false)
  const [magnifierPosition, setMagnifierPosition] = useState({ x: 0, y: 0 })
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 })
  const [isMounted, setIsMounted] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)

  const tabsRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const contentRefs = useRef<Record<TabType, HTMLElement | null>>({
    images: null,
    specifications: null,
    applications: null,
    downloads: null,
    description: null,
    compatibility: null
  })

  // 获取产品数据
  const productImages = useMemo(() => getProductImages(product), [product])
  const specifications = useMemo(() => getProductSpecifications(product, t), [product, t])
  const applications = useMemo(() => getApplications(t), [t])

  // 这些变量在组件中会被使用，保留它们
  console.log('Product data loaded:', { productImages: productImages.length, specifications: specifications.length, applications: applications.length })
  const originalPositionRef = useRef<number | null>(null)
  const throttleRef = useRef<NodeJS.Timeout | null>(null)

  // 检测移动设备
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const tabs = [
    {
      id: 'images',
      label: t('detail.tabs.productDetails'),
      icon: ImageIcon
    },
    {
      id: 'specifications',
      label: t('detail.tabs.specifications'),
      icon: Monitor
    },
    {
      id: 'applications',
      label: t('detail.tabs.applications'),
      icon: Settings
    },
    {
      id: 'downloads',
      label: t('detail.tabs.downloads'),
      icon: Download
    }
  ]

  const renderDownloadCard = useCallback((download: ProductDownload, index: number) => {
    const typeColor = downloadTypeColors[download.type as keyof typeof downloadTypeColors] || downloadTypeColors.other
    
    return (
      <div key={index} className="bg-white border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-2">{download.title}</h4>
            <p className="text-sm text-gray-600 mb-3">{download.description}</p>
            <div className="flex items-center gap-3 text-sm text-gray-500">
              <span className={`px-2 py-1 text-xs font-medium border ${typeColor}`}>
                {download.type}
              </span>
              <span>{download.version}</span>
              <span>{download.fileSize}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-400">
            {new Date(download.releaseDate).toLocaleDateString()}
          </span>
          <Button size="sm" asChild>
            <a href={download.fileUrl} download className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Download
            </a>
          </Button>
        </div>
      </div>
    )
  }, [])

  const detectActiveSection = useCallback(() => {
    if (!isMounted || typeof window === 'undefined') return

    const scrollPosition = window.scrollY + HEADER_HEIGHT + SCROLL_OFFSET + 50
    const sections = Object.entries(contentRefs.current)
    
    for (let i = sections.length - 1; i >= 0; i--) {
      const [sectionId, element] = sections[i]
      if (element && element.offsetTop <= scrollPosition) {
        setActiveTab(sectionId as TabType)
        break
      }
    }
  }, [isMounted])

  useEffect(() => {
    setIsMounted(true)
    
    const timer1 = setTimeout(() => {
      if (tabsRef.current) {
        originalPositionRef.current = tabsRef.current.offsetTop
      }
    }, 100)

    const timer2 = setTimeout(() => {
      detectActiveSection()
    }, 200)

    const timer3 = setTimeout(() => {
      detectActiveSection()
    }, 500)

    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(() => {
        if (tabsRef.current) {
          originalPositionRef.current = tabsRef.current.offsetTop
        }
        detectActiveSection()
      }, 150)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(resizeTimer)
      window.removeEventListener('resize', handleResize)
    }
  }, [isMounted, isSticky])

  useEffect(() => {
    if (!isMounted || typeof window === 'undefined') return

    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          if (tabsRef.current && originalPositionRef.current !== null) {
            const currentScrollY = window.scrollY
            const triggerPoint = originalPositionRef.current - HEADER_HEIGHT

            const buffer = 5
            const shouldBeSticky = currentScrollY >= triggerPoint - buffer

            if (shouldBeSticky !== isSticky) {
              setIsTransitioning(true)
              setIsSticky(shouldBeSticky)

              setTimeout(() => {
                setIsTransitioning(false)
              }, 200)
            }
          }

          if (throttleRef.current) {
            clearTimeout(throttleRef.current)
          }

          throttleRef.current = setTimeout(() => {
            detectActiveSection()
          }, 100)

          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
      if (throttleRef.current) {
        clearTimeout(throttleRef.current)
      }
    }
  }, [detectActiveSection, isMounted, isSticky])

  const handleTabChange = (tabId: TabType) => {
    if (!isMounted || typeof window === 'undefined') return

    const contentElement = contentRefs.current[tabId]
    if (contentElement) {
      // 计算更精确的滚动位置
      const tabsHeight = tabsRef.current?.offsetHeight || 64
      const additionalOffset = isSticky ? 20 : 100
      const elementTop = contentElement.offsetTop - HEADER_HEIGHT - tabsHeight - additionalOffset
      
      window.scrollTo({
        top: Math.max(0, elementTop),
        behavior: 'smooth'
      })
    }
  }

  return (
    <div className="w-full">
      {isMounted && isSticky && (
        <div className="h-16 border-b border-gray-200" />
      )}

      <div
        ref={tabsRef}
        className={`
          transition-all duration-200 ease-out z-40 will-change-transform
          ${isMounted && isSticky
            ? `fixed left-0 right-0 bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-200`
            : 'relative bg-transparent border-b border-gray-200'
          }
          ${isTransitioning ? 'transform-gpu' : ''}
        `}
        style={isMounted && isSticky ? {
          top: `${HEADER_HEIGHT}px`,
          transform: 'translate3d(0, 0, 0)'
        } : {
          transform: 'translate3d(0, 0, 0)'
        }}
      >
        <div className={`${isSticky ? 'container mx-auto px-4' : 'px-6'}`}>
          <nav className="flex space-x-2 overflow-x-auto scrollbar-hide">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              const isActive = activeTab === tab.id

              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id as TabType)}
                  className={`
                    flex items-center gap-3 px-6 py-3 font-medium text-sm whitespace-nowrap
                    transition-all duration-300 ease-in-out relative group
                    border-2 border-transparent
                    ${isActive
                      ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg shadow-orange-500/25 transform scale-105'
                      : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50 hover:border-orange-200 hover:shadow-md hover:transform hover:scale-102'
                    }
                    ${isSticky ? 'min-h-[48px]' : 'min-h-[52px]'}
                  `}
                >
                  <IconComponent className={`w-5 h-5 transition-all duration-300 ${
                    isActive ? 'text-white' : 'text-gray-500 group-hover:text-orange-500'
                  }`} />
                  <span className="font-semibold">{tab.label}</span>
                  {isActive && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white shadow-sm" />
                  )}
                  {!isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 via-orange-500/5 to-orange-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  )}
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      <div className={`space-y-6 ${isSticky ? 'mt-8' : 'mt-8'} bg-gray-50 p-6`}>
        <div
          ref={(el) => { contentRefs.current.description = el }}
          id="tab-content-description"
          className="scroll-mt-24 bg-white p-6 shadow-sm border border-gray-200"
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <FileText className="w-6 h-6 text-orange-500" />
              {t('detail.tabs.description')}
            </h2>
          </div>
          <div className="prose prose-lg max-w-none">
            {typeof detailTabs.description === 'string' ? (
              <div dangerouslySetInnerHTML={{ __html: detailTabs.description }} />
            ) : typeof detailTabs.description === 'object' && detailTabs.description !== null ? (
              <div className="space-y-4">
                {Object.entries(detailTabs.description).map(([key, value]) => (
                  <div key={key} className="border-b border-gray-100 pb-3 last:border-b-0">
                    <dt className="font-semibold text-gray-900 mb-1">{key}</dt>
                    <dd className="text-gray-600">{String(value)}</dd>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">{t('detail.noContent')}</p>
            )}
          </div>
        </div>

        <div
          ref={(el) => { contentRefs.current.specifications = el }}
          id="tab-content-specifications"
          className="scroll-mt-24 bg-white p-6 shadow-sm border border-gray-200"
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Monitor className="w-6 h-6" style={{color: 'rgb(59, 115, 237)'}} />
              {t('detail.tabs.specifications')}
            </h2>
          </div>
          <div className="prose prose-lg max-w-none">
            {typeof detailTabs.specifications === 'string' ? (
              <div dangerouslySetInnerHTML={{ __html: detailTabs.specifications }} />
            ) : typeof detailTabs.specifications === 'object' && detailTabs.specifications !== null ? (
              <div className="space-y-4">
                {Object.entries(detailTabs.specifications).map(([key, value]) => (
                  <div key={key} className="border-b border-gray-100 pb-3 last:border-b-0">
                    <dt className="font-semibold text-gray-900 mb-1">{key}</dt>
                    <dd className="text-gray-600">{String(value)}</dd>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">{t('detail.noContent')}</p>
            )}
          </div>
        </div>

        <div
          ref={(el) => { contentRefs.current.compatibility = el }}
          id="tab-content-compatibility"
          className="scroll-mt-24 bg-white p-6 shadow-sm border border-gray-200"
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Laptop className="w-6 h-6 text-orange-500" />
              {t('detail.tabs.compatibility')}
            </h2>
          </div>
          <div className="prose prose-lg max-w-none">
            {typeof detailTabs.compatibility === 'string' ? (
              <div dangerouslySetInnerHTML={{ __html: detailTabs.compatibility }} />
            ) : Array.isArray(detailTabs.compatibility) ? (
              <div className="space-y-2">
                {detailTabs.compatibility.map((item: any, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-orange-500"></span>
                    <span className="text-gray-600">{String(item)}</span>
                  </div>
                ))}
              </div>
            ) : typeof detailTabs.compatibility === 'object' && detailTabs.compatibility !== null ? (
              <div className="space-y-4">
                {Object.entries(detailTabs.compatibility).map(([key, value]) => (
                  <div key={key} className="border-b border-gray-100 pb-3 last:border-b-0">
                    <dt className="font-semibold text-gray-900 mb-1">{key}</dt>
                    <dd className="text-gray-600">{String(value)}</dd>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">{t('detail.noContent')}</p>
            )}
          </div>
        </div>

        <div
          ref={(el) => { contentRefs.current.downloads = el }}
          id="tab-content-downloads"
          className="scroll-mt-24 bg-white p-6 shadow-sm border border-gray-200"
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Download className="w-6 h-6 text-orange-500" />
              {t('detail.tabs.downloads')}
            </h2>
            <p className="text-gray-600">
              {t('detail.downloadsDescription', { productName: product.name })}
            </p>
          </div>

          {downloads.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {downloads.map(renderDownloadCard)}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Download className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('detail.noDownloads')}</p>
            </div>
          )}

          
        </div>
      </div>
    </div>
  )
})

ProductDetailTabs.displayName = 'ProductDetailTabs'

export default ProductDetailTabs
