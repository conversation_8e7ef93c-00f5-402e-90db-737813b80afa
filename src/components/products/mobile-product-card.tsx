'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Product } from '@/types/product'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Eye, Heart } from 'lucide-react'
import { ResponsiveText } from '@/components/ui/responsive-text'

interface MobileProductCardProps {
  product: Product
  onAddToWishlist?: (product: Product) => void
}

export function MobileProductCard({ product, onAddToWishlist }: MobileProductCardProps) {
  const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]


  const handleAddToWishlist = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onAddToWishlist?.(product)
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-orange-300 overflow-hidden">
      <Link href={`/products/${product.slug}`}>
        <div className="relative">
          {/* Product Image */}
          <div className="aspect-square bg-gray-100 relative overflow-hidden">
            {mainImage ? (
              <Image
                src={mainImage.url}
                alt={mainImage.alt}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <div className="text-4xl mb-2">📦</div>
                  <div className="text-sm">No Image</div>
                </div>
              </div>
            )}
            
            {/* Badges */}
            <div className="absolute top-2 left-2 flex flex-col gap-1">

              {product.isFeatured && (
                <Badge className="bg-orange-500 text-white text-xs px-1.5 py-0.5">
                  Featured
                </Badge>
              )}

            </div>

            {/* Wishlist Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 p-1.5 bg-white/80 hover:bg-white"
              onClick={handleAddToWishlist}
            >
              <Heart className="w-3.5 h-3.5" />
            </Button>
          </div>

          {/* Product Info */}
          <CardContent className="p-3">
            {/* Category */}
            <div className="text-xs text-orange-600 font-medium mb-1 uppercase tracking-wide">
              {product.category.replace('-', ' ')}
            </div>

            {/* Product Name */}
            <ResponsiveText
              as="h3"
              size="sm"
              weight="semibold"
              className="text-gray-900 mb-1 line-clamp-2 group-hover:text-orange-600 transition-colors"
            >
              {product.name}
            </ResponsiveText>

            {/* Short Description */}
            <ResponsiveText
              size="xs"
              color="secondary"
              className="mb-2 line-clamp-2"
            >
              {product.shortDescription}
            </ResponsiveText>



            {/* Action Buttons */}
            <div className="flex gap-2 mb-3">
              <Button
                size="sm"
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-xs"
                asChild
              >
                <Link href={`/products/${product.slug}`} className="flex items-center justify-center">
                  <Eye className="w-3 h-3 mr-1" />
                  View Details
                </Link>
              </Button>
            </div>

            {/* Features (Mobile Only) */}
            {product.features && product.features.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-100">
                <div className="flex flex-wrap gap-1">
                  {product.features.slice(0, 2).map((feature, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="text-xs px-1.5 py-0.5"
                    >
                      {feature}
                    </Badge>
                  ))}
                  {product.features.length > 2 && (
                    <Badge
                      variant="secondary"
                      className="text-xs px-1.5 py-0.5"
                    >
                      +{product.features.length - 2} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </div>
      </Link>
    </Card>
  )
}
