import Head from 'next/head'

interface SEOHeadProps {
  language?: 'en' | 'zh'
  pathname?: string
  alternateUrls?: {
    en: string
    zh: string
  }
  canonicalUrl?: string
}

export function SEOHead({ 
  language = 'en', 
  pathname = '/',
  alternateUrls,
  canonicalUrl 
}: SEOHeadProps) {
  // 生成默认的替代URL
  const defaultAlternateUrls = {
    en: `https://Linnuo-clone.vercel.app/en${pathname}`,
    zh: `https://Linnuo-clone.vercel.app/zh${pathname}`
  }

  const urls = alternateUrls || defaultAlternateUrls
  const canonical = canonicalUrl || `https://Linnuo-clone.vercel.app${pathname}`

  return (
    <Head>
      {/* 规范链接 */}
      <link rel="canonical" href={canonical} />
      
      {/* 语言替代链接 */}
      <link rel="alternate" hrefLang="en" href={urls.en} />
      <link rel="alternate" hrefLang="zh" href={urls.zh} />
      <link rel="alternate" hrefLang="zh-CN" href={urls.zh} />
      <link rel="alternate" hrefLang="x-default" href={urls.en} />

      {/* 当前语言标识 */}
      <meta httpEquiv="content-language" content={language === 'zh' ? 'zh-CN' : 'en-US'} />
      
      {/* 地理定位 */}
      <meta name="geo.region" content={language === 'zh' ? 'CN' : 'US'} />
      <meta name="geo.placename" content={language === 'zh' ? 'China' : 'United States'} />
      
      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://vivid-pleasure-04cb3dbd82.strapiapp.com" />
      
      {/* 预连接 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Google Fonts - Open Sans */}
      <link 
        href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap" 
        rel="stylesheet" 
      />
      
      {/* 关键资源预加载 */}
      <link 
        rel="preload" 
        href="/fonts/inter-400.woff2" 
        as="font" 
        type="font/woff2" 
        crossOrigin="anonymous" 
      />
      <link 
        rel="preload" 
        href="/fonts/inter-600.woff2" 
        as="font" 
        type="font/woff2" 
        crossOrigin="anonymous" 
      />
      
      {/* 预加载 Open Sans 关键字重 */}
      <link 
        rel="preload" 
        href="https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVc.woff2" 
        as="font" 
        type="font/woff2" 
        crossOrigin="anonymous" 
      />
      <link 
        rel="preload" 
        href="https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsg-1x4gaVc.woff2" 
        as="font" 
        type="font/woff2" 
        crossOrigin="anonymous" 
      />
      
      {/* 关键图片预加载 */}
      <link 
        rel="preload" 
        href="/images/background/about-bg.jpg" 
        as="image" 
        type="image/jpeg" 
      />
      <link 
        rel="preload" 
        href="/logo-top-bg-white.svg" 
        as="image" 
        type="image/svg+xml" 
      />

      {/* 结构化数据 - 网站语言信息 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "url": canonical,
            "inLanguage": language === 'zh' ? 'zh-CN' : 'en-US',
            "availableLanguage": [
              {
                "@type": "Language",
                "name": "English",
                "alternateName": "en"
              },
              {
                "@type": "Language", 
                "name": "中文",
                "alternateName": "zh"
              }
            ]
          })
        }}
      />
    </Head>
  )
}

// 静态版本，不依赖任何Context
export function StaticSEOHead() {
  return (
    <>
      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://vivid-pleasure-04cb3dbd82.strapiapp.com" />
      
      {/* 预连接 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* 关键资源预加载 - 字体样式 */}
      <link 
        rel="stylesheet" 
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" 
      />
      
      {/* 关键图片预加载 */}
      <link 
        rel="preload" 
        href="/logo-top-bg-white.svg" 
        as="image" 
        type="image/svg+xml" 
      />

      {/* 基础语言设置 */}
      <link rel="alternate" hrefLang="en" href="https://Linnuo-clone.vercel.app/en" />
      <link rel="alternate" hrefLang="zh" href="https://Linnuo-clone.vercel.app/zh" />
      <link rel="alternate" hrefLang="zh-CN" href="https://Linnuo-clone.vercel.app/zh" />
      <link rel="alternate" hrefLang="x-default" href="https://Linnuo-clone.vercel.app/en" />
    </>
  )
}
