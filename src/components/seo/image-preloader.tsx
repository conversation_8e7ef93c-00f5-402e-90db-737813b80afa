'use client'

import Head from 'next/head'
import { useEffect, useState } from 'react'

interface ImagePreloaderProps {
  images: Array<{
    src: string
    priority?: 'high' | 'low'
    type?: 'image/webp' | 'image/avif' | 'image/jpeg' | 'image/png'
  }>
}

export function ImagePreloader({ images }: ImagePreloaderProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) return null

  return (
    <Head>
      {images.map((image, index) => (
        <link
          key={`${image.src}-${index}`}
          rel="preload"
          href={image.src}
          as="image"
          type={image.type}
          // @ts-ignore - fetchpriority is a valid attribute
          fetchpriority={image.priority || 'low'}
        />
      ))}
    </Head>
  )
}

// Hook for managing critical image preloading
export function useCriticalImagePreloading() {
  const [criticalImages] = useState([
    {
      src: '/images/products/lattepanda-sigma-hero.jpg',
      priority: 'high' as const,
      type: 'image/jpeg' as const
    },
    {
      src: '/images/hero/sigma-hero.jpg',
      priority: 'high' as const,
      type: 'image/jpeg' as const
    },
    {
      src: '/images/products/sigma-main.jpg',
      priority: 'low' as const,
      type: 'image/jpeg' as const
    }
  ])

  return { criticalImages, ImagePreloader }
}
