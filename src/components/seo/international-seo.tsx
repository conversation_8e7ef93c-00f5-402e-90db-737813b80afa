'use client'

import { useLanguage } from '@/contexts/simple-language-context'
import { usePathname } from 'next/navigation'

interface InternationalSEOProps {
  alternateUrls?: {
    en: string
    zh: string
  }
  canonicalUrl?: string
}

export function InternationalSEO({ alternateUrls, canonicalUrl }: InternationalSEOProps) {
  const { language } = useLanguage()
  const pathname = usePathname()

  // 生成默认的替代URL
  const defaultAlternateUrls = {
    en: `https://Linnuo-clone.vercel.app/en${pathname}`,
    zh: `https://Linnuo-clone.vercel.app/zh${pathname}`
  }

  const urls = alternateUrls || defaultAlternateUrls
  const canonical = canonicalUrl || `https://Linnuo-clone.vercel.app${pathname}`

  return (
    <>
      {/* 规范链接 */}
      <link rel="canonical" href={canonical} />
      
      {/* 语言替代链接 */}
      <link rel="alternate" hrefLang="en" href={urls.en} />
      <link rel="alternate" hrefLang="zh" href={urls.zh} />
      <link rel="alternate" hrefLang="zh-CN" href={urls.zh} />
      <link rel="alternate" hrefLang="x-default" href={urls.en} />

      {/* 当前语言标识 */}
      <meta httpEquiv="content-language" content={language === 'zh' ? 'zh-CN' : 'en-US'} />
      
      {/* 地理定位 */}
      <meta name="geo.region" content={language === 'zh' ? 'CN' : 'US'} />
      <meta name="geo.placename" content={language === 'zh' ? 'China' : 'United States'} />
      
      {/* 结构化数据 - 网站语言信息 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "url": canonical,
            "inLanguage": language === 'zh' ? 'zh-CN' : 'en-US',
            "availableLanguage": [
              {
                "@type": "Language",
                "name": "English",
                "alternateName": "en"
              },
              {
                "@type": "Language", 
                "name": "中文",
                "alternateName": "zh"
              }
            ]
          })
        }}
      />
    </>
  )
}

// 语言切换器组件
export function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage()
  const pathname = usePathname()

  const switchLanguage = (newLang: 'en' | 'zh') => {
    setLanguage(newLang)
    
    // 更新URL（如果需要）
    const newUrl = pathname.replace(/^\/(en|zh)/, `/${newLang}`)
    window.history.pushState({}, '', newUrl)
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => switchLanguage('en')}
        className={`px-3 py-1 text-sm rounded ${
          language === 'en' 
            ? 'bg-orange-500 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        aria-label="Switch to English"
      >
        EN
      </button>
      <button
        onClick={() => switchLanguage('zh')}
        className={`px-3 py-1 text-sm rounded ${
          language === 'zh' 
            ? 'bg-orange-500 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        aria-label="切换到中文"
      >
        中文
      </button>
    </div>
  )
}

// 生成多语言sitemap
export function generateMultilingualSitemap() {
  const baseUrl = 'https://Linnuo-clone.vercel.app'
  const languages = ['en', 'zh']
  
  const staticPages = [
    '',
    '/products',
    '/news', 
    '/about',
    '/contact',
    '/downloads',
    '/application'
  ]

  const sitemapEntries = []

  staticPages.forEach(page => {
    languages.forEach(lang => {
      const url = `${baseUrl}/${lang}${page}`
      const alternates = languages.map(altLang => ({
        lang: altLang,
        url: `${baseUrl}/${altLang}${page}`
      }))

      sitemapEntries.push({
        url,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: page === '' ? 1 : 0.8,
        alternates
      })
    })
  })

  return sitemapEntries
}

// 多语言面包屑导航
interface BreadcrumbItem {
  name: string
  url: string
}

interface MultilingualBreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

export function MultilingualBreadcrumb({ items, className }: MultilingualBreadcrumbProps) {
  const { language } = useLanguage()

  const homeText = language === 'zh' ? '首页' : 'Home'

  return (
    <nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
      <a href="/" className="hover:text-orange-500">
        {homeText}
      </a>
      {items.map((item, index) => (
        <span key={index} className="flex items-center space-x-2">
          <span>/</span>
          {index === items.length - 1 ? (
            <span className="text-gray-900 font-medium">{item.name}</span>
          ) : (
            <a href={item.url} className="hover:text-orange-500">
              {item.name}
            </a>
          )}
        </span>
      ))}
      
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": homeText,
                "item": "https://Linnuo-clone.vercel.app/"
              },
              ...items.map((item, index) => ({
                "@type": "ListItem",
                "position": index + 2,
                "name": item.name,
                "item": `https://Linnuo-clone.vercel.app${item.url}`
              }))
            ]
          })
        }}
      />
    </nav>
  )
}

// 多语言元数据生成器
export function generateMultilingualMetadata(
  title: { en: string; zh: string },
  description: { en: string; zh: string },
  keywords: { en: string[]; zh: string[] },
  currentLang: 'en' | 'zh'
) {
  return {
    title: title[currentLang],
    description: description[currentLang],
    keywords: keywords[currentLang],
    openGraph: {
      title: title[currentLang],
      description: description[currentLang],
      locale: currentLang === 'zh' ? 'zh_CN' : 'en_US',
      alternateLocale: currentLang === 'zh' ? 'en_US' : 'zh_CN'
    },
    twitter: {
      title: title[currentLang],
      description: description[currentLang]
    }
  }
}
