import { Product } from '@/types/product'
import { BlogPost } from '@/types/blog'

interface ProductStructuredDataProps {
  product: Product
}

export function ProductStructuredData({ product }: ProductStructuredDataProps) {
  const mainImage = product.images?.find(img => img.type === 'main') || product.images?.[0]
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": mainImage ? [mainImage.url] : [],
    "brand": {
      "@type": "Brand",
      "name": "Linnuo"
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "Linnuo"
    },
    "category": product.category,
    "sku": product.id,
    "offers": {
      "@type": "Offer",
      "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://Linnuo-clone.vercel.app'}/products/${product.slug}`,
      "priceCurrency": "USD",
      // TODO: Replace with actual price from product data
      "price": product.price?.toString() || "99.99",
      "priceValidUntil": new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T'),
      "itemCondition": "https://schema.org/NewCondition",
      "availability": product.status === 'available' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
    },
    // TODO: Add rating data to product type and API
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": product.rating?.toString() || "4.8",
      "reviewCount": product.reviewCount?.toString() || "125"
    },
    // TODO: Add review data to product type and API
    "review": [
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Tech Enthusiast"
        },
        "datePublished": "2024-07-15",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5"
        },
        "reviewBody": "This is an amazing single board computer! Incredibly powerful and versatile. Perfect for my robotics project."
      }
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

interface BlogStructuredDataProps {
  post: BlogPost
}

export function BlogStructuredData({ post }: BlogStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.featuredImage ? [post.featuredImage.url] : [],
    "author": {
      "@type": "Person",
      "name": post.author.name,
      "description": post.author.bio
    },
    "publisher": {
      "@type": "Organization",
      "name": "Linnuo",
      "logo": {
        "@type": "ImageObject",
        "url": "https://Linnuo-clone.vercel.app/logo-top-bg-white.svg"
      }
    },
    "datePublished": post.publishedAt,
    "dateModified": post.updatedAt || post.publishedAt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://Linnuo-clone.vercel.app/blog/${post.slug}`
    },
    "keywords": post.tags.join(", "),
    "articleSection": post.category,
    "wordCount": post.content.split(' ').length,
    "timeRequired": `PT${post.readTime}M`
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

export function OrganizationStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Linnuo",
    "url": "https://Linnuo-clone.vercel.app",
    "logo": "https://Linnuo-clone.vercel.app/logo-top-bg-white.svg",
    "description": "Linnuois a complete Windows/Linux device in a single board computer that can run almost any x86 software. Perfect for IoT, robotics, and embedded applications.",
    "foundingDate": "2015",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": ["English"]
    },
    "sameAs": [
      "https://twitter.com/LinnuoCN",
      "https://github.com/Linnuoteam",
      "https://discord.gg/rX2BmEmFXH"
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

export function WebsiteStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Linnuo",
    "url": "https://Linnuo-clone.vercel.app",
    "description": "Linnuo- x86 Windows/Linux Single Board Computers",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://Linnuo-clone.vercel.app/products?search={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
