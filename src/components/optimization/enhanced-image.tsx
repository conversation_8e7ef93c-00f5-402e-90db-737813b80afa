'use client'

import { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface EnhancedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
  // 新增优化选项
  enableWebP?: boolean
  enableAVIF?: boolean
  enableLazyLoading?: boolean
  enableProgressiveLoading?: boolean
  enableErrorFallback?: boolean
  fallbackSrc?: string
  aspectRatio?: string
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down'
}

export function EnhancedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  loading = 'lazy',
  onLoad,
  onError,
  enableWebP = true,
  enableAVIF = true,
  enableLazyLoading = true,
  enableProgressiveLoading = true,
  enableErrorFallback = true,
  fallbackSrc = '/images/placeholder.jpg',
  aspectRatio,
  objectFit = 'cover',
  ...props
}: EnhancedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [loadProgress, setLoadProgress] = useState(0)
  const [isInView, setIsInView] = useState(!enableLazyLoading || priority)
  const imgRef = useRef<HTMLDivElement>(null)

  // 懒加载观察器
  useEffect(() => {
    if (!enableLazyLoading || priority || isInView) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '100px 0px', // 提前100px开始加载
        threshold: 0.01,
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current)
      }
    }
  }, [enableLazyLoading, priority, isInView])

  // 生成优化的图片源
  const generateOptimizedSrc = (originalSrc: string, format?: string) => {
    // 如果是外部URL，直接返回
    if (originalSrc.startsWith('http')) {
      return originalSrc
    }

    // 构建优化参数
    const params = new URLSearchParams()
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    if (format) params.set('f', format)

    return `${originalSrc}?${params.toString()}`
  }

  // 处理加载完成
  const handleLoad = () => {
    setIsLoaded(true)
    setLoadProgress(100)
    onLoad?.()
  }

  // 处理加载错误
  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  // 模拟渐进式加载进度
  useEffect(() => {
    if (!enableProgressiveLoading || isLoaded) return

    const interval = setInterval(() => {
      setLoadProgress((prev) => {
        if (prev >= 90) {
          clearInterval(interval)
          return prev
        }
        return prev + Math.random() * 10
      })
    }, 100)

    return () => clearInterval(interval)
  }, [enableProgressiveLoading, isLoaded])

  // 如果不在视口内且启用了懒加载，显示占位符
  if (!isInView) {
    return (
      <div
        ref={imgRef}
        className={cn(
          'bg-gray-200 animate-pulse',
          aspectRatio && `aspect-[${aspectRatio}]`,
          className
        )}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
        }}
      />
    )
  }

  // 错误状态显示备用图片
  if (hasError && enableErrorFallback) {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <Image
          src={fallbackSrc}
          alt={`${alt} (fallback)`}
          width={width}
          height={height}
          fill={fill}
          className={cn('object-cover', `object-${objectFit}`)}
          quality={quality}
        />
        <div className="absolute inset-0 bg-black/10 flex items-center justify-center">
          <span className="text-white text-sm bg-black/50 px-2 py-1 rounded">
            图片加载失败
          </span>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={imgRef}
      className={cn('relative overflow-hidden', className)}
      style={{
        aspectRatio: aspectRatio,
      }}
    >
      {/* 加载进度条 */}
      {enableProgressiveLoading && !isLoaded && (
        <div className="absolute inset-0 bg-gray-200">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-300 to-gray-200 animate-pulse" />
          <div className="absolute bottom-0 left-0 h-1 bg-orange-500 transition-all duration-300"
               style={{ width: `${loadProgress}%` }} />
        </div>
      )}

      {/* 主图片 */}
      <picture>
        {/* AVIF格式 - 最新的高效格式 */}
        {enableAVIF && (
          <source
            srcSet={generateOptimizedSrc(src, 'avif')}
            type="image/avif"
            sizes={sizes}
          />
        )}
        
        {/* WebP格式 - 广泛支持的现代格式 */}
        {enableWebP && (
          <source
            srcSet={generateOptimizedSrc(src, 'webp')}
            type="image/webp"
            sizes={sizes}
          />
        )}

        {/* 原始格式作为备用 */}
        <Image
          src={generateOptimizedSrc(src)}
          alt={alt}
          width={width}
          height={height}
          fill={fill}
          priority={priority}
          quality={quality}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          sizes={sizes}
          loading={loading}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            `object-${objectFit}`
          )}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      </picture>

      {/* 加载状态覆盖层 */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

// 预设的图片组件变体
export function HeroImage(props: Omit<EnhancedImageProps, 'priority' | 'quality'>) {
  return (
    <EnhancedImage
      {...props}
      priority={true}
      quality={95}
      enableProgressiveLoading={true}
      placeholder="blur"
    />
  )
}

export function ProductImage(props: Omit<EnhancedImageProps, 'quality' | 'aspectRatio'>) {
  return (
    <EnhancedImage
      {...props}
      quality={90}
      aspectRatio="4/3"
      enableWebP={true}
      enableAVIF={true}
      enableLazyLoading={true}
    />
  )
}

export function ThumbnailImage(props: Omit<EnhancedImageProps, 'quality' | 'loading'>) {
  return (
    <EnhancedImage
      {...props}
      quality={75}
      loading="lazy"
      enableProgressiveLoading={false}
    />
  )
}

export function NewsImage(props: Omit<EnhancedImageProps, 'aspectRatio' | 'objectFit'>) {
  return (
    <EnhancedImage
      {...props}
      aspectRatio="16/9"
      objectFit="cover"
      enableLazyLoading={true}
      enableErrorFallback={true}
    />
  )
}
