'use client'

import { useEffect } from 'react'

// Core Web Vitals 监控和优化
export function CoreWebVitalsOptimizer() {
  useEffect(() => {
    // 只在生产环境和支持的浏览器中运行
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {
      return
    }

    // 动态导入 web-vitals 库以避免影响初始加载
    import('web-vitals').then(({ onCLS, onFCP, onLCP, onTTFB }) => {
      // Cumulative Layout Shift (CLS) - 累积布局偏移
      onCLS((metric) => {
        // 发送到分析服务
        sendToAnalytics('CLS', metric)
        
        // 如果CLS过高，在开发环境中警告
        if (metric.value > 0.1 && process.env.NODE_ENV === 'development') {
          console.warn('High CLS detected:', metric.value)
        }
      })

      // First Input Delay (FID) - 首次输入延迟
     

      // First Contentful Paint (FCP) - 首次内容绘制
      onFCP((metric) => {
        sendToAnalytics('FCP', metric)
        
        if (metric.value > 1800 && process.env.NODE_ENV === 'development') {
          console.warn('Slow FCP detected:', metric.value)
        }
      })

      // Largest Contentful Paint (LCP) - 最大内容绘制
      onLCP((metric) => {
        sendToAnalytics('LCP', metric)
        
        if (metric.value > 2500 && process.env.NODE_ENV === 'development') {
          console.warn('Slow LCP detected:', metric.value)
        }
      })

      // Time to First Byte (TTFB) - 首字节时间
      onTTFB((metric) => {
        sendToAnalytics('TTFB', metric)
        
        if (metric.value > 800 && process.env.NODE_ENV === 'development') {
          console.warn('Slow TTFB detected:', metric.value)
        }
      })
    }).catch((error) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Failed to load web-vitals:', error)
      }
    })

    // 预连接到重要的第三方域名
    preconnectToDomains([
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://vivid-pleasure-04cb3dbd82.strapiapp.com',
      'https://vivid-pleasure-04cb3dbd82.media.strapiapp.com'
    ])

    // 预加载关键资源
    preloadCriticalResources()

    // 优化字体加载
    optimizeFontLoading()

    // 减少布局偏移
    reduceLayoutShift()

  }, [])

  return null // 这是一个纯优化组件，不渲染任何内容
}

// 发送指标到分析服务
function sendToAnalytics(metricName: string, metric: any) {
  // 这里可以集成 Google Analytics, Vercel Analytics 等
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metricName, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.value),
      non_interaction: true,
    })
  }

  // 发送到 Vercel Analytics
  if (typeof window !== 'undefined' && (window as any).va) {
    (window as any).va('track', 'Web Vitals', {
      metric: metricName,
      value: metric.value,
      id: metric.id,
    })
  }

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`${metricName}:`, metric.value)
  }
}

// 预连接到重要域名
function preconnectToDomains(domains: string[]) {
  domains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

// 预加载关键资源
function preloadCriticalResources() {
  const criticalResources = [
    { href: '/images/logo.svg', as: 'image', type: 'image/svg+xml' },
  ]

  criticalResources.forEach(resource => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = resource.href
    link.as = resource.as
    if (resource.type) {
      link.type = resource.type
    }
    document.head.appendChild(link)
  })
}

// 优化字体加载
function optimizeFontLoading() {
  // 直接添加字体样式表
  const fontLink = document.createElement('link')
  fontLink.rel = 'stylesheet'
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  document.head.appendChild(fontLink)

  // 字体显示优化
  if ('fonts' in document) {
    document.fonts.ready.then(() => {
      document.body.classList.add('fonts-loaded')
    })
  }
}

// 减少布局偏移
function reduceLayoutShift() {
  // 为图片容器设置固定尺寸
  const images = document.querySelectorAll('img[data-optimize]')
  images.forEach(img => {
    if (!img.getAttribute('width') || !img.getAttribute('height')) {
      // 为没有尺寸的图片设置默认尺寸
      img.setAttribute('width', '800')
      img.setAttribute('height', '600')
      img.setAttribute('style', 'aspect-ratio: 4/3; object-fit: cover;')
    }
  })

  // 为动态内容预留空间
  const dynamicContainers = document.querySelectorAll('[data-dynamic-content]')
  dynamicContainers.forEach(container => {
    if (!container.getAttribute('style')?.includes('min-height')) {
      (container as HTMLElement).style.minHeight = '200px'
    }
  })
}

// 图片懒加载优化
export function optimizeImageLoading() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          if (img.dataset.src) {
            img.src = img.dataset.src
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        }
      })
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })

    // 观察所有懒加载图片
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img)
    })
  }
}

// 关键CSS内联
export function inlineCriticalCSS() {
  const criticalCSS = `
    /* 关键路径CSS */
    body { font-family: Inter, sans-serif; }
    .hero-section { min-height: 100vh; }
    .loading-spinner { 
      width: 40px; 
      height: 40px; 
      border: 4px solid #f3f3f3;
      border-top: 4px solid #f97316;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .fonts-loaded { font-display: swap; }
  `

  const style = document.createElement('style')
  style.textContent = criticalCSS
  document.head.appendChild(style)
}

// 性能预算监控
export function monitorPerformanceBudget() {
  if ('performance' in window && 'PerformanceObserver' in window) {
    // 监控资源大小
    const resourceObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry: any) => {
        if (entry.transferSize > 1024 * 1024) { // 1MB
          console.warn('Large resource detected:', entry.name, `${(entry.transferSize / 1024 / 1024).toFixed(2)}MB`)
        }
      })
    })

    try {
      resourceObserver.observe({ entryTypes: ['resource'] })
    } catch (e) {
      console.warn('Resource monitoring not supported')
    }

    // 监控长任务
    const longTaskObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) { // 50ms
          console.warn('Long task detected:', `${entry.duration.toFixed(2)}ms`)
        }
      })
    })

    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] })
    } catch (e) {
      console.warn('Long task monitoring not supported')
    }
  }
}
