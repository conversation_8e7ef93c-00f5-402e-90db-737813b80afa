import React, { useState, useCallback, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';

// 优化的图片组件，支持缓存和预加载
const OptimizedImage: React.FC<{
  src: string;
  alt?: string;
  className?: string;
  onLoad?: () => void;
  onError?: () => void;
}> = ({ src, alt, className, onLoad, onError }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  if (hasError) {
    return (
      <div className={cn('bg-gray-100 border border-gray-300 p-4 text-center text-gray-500', className)}>
        <span>图片加载失败</span>
        {alt && <p className="text-sm mt-1">{alt}</p>}
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse" />
      )}
      <img
        src={src}
        alt={alt || ''}
        className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0'
        )}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy"
      />
    </div>
  );
};

export interface RichTextRendererProps {
  content: string | object | null | undefined;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  imageClassName?: string;
  linkClassName?: string;
  enableImageOptimization?: boolean;
  baseUrl?: string;
}

const RichTextRenderer: React.FC<RichTextRendererProps> = ({
  content,
  variant = 'default',
  className,
  imageClassName,
  linkClassName,
  enableImageOptimization = true,
  baseUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'
}) => {
  // 内容预处理：替换IP地址
  const preprocessContent = useCallback((text: string): string => {
    if (!text) return '';
    
    // 替换常见的本地IP地址为配置的baseUrl
    return text
      .replace(/http:\/\/192\.168\.\d+\.\d+:\d+/g, baseUrl)
      .replace(/http:\/\/localhost:\d+/g, baseUrl)
      .replace(/http:\/\/127\.0\.0\.1:\d+/g, baseUrl);
  }, [baseUrl]);

  // 检测内容格式
  const detectContentFormat = useCallback((text: string): 'markdown' | 'html' | 'plain' => {
    if (!text) return 'plain';
    
    // 检测Markdown语法
    const markdownPatterns = [
      /^#{1,6}\s/m,           // 标题
      /\*\*.*?\*\*/,          // 粗体
      /\*.*?\*/,             // 斜体
      /\[.*?\]\(.*?\)/,      // 链接
      /!\[.*?\]\(.*?\)/,     // 图片
      /^\s*[-*+]\s/m,        // 列表
      /^\s*\d+\.\s/m,       // 有序列表
      /```[\s\S]*?```/,      // 代码块
      /`.*?`/,               // 行内代码
    ];
    
    // 检测HTML标签
    const htmlPatterns = [
      /<\/?[a-z][\s\S]*>/i,   // HTML标签
      /&[a-z]+;/i,           // HTML实体
    ];
    
    const hasMarkdown = markdownPatterns.some(pattern => pattern.test(text));
    const hasHtml = htmlPatterns.some(pattern => pattern.test(text));
    
    if (hasMarkdown && !hasHtml) return 'markdown';
    if (hasHtml) return 'html';
    return 'plain';
  }, []);

  // 样式变体配置
  const variantStyles = useMemo(() => {
    const baseStyles = {
      default: 'prose prose-gray max-w-none',
      compact: 'prose prose-sm prose-gray max-w-none',
      detailed: 'prose prose-lg prose-gray max-w-none'
    };
    
    return {
      ...baseStyles,
      [variant]: cn(baseStyles[variant], className)
    };
  }, [variant, className]);

  // 处理纯文本格式化
  const formatPlainText = useCallback((text: string): string => {
    return text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n\n');
  }, []);

  // 处理JSON格式内容（Strapi富文本格式）
  const processJsonContent = useCallback((jsonContent: any): string => {
    if (!jsonContent || typeof jsonContent !== 'object') {
      return String(jsonContent || '');
    }

    // 处理Strapi的富文本格式
    if (Array.isArray(jsonContent)) {
      return jsonContent.map(item => {
        if (typeof item === 'string') return item;
        if (item?.children) {
          return item.children.map((child: any) => child.text || '').join('');
        }
        return JSON.stringify(item);
      }).join('\n\n');
    }

    // 处理其他对象格式
    if (jsonContent.content || jsonContent.text) {
      return jsonContent.content || jsonContent.text;
    }

    return JSON.stringify(jsonContent, null, 2);
  }, []);

  // 自定义组件配置
  const components = useMemo(() => ({
    img: ({ src, alt, ...props }: any) => {
      if (!src) return null;
      
      // 处理图片路径
      let imageSrc = src;
      
      // 如果是完整的 HTTP/HTTPS URL，直接使用
      if (src.startsWith('http://') || src.startsWith('https://')) {
        imageSrc = src;
      }
      // 处理相对路径
      else if (src.startsWith('/uploads/') || src.startsWith('uploads/')) {
        imageSrc = `${baseUrl}${src.startsWith('/') ? '' : '/'}${src}`;
      } else if (src.startsWith('/')) {
        imageSrc = `${baseUrl}${src}`;
      }
      
      // 只对非完整URL进行预处理（IP地址替换等）
      if (!src.startsWith('http://') && !src.startsWith('https://')) {
        imageSrc = preprocessContent(imageSrc);
      }
      
      // Always use native img tag to avoid hydration issues
      return (
        <img
          src={imageSrc}
          alt={alt}
          className={cn('shadow-sm', imageClassName)}
          loading="lazy"
          {...props}
        />
      );
    },
    a: ({ href, children, ...props }: any) => {
      const processedHref = href ? preprocessContent(href) : '#';
      return (
        <a
          href={processedHref}
          className={cn('text-blue-600 hover:text-blue-800 underline', linkClassName)}
          target={processedHref.startsWith('http') ? '_blank' : undefined}
          rel={processedHref.startsWith('http') ? 'noopener noreferrer' : undefined}
          {...props}
        >
          {children}
        </a>
      );
    },
    h1: ({ children, ...props }: any) => (
      <h1 className="text-3xl font-bold mb-4 text-gray-900" {...props}>{children}</h1>
    ),
    h2: ({ children, ...props }: any) => (
      <h2 className="text-2xl font-semibold mb-3 text-gray-800" {...props}>{children}</h2>
    ),
    h3: ({ children, ...props }: any) => (
      <h3 className="text-xl font-medium mb-2 text-gray-700" {...props}>{children}</h3>
    ),
    p: ({ children, ...props }: any) => (
      <p className="mb-4 text-gray-600 leading-relaxed" {...props}>{children}</p>
    ),
    ul: ({ children, ...props }: any) => (
      <ul className="list-disc list-inside mb-4 space-y-1" {...props}>{children}</ul>
    ),
    ol: ({ children, ...props }: any) => (
      <ol className="list-decimal list-inside mb-4 space-y-1" {...props}>{children}</ol>
    ),
    li: ({ children, ...props }: any) => (
      <li className="text-gray-600" {...props}>{children}</li>
    ),
    blockquote: ({ children, ...props }: any) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-700 mb-4" {...props}>
        {children}
      </blockquote>
    ),
    code: ({ children, ...props }: any) => (
      <code className="bg-gray-100 px-1 py-0.5 text-sm font-mono" {...props}>
        {children}
      </code>
    ),
    pre: ({ children, ...props }: any) => (
      <pre className="bg-gray-100 p-4 overflow-x-auto mb-4" {...props}>
        {children}
      </pre>
    ),
    table: ({ children, ...props }: any) => (
      <div className="overflow-x-auto mb-4">
        <table className="min-w-full border-collapse border border-gray-300" {...props}>
          {children}
        </table>
      </div>
    ),
    th: ({ children, ...props }: any) => (
      <th className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left" {...props}>
        {children}
      </th>
    ),
    td: ({ children, ...props }: any) => (
      <td className="border border-gray-300 px-4 py-2" {...props}>{children}</td>
    ),
  }), [baseUrl, preprocessContent, enableImageOptimization, imageClassName, linkClassName]);

  // 主渲染逻辑
  const renderContent = useMemo(() => {
    if (!content) {
      return <div className="text-gray-500 italic">暂无内容</div>;
    }

    let processedContent: string;
    
    // 处理不同类型的内容
    if (typeof content === 'string') {
      processedContent = preprocessContent(content);
    } else if (typeof content === 'object') {
      processedContent = preprocessContent(processJsonContent(content));
    } else {
      processedContent = preprocessContent(String(content));
    }

    if (!processedContent.trim()) {
      return <div className="text-gray-500 italic">暂无内容</div>;
    }

    const contentFormat = detectContentFormat(processedContent);

    // 根据内容格式选择渲染方式
    switch (contentFormat) {
      case 'markdown':
      case 'html':
        return (
          <div className={variantStyles[variant]}>
            <ReactMarkdown
              components={components}
              rehypePlugins={[rehypeRaw]}
              remarkPlugins={[remarkGfm]}
              skipHtml={false}
            >
              {processedContent}
            </ReactMarkdown>
          </div>
        );
      
      case 'plain':
      default:
        const formattedText = formatPlainText(processedContent);
        return (
          <div className={cn('whitespace-pre-wrap text-gray-600 leading-relaxed', className)}>
            {formattedText}
          </div>
        );
    }
  }, [content, preprocessContent, processJsonContent, detectContentFormat, variantStyles, variant, components, formatPlainText, className]);

  return renderContent;
};

export default RichTextRenderer;