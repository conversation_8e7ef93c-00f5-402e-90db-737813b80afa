import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  MessageSquare,
  Github,
  Youtube,
  Twitter,
  ArrowRight,
  Star,
  Calendar,
  MapPin,
  ExternalLink,
  Heart,
  Share2
} from 'lucide-react'

const communityStats = [
  { label: 'Community Members', value: '25,000+', icon: Users },
  { label: 'Projects Shared', value: '3,500+', icon: Star },
  { label: 'Forum Posts', value: '15,000+', icon: MessageSquare },
  { label: 'GitHub Stars', value: '8,200+', icon: Github }
]

const platforms = [
  {
    name: 'Discord Server',
    description: 'Real-time chat with the community',
    icon: MessageSquare,
    members: '12,000+',
    href: '#discord',
    primary: true
  },
  {
    name: 'GitH<PERSON>',
    description: 'Open source projects and code sharing',
    icon: Gith<PERSON>,
    members: '8,200+',
    href: '#github'
  },
  {
    name: 'YouTube',
    description: 'Tutorials, reviews, and project showcases',
    icon: Youtube,
    members: '45,000+',
    href: '#youtube'
  },
  {
    name: 'Twitter',
    description: 'Latest news and community updates',
    icon: Twitter,
    members: '18,000+',
    href: '#twitter'
  }
]

const featuredProjects = [
  {
    title: 'Smart Home Hub',
    author: 'Alex Chen',
    description: 'Complete home automation system built with Linnuo3 Delta',
    image: '/images/projects/smart-home.jpg',
    likes: 234,
    category: 'IoT',
    href: '#project-1'
  },
  {
    title: 'Portable Gaming Console',
    author: 'Sarah Kim',
    description: 'Retro gaming console with modern performance',
    image: '/images/projects/gaming-console.jpg',
    likes: 189,
    category: 'Gaming',
    href: '#project-2'
  },
  {
    title: 'AI Camera System',
    author: 'Mike Johnson',
    description: 'Real-time object detection and tracking system',
    image: '/images/projects/ai-camera.jpg',
    likes: 156,
    category: 'AI/ML',
    href: '#project-3'
  }
]

const upcomingEvents = [
  {
    title: 'LinnuoMaker Meetup',
    date: '2024-02-15',
    time: '7:00 PM PST',
    location: 'San Francisco, CA',
    type: 'In-Person',
    attendees: 45
  },
  {
    title: 'IoT Workshop: Getting Started',
    date: '2024-02-20',
    time: '2:00 PM EST',
    location: 'Online',
    type: 'Virtual',
    attendees: 120
  },
  {
    title: 'Community Project Showcase',
    date: '2024-02-28',
    time: '6:00 PM GMT',
    location: 'Online',
    type: 'Virtual',
    attendees: 200
  }
]

export function CommunityPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Join the LinnuoCommunity
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Connect with makers, developers, and enthusiasts from around the world. 
          Share your projects, get help, and inspire others.
        </p>
      </div>

      {/* Community Stats */}
      <section className="mb-12">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {communityStats.map((stat) => (
            <Card key={stat.label} className="text-center">
              <CardContent className="pt-6">
                <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Community Platforms */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Connect With Us</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {platforms.map((platform) => (
            <Card key={platform.name} className={`group hover:shadow-lg transition-shadow ${
              platform.primary ? 'ring-2 ring-orange-500' : ''
            }`}>
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-3 bg-orange-100 w-fit">
                  <platform.icon className="w-6 h-6 text-orange-600" />
                </div>
                <CardTitle className="text-lg">{platform.name}</CardTitle>
                <CardDescription>{platform.description}</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-sm text-gray-500 mb-4">
                  {platform.members} members
                </div>
                <Button 
                  asChild 
                  className={platform.primary ? 'bg-orange-500 hover:bg-orange-600' : ''}
                  variant={platform.primary ? 'default' : 'outline'}
                >
                  <Link href={platform.href} className="flex items-center">
                    <span className="flex items-center">
                      Join Now
                      <ExternalLink className="w-4 h-4 ml-2" />
                    </span>
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Featured Projects */}
        <div className="lg:col-span-2">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Featured Projects</h2>
            <Button variant="outline" asChild>
              <Link href="/projects">
                <span className="flex items-center">
                  View All Projects
                  <ArrowRight className="w-4 h-4 ml-2" />
                </span>
              </Link>
            </Button>
          </div>
          
          <div className="space-y-6">
            {featuredProjects.map((project, index) => (
              <Card key={index} className="group hover:shadow-lg transition-shadow">
                <div className="md:flex">
                  <div className="md:w-48 md:flex-shrink-0">
                    <div className="h-48 md:h-full bg-gray-200 relative overflow-hidden">
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <div className="text-center">
                          <div className="text-4xl mb-2">🔧</div>
                          <div className="text-sm">Project Image</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {project.category}
                        </Badge>
                        <div className="flex items-center text-sm text-gray-500">
                          <Heart className="w-4 h-4 mr-1" />
                          {project.likes}
                        </div>
                      </div>
                      <CardTitle className="group-hover:text-orange-600 transition-colors">
                        {project.title}
                      </CardTitle>
                      <CardDescription>
                        by {project.author}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 mb-4">{project.description}</p>
                      <div className="flex items-center justify-between">
                        <Button variant="ghost" asChild>
                          <Link href={project.href}>
                            <span className="flex items-center">
                              View Project
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </span>
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Share2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Upcoming Events */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Upcoming Events</h2>
          <div className="space-y-4">
            {upcomingEvents.map((event, index) => (
              <Card key={index} className="group hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-2">
                    <Badge 
                      variant={event.type === 'Virtual' ? 'secondary' : 'default'}
                      className="text-xs"
                    >
                      {event.type}
                    </Badge>
                    <div className="flex items-center text-xs text-gray-500">
                      <Users className="w-3 h-3 mr-1" />
                      {event.attendees}
                    </div>
                  </div>
                  <CardTitle className="text-lg group-hover:text-orange-600 transition-colors">
                    {event.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {new Date(event.date).toLocaleDateString()} at {event.time}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      {event.location}
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-4">
                    Register
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Community Guidelines */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Community Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Be respectful and inclusive</li>
                <li>• Share knowledge and help others</li>
                <li>• Keep discussions on-topic</li>
                <li>• No spam or self-promotion</li>
                <li>• Follow platform-specific rules</li>
              </ul>
              <Button variant="outline" size="sm" asChild className="w-full mt-4">
                <Link href="/community/guidelines">
                  Read Full Guidelines
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Call to Action */}
      <section className="mt-12 bg-gradient-to-r from-orange-500 to-orange-600 p-8 text-white text-center">
        <h2 className="text-2xl font-bold mb-4">
          Ready to Join Our Community?
        </h2>
        <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
          Start connecting with fellow makers today. Share your projects, get help with challenges, 
          and be part of the growing Linnuoecosystem.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild className="bg-white text-orange-600 hover:bg-gray-100">
            <Link href="#discord">
              Join Discord Server
            </Link>
          </Button>
          <Button variant="outline" asChild className="border-white text-white hover:bg-white hover:text-orange-600">
            <Link href="/projects/submit">
              Share Your Project
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
