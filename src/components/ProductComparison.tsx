'use client';

import { useState } from 'react';
// 移除 next-intl 依赖,改用本地化字符串
import { Check, X, Plus, Minus } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: string;
  features: {
    [key: string]: boolean | string;
  };
}

const products: Product[] = [
  {
    id: 'basic',
    name: 'Basic Plan',
    price: '$9/month',
    features: {
      users: '5',
      storage: '10GB',
      support: true,
      analytics: false,
      api: false,
      customization: false,
      integrations: '3',
      backup: false
    }
  },
  {
    id: 'pro',
    name: 'Pro Plan',
    price: '$29/month',
    features: {
      users: '25',
      storage: '100GB',
      support: true,
      analytics: true,
      api: true,
      customization: true,
      integrations: '10',
      backup: true
    }
  },
  {
    id: 'enterprise',
    name: 'Enterprise Plan',
    price: '$99/month',
    features: {
      users: 'Unlimited',
      storage: '1TB',
      support: true,
      analytics: true,
      api: true,
      customization: true,
      integrations: 'Unlimited',
      backup: true
    }
  }
];

const featureLabels = {
  users: 'Max Users',
  storage: 'Storage',
  support: '24/7 Support',
  analytics: 'Advanced Analytics',
  api: 'API Access',
  customization: 'Custom Branding',
  integrations: 'Integrations',
  backup: 'Automated Backup'
};

export default function ProductComparison() {
  // 移除 useTranslations 的使用,因为已经不再依赖 next-intl
  const [selectedProducts, setSelectedProducts] = useState<string[]>(['basic', 'pro']);

  const addProduct = (productId: string) => {
    if (selectedProducts.length < 3 && !selectedProducts.includes(productId)) {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  const removeProduct = (productId: string) => {
    if (selectedProducts.length > 2) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="w-5 h-5 text-green-500 mx-auto" />
      ) : (
        <X className="w-5 h-5 text-red-500 mx-auto" />
      );
    }
    return <span className="text-gray-900 font-medium">{value}</span>;
  };

  const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));
  const availableProducts = products.filter(p => !selectedProducts.includes(p.id));

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Product Selection */}
      {availableProducts.length > 0 && selectedProducts.length < 3 && (
        <div className="p-6 bg-gray-50 border-b">
          <h3 className="text-lg font-semibold mb-4">Add Product to Compare</h3>
          <div className="flex gap-4">
            {availableProducts.map(product => (
              <button
                key={product.id}
                onClick={() => addProduct(product.id)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                {product.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Comparison Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="text-left p-6 font-semibold text-gray-900">Features</th>
              {selectedProductsData.map(product => (
                <th key={product.id} className="text-center p-6 min-w-[200px]">
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <h3 className="font-semibold text-gray-900">{product.name}</h3>
                      {selectedProducts.length > 2 && (
                        <button
                          onClick={() => removeProduct(product.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                    <div className="text-2xl font-bold text-blue-600">{product.price}</div>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Object.entries(featureLabels).map(([key, label], index) => (
              <tr key={key} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="p-6 font-medium text-gray-900">{label}</td>
                {selectedProductsData.map(product => (
                  <td key={product.id} className="p-6 text-center">
                    {renderFeatureValue(product.features[key])}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Action Buttons */}
      <div className="p-6 bg-gray-50 border-t">
        <div className="flex justify-center gap-4">
          {selectedProductsData.map(product => (
            <button
              key={product.id}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Choose {product.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}