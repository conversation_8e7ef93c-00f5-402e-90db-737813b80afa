'use client'

import { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'

interface AnimatedBackgroundProps {
  className?: string
  variant?: 'grid' | 'dots' | 'waves' | 'gradient'
  primaryColor?: string
  secondaryColor?: string
  speed?: number
  density?: number
  interactive?: boolean
}

export function AnimatedBackground({
  className = '',
  variant = 'grid',
  primaryColor = 'rgba(255, 107, 53, 0.1)', // Linnuo橙色
  secondaryColor = 'rgba(30, 58, 138, 0.05)', // 深蓝色
  speed = 1,
  density = 1,
  interactive = true
}: AnimatedBackgroundProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  
  // 网格背景动画
  if (variant === 'grid') {
    return (
      <div 
        className={`absolute inset-0 overflow-hidden ${className}`}
        style={{
          backgroundImage: `
            linear-gradient(${primaryColor} 1px, transparent 1px),
            linear-gradient(90deg, ${primaryColor} 1px, transparent 1px)
          `,
          backgroundSize: `${20 * density}px ${20 * density}px`
        }}
      >
        <motion.div
          className="absolute inset-0"
          animate={{
            backgroundPosition: ['0px 0px', `${20 * density}px ${20 * density}px`]
          }}
          transition={{
            duration: 20 / speed,
            ease: "linear",
            repeat: Infinity
          }}
          style={{
            backgroundImage: `
              linear-gradient(${secondaryColor} 1px, transparent 1px),
              linear-gradient(90deg, ${secondaryColor} 1px, transparent 1px)
            `,
            backgroundSize: `${20 * density}px ${20 * density}px`
          }}
        />
      </div>
    )
  }
  
  // 点阵背景动画
  if (variant === 'dots') {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        <motion.div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(${primaryColor} 1px, transparent 1px)`,
            backgroundSize: `${20 * density}px ${20 * density}px`
          }}
          animate={{
            backgroundPosition: ['0px 0px', `${10 * density}px ${10 * density}px`]
          }}
          transition={{
            duration: 15 / speed,
            ease: "linear",
            repeat: Infinity
          }}
        />
      </div>
    )
  }
  
  // 波浪背景动画
  if (variant === 'waves') {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        <svg 
          className="absolute bottom-0 left-0 right-0 w-full h-auto"
          viewBox="0 0 1440 320"
          preserveAspectRatio="none"
        >
          <motion.path 
            fill={primaryColor}
            initial={{ d: "M0,160L48,144C96,128,192,96,288,106.7C384,117,480,171,576,186.7C672,203,768,181,864,154.7C960,128,1056,96,1152,96C1248,96,1344,128,1392,144L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
            animate={{ 
              d: [
                "M0,160L48,144C96,128,192,96,288,106.7C384,117,480,171,576,186.7C672,203,768,181,864,154.7C960,128,1056,96,1152,96C1248,96,1344,128,1392,144L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                "M0,192L48,197.3C96,203,192,213,288,192C384,171,480,117,576,112C672,107,768,149,864,165.3C960,181,1056,171,1152,149.3C1248,128,1344,96,1392,80L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                "M0,128L48,149.3C96,171,192,213,288,213.3C384,213,480,171,576,149.3C672,128,768,128,864,149.3C960,171,1056,213,1152,218.7C1248,224,1344,192,1392,176L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              ]
            }}
            transition={{
              duration: 20 / speed,
              times: [0, 0.5, 1],
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />
          
          <motion.path 
            fill={secondaryColor}
            initial={{ d: "M0,256L48,261.3C96,267,192,277,288,266.7C384,256,480,224,576,218.7C672,213,768,235,864,234.7C960,235,1056,213,1152,202.7C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z" }}
            animate={{ 
              d: [
                "M0,256L48,261.3C96,267,192,277,288,266.7C384,256,480,224,576,218.7C672,213,768,235,864,234.7C960,235,1056,213,1152,202.7C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                "M0,288L48,277.3C96,267,192,245,288,240C384,235,480,245,576,250.7C672,256,768,256,864,245.3C960,235,1056,213,1152,213.3C1248,213,1344,235,1392,245.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z",
                "M0,224L48,229.3C96,235,192,245,288,261.3C384,277,480,299,576,293.3C672,288,768,256,864,250.7C960,245,1056,267,1152,272C1248,277,1344,267,1392,261.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              ]
            }}
            transition={{
              duration: 15 / speed,
              times: [0, 0.5, 1],
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: 0.5 // 错开两个波浪的动画
            }}
          />
        </svg>
      </div>
    )
  }
  
  // 渐变背景动画
  if (variant === 'gradient') {
    return (
      <motion.div 
        className={`absolute inset-0 ${className}`}
        style={{
          background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
          backgroundSize: '400% 400%'
        }}
        animate={{
          backgroundPosition: ['0% 0%', '100% 100%']
        }}
        transition={{
          duration: 10 / speed,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      />
    )
  }
  
  // 默认返回网格背景
  return (
    <div 
      className={`absolute inset-0 overflow-hidden ${className}`}
      style={{
        backgroundImage: `
          linear-gradient(${primaryColor} 1px, transparent 1px),
          linear-gradient(90deg, ${primaryColor} 1px, transparent 1px)
        `,
        backgroundSize: `${20 * density}px ${20 * density}px`
      }}
    />
  )
}