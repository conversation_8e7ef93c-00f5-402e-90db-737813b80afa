'use client'

import { ReactNode } from 'react'
import { motion, Variants } from 'framer-motion'

type AnimationDirection = 'up' | 'down' | 'left' | 'right' | 'scale' | 'fade'

interface ScrollRevealProps {
  children: ReactNode
  className?: string
  direction?: AnimationDirection
  distance?: number
  duration?: number
  delay?: number
  staggerChildren?: number
  threshold?: number
  once?: boolean
}

export function ScrollReveal({
  children,
  className = '',
  direction = 'up',
  distance = 50,
  duration = 0.6,
  delay = 0,
  staggerChildren = 0,
  threshold = 0.1,
  once = true
}: ScrollRevealProps) {
  // 根据方向设置不同的动画变体
  const getVariants = (): Variants => {
    const variants: Variants = {
      hidden: {},
      visible: {
        transition: {
          duration,
          delay,
          staggerChildren
        }
      }
    }
    
    switch (direction) {
      case 'up':
        variants.hidden = { opacity: 0, y: distance }
        variants.visible = { opacity: 1, y: 0 }
        break
      case 'down':
        variants.hidden = { opacity: 0, y: -distance }
        variants.visible = { opacity: 1, y: 0 }
        break
      case 'left':
        variants.hidden = { opacity: 0, x: distance }
        variants.visible = { opacity: 1, x: 0 }
        break
      case 'right':
        variants.hidden = { opacity: 0, x: -distance }
        variants.visible = { opacity: 1, x: 0 }
        break
      case 'scale':
        variants.hidden = { opacity: 0, scale: 0.8 }
        variants.visible = { opacity: 1, scale: 1 }
        break
      case 'fade':
      default:
        variants.hidden = { opacity: 0 }
        variants.visible = { opacity: 1 }
        break
    }
    
    return variants
  }
  
  const variants = getVariants()
  
  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, amount: threshold }}
      variants={variants}
    >
      {children}
    </motion.div>
  )
}

// 子元素动画组件
export function ScrollRevealItem({
  children,
  className = '',
  direction = 'up',
  distance = 30,
  duration = 0.5,
  delay = 0
}: Omit<ScrollRevealProps, 'staggerChildren' | 'threshold' | 'once'>) {
  // 根据方向设置不同的动画变体
  const getVariants = (): Variants => {
    const variants: Variants = {
      hidden: {},
      visible: {
        transition: {
          duration,
          delay
        }
      }
    }
    
    switch (direction) {
      case 'up':
        variants.hidden = { opacity: 0, y: distance }
        variants.visible = { opacity: 1, y: 0 }
        break
      case 'down':
        variants.hidden = { opacity: 0, y: -distance }
        variants.visible = { opacity: 1, y: 0 }
        break
      case 'left':
        variants.hidden = { opacity: 0, x: distance }
        variants.visible = { opacity: 1, x: 0 }
        break
      case 'right':
        variants.hidden = { opacity: 0, x: -distance }
        variants.visible = { opacity: 1, x: 0 }
        break
      case 'scale':
        variants.hidden = { opacity: 0, scale: 0.8 }
        variants.visible = { opacity: 1, scale: 1 }
        break
      case 'fade':
      default:
        variants.hidden = { opacity: 0 }
        variants.visible = { opacity: 1 }
        break
    }
    
    return variants
  }
  
  const variants = getVariants()
  
  return (
    <motion.div
      className={className}
      variants={variants}
    >
      {children}
    </motion.div>
  )
}