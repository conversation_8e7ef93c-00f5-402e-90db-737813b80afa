'use client'

import { useState, useRef, ReactNode } from 'react'
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion'

interface Animated3DCardProps {
  children: ReactNode
  className?: string
  backgroundGradient?: boolean
  glowColor?: string
  hoverScale?: number
  rotationIntensity?: number
}

export function Animated3DCard({
  children,
  className = '',
  backgroundGradient = true,
  glowColor = 'rgba(255, 107, 53, 0.4)', // 默认使用橙色光晕（Linnuo品牌色）
  hoverScale = 1.05,
  rotationIntensity = 10
}: Animated3DCardProps) {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  
  // 创建动画值
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  
  // 添加弹簧效果使动画更自然
  const springConfig = { damping: 20, stiffness: 150 }
  const rotateX = useSpring(useTransform(mouseY, [0, 1], [rotationIntensity, -rotationIntensity]), springConfig)
  const rotateY = useSpring(useTransform(mouseX, [0, 1], [-rotationIntensity, rotationIntensity]), springConfig)
  
  // 光晕位置
  const glowX = useSpring(useTransform(mouseX, [0, 1], [-50, 50]), springConfig)
  const glowY = useSpring(useTransform(mouseY, [0, 1], [-50, 50]), springConfig)
  
  // 处理鼠标移动
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return
    
    const rect = cardRef.current.getBoundingClientRect()
    
    // 计算鼠标在卡片上的相对位置（0-1范围）
    const normalizedX = (e.clientX - rect.left) / rect.width
    const normalizedY = (e.clientY - rect.top) / rect.height
    
    mouseX.set(normalizedX)
    mouseY.set(normalizedY)
  }

  return (
    <motion.div
      ref={cardRef}
      className={`relative overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        rotateX,
        rotateY,
        transformStyle: 'preserve-3d',
        perspective: 800,
      }}
      whileHover={{ scale: hoverScale }}
      transition={{ duration: 0.3 }}
    >
      {/* 内容容器 */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* 背景渐变 */}
      {backgroundGradient && (
        <motion.div 
          className="absolute inset-0 bg-gradient-to-br from-Linnuo-orange/20 via-transparent to-Linnuo-orange-dark/20 opacity-0 transition-opacity duration-300"
          style={{ opacity: isHovered ? 0.8 : 0 }}
        />
      )}
      
      {/* 光晕效果 */}
      <motion.div
        className="absolute -inset-[100px] opacity-0 transition-opacity duration-300 pointer-events-none"
        style={{
          background: `radial-gradient(circle, ${glowColor} 0%, transparent 70%)`,
          top: glowY,
          left: glowX,
          opacity: isHovered ? 0.6 : 0
        }}
      />
    </motion.div>
  )
}