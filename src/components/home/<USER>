'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, useInView } from 'framer-motion'

interface AnimatedCounterProps {
  end: number
  duration?: number
  delay?: number
  prefix?: string
  suffix?: string
  className?: string
  decimals?: number
  threshold?: number
  once?: boolean
  easing?: 'linear' | 'easeOut' | 'easeIn' | 'easeInOut'
}

export function AnimatedCounter({
  end,
  duration = 2,
  delay = 0,
  prefix = '',
  suffix = '',
  className = '',
  decimals = 0,
  threshold = 0.1,
  once = true,
  easing = 'easeOut'
}: AnimatedCounterProps) {
  const [count, setCount] = useState(0)
  const ref = useRef<HTMLSpanElement>(null)
  const inView = useInView(ref, { once, amount: threshold })
  const [hasAnimated, setHasAnimated] = useState(false)
  
  // 格式化数字，添加千位分隔符
  const formatNumber = (num: number): string => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  }
  
  // 根据缓动类型获取缓动函数
  const getEasingFunction = (type: string) => {
    switch (type) {
      case 'linear':
        return (t: number) => t
      case 'easeIn':
        return (t: number) => t * t
      case 'easeOut':
        return (t: number) => 1 - Math.pow(1 - t, 2)
      case 'easeInOut':
        return (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2
      default:
        return (t: number) => 1 - Math.pow(1 - t, 2) // 默认easeOut
    }
  }
  
  useEffect(() => {
    if (!inView || hasAnimated) return
    
    let startTimestamp: number | null = null
    const easingFunction = getEasingFunction(easing)
    
    // 延迟开始动画
    const timeoutId = setTimeout(() => {
      const step = (timestamp: number) => {
        if (!startTimestamp) startTimestamp = timestamp
        const progress = Math.min((timestamp - startTimestamp) / (duration * 1000), 1)
        const easedProgress = easingFunction(progress)
        
        setCount(Math.floor(easedProgress * end))
        
        if (progress < 1) {
          requestAnimationFrame(step)
        } else {
          setCount(end)
          setHasAnimated(true)
        }
      }
      
      requestAnimationFrame(step)
    }, delay * 1000)
    
    return () => clearTimeout(timeoutId)
  }, [inView, end, duration, delay, easing, hasAnimated])
  
  return (
    <motion.span
      ref={ref}
      className={className}
      initial={{ opacity: 0, y: 10 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
      transition={{ duration: 0.5, delay }}
    >
      {prefix}{formatNumber(count)}{suffix}
    </motion.span>
  )
}