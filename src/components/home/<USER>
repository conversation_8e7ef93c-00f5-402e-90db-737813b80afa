'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'

interface FloatingElementProps {
  children: ReactNode
  className?: string
  duration?: number
  delay?: number
  yOffset?: number
  rotationRange?: number
  enableRotation?: boolean
}

export function FloatingElement({
  children,
  className = '',
  duration = 4, // 动画周期时长（秒）
  delay = 0, // 动画延迟（秒）
  yOffset = 15, // 上下浮动的像素范围
  rotationRange = 3, // 旋转角度范围
  enableRotation = false // 是否启用旋转
}: FloatingElementProps) {
  // 浮动动画变体
  const floatingAnimation = {
    y: [
      0, 
      -yOffset / 2, 
      0, 
      yOffset / 2, 
      0
    ],
    transition: {
      duration,
      ease: "easeInOut",
      repeat: Infinity,
      delay
    }
  }
  
  // 旋转动画变体
  const rotationAnimation = enableRotation ? {
    rotate: [
      0,
      rotationRange,
      0,
      -rotationRange,
      0
    ],
    transition: {
      duration: duration * 1.2, // 稍微慢于浮动动画
      ease: "easeInOut",
      repeat: Infinity,
      delay
    }
  } : {}
  
  return (
    <motion.div
      className={className}
      animate={{
        ...floatingAnimation,
        ...rotationAnimation
      }}
      style={{ willChange: 'transform' }} // 性能优化
    >
      {children}
    </motion.div>
  )
}