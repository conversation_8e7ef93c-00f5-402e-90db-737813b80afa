'use client'

import { Card, CardContent } from '@/components/ui/card'
import {
  Cpu,
  Zap,
  Shield,
  Code,
  Wifi,
  HardDrive,
  Monitor,
  Gamepad2
} from 'lucide-react'
import { useHomeTranslations } from '@/hooks/use-translations'

// 特性配置将在组件内部动态生成

export function FeatureHighlights() {
  const { t } = useHomeTranslations()

  // 动态生成特性配置
  const features = [
    {
      icon: Cpu,
      titleKey: 'intelProcessors',
      color: 'text-blue-600'
    },
    {
      icon: Zap,
      titleKey: 'highPerformance',
      color: 'text-yellow-600'
    },
    {
      icon: Shield,
      titleKey: 'reliableStable',
      color: 'text-green-600'
    },
    {
      icon: Code,
      titleKey: 'developerFriendly',
      color: 'text-purple-600'
    },
    {
      icon: Wifi,
      titleKey: 'advancedConnectivity',
      color: 'text-blue-600'
    },
    {
      icon: HardDrive,
      titleKey: 'expandableStorage',
      color: 'text-red-600'
    },
    {
      icon: Monitor,
      titleKey: 'displaySupport',
      color: 'text-indigo-600'
    },
    {
      icon: Gamepad2,
      titleKey: 'gamingReady',
      color: 'text-pink-600'
    }
  ]

  return (
    <section className="relative py-20 lg:py-28 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 overflow-hidden">

      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">

          
          <h2 
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
            dangerouslySetInnerHTML={{ __html: t("featureHighlights.title") }}
          />
          <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t("featureHighlights.subtitle")}
          </p>
          

        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6 lg:gap-10">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <div
                key={index}
                className="group relative bg-white/80 backdrop-blur-sm p-3 md:p-6 lg:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200/50 hover:border-blue-600/30 transform hover:-translate-y-2"
                style={{
                  animationDelay: `${index * 150}ms`,
                  animation: 'fadeInUp 0.8s ease-out forwards'
                }}
              >

                
                <div className="relative z-10 text-center">
                  {/* 图标容器 */}
                  <div className="mb-3 md:mb-4 lg:mb-6">
                    <div className="relative inline-block">
                      <div className={`w-8 h-8 md:w-12 md:h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110 ${feature.color}`}>
                        <IconComponent className="w-4 h-4 md:w-6 md:h-6 lg:w-8 lg:h-8 text-white" />
                      </div>

                    </div>
                  </div>
                  
                  {/* 标题 */}
                  <h3 className="text-xs md:text-lg lg:text-xl font-bold text-gray-900 mb-2 md:mb-3 lg:mb-4 group-hover:text-blue-600 transition-colors duration-300">
                    {t(`featureHighlights.features.${feature.titleKey}.title`)}
                  </h3>
                  
                  {/* 描述 */}
                  <p className="text-gray-600 leading-relaxed text-xs md:text-sm">
                    {t(`featureHighlights.features.${feature.titleKey}.description`)}
                  </p>
                  

                </div>
              </div>
            )
          })}
        </div>
        

      </div>
      
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(40px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </section>
  )
}
