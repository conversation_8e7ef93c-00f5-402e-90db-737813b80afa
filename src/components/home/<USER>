'use client'

import { useState, useEffect } from 'react'
import { motion, useAnimation, Variants } from 'framer-motion'

interface AnimatedTextProps {
  text: string
  className?: string
  type?: 'typing' | 'reveal' | 'gradient'
  typingSpeed?: number
  delay?: number
  loop?: boolean
  loopDelay?: number
  gradientColors?: string[]
}

export function AnimatedText({
  text,
  className = '',
  type = 'reveal',
  typingSpeed = 50, // 打字速度（毫秒/字符）
  delay = 0, // 初始延迟（秒）
  loop = false,
  loopDelay = 2, // 循环延迟（秒）
  gradientColors = ['#FF6B35', '#F7931E', '#1E3A8A'] // 默认渐变色（Linnuo品牌色）
}: AnimatedTextProps) {
  const controls = useAnimation()
  const [displayText, setDisplayText] = useState('')
  
  // 文本显示动画变体
  const textVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay,
        staggerChildren: 0.05,
        delayChildren: delay
      }
    }
  }
  
  // 字符动画变体
  const characterVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0
    }
  }
  
  // 打字机效果
  useEffect(() => {
    if (type !== 'typing') return
    
    let currentIndex = 0
    setDisplayText('')
    
    const startTyping = () => {
      const typingInterval = setInterval(() => {
        if (currentIndex < text.length) {
          setDisplayText(prev => prev + text[currentIndex])
          currentIndex++
        } else {
          clearInterval(typingInterval)
          
          if (loop) {
            setTimeout(() => {
              setDisplayText('')
              currentIndex = 0
              startTyping()
            }, loopDelay * 1000)
          }
        }
      }, typingSpeed)
      
      return () => clearInterval(typingInterval)
    }
    
    const typingTimeout = setTimeout(startTyping, delay * 1000)
    return () => clearTimeout(typingTimeout)
  }, [text, typingSpeed, delay, loop, loopDelay, type])
  
  // 渐变文本效果
  if (type === 'gradient') {
    const gradientStyle = {
      backgroundImage: `linear-gradient(135deg, ${gradientColors.join(', ')})`,
      backgroundSize: '200% 200%',
      backgroundClip: 'text',
      WebkitBackgroundClip: 'text',
      color: 'transparent',
      WebkitTextFillColor: 'transparent',
      animation: 'gradient-animation 5s ease infinite',
    }
    
    return (
      <motion.div
        className={className}
        style={gradientStyle}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay, duration: 0.5 }}
      >
        {text}
      </motion.div>
    )
  }
  
  // 打字机效果
  if (type === 'typing') {
    return (
      <div className={`${className} inline-block`}>
        {displayText}
        <motion.span
          animate={{ opacity: [0, 1, 0] }}
          transition={{ repeat: Infinity, duration: 0.8 }}
          className="inline-block w-[2px] h-[1em] bg-current ml-[2px] align-middle"
        />
      </div>
    )
  }
  
  // 默认文本显示动画（逐字显示）
  return (
    <motion.div
      className={className}
      variants={textVariants}
      initial="hidden"
      animate="visible"
    >
      {text.split('').map((char, index) => (
        <motion.span
          key={`${char}-${index}`}
          variants={characterVariants}
          className="inline-block"
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.div>
  )
}