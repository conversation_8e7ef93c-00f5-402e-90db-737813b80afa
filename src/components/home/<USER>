'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Mail, 
  CheckCircle, 
  Gift, 
  Zap, 
  Bell,
  Users,
  Sparkles
} from 'lucide-react'

const benefits = [
  {
    icon: Zap,
    title: 'Early Access',
    description: 'Be the first to know about new products and features'
  },
  {
    icon: Gift,
    title: 'Exclusive Offers',
    description: 'Special discounts and promotions for subscribers'
  },
  {
    icon: Bell,
    title: 'Project Updates',
    description: 'Latest tutorials, guides, and community highlights'
  },
  {
    icon: Users,
    title: 'Community Events',
    description: 'Invitations to webinars, workshops, and meetups'
  }
]

export function NewsletterSection() {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsSubscribed(true)
    setIsLoading(false)
    setEmail('')
  }

  if (isSubscribed) {
    return (
      <section className="py-16 lg:py-24 bg-gradient-to-br from-orange-500 via-orange-600 to-red-600">
        <div className="container mx-auto px-4">
          <Card className="max-w-2xl mx-auto text-center">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-green-100 flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome to the Community! 🎉
              </h3>
              <p className="text-gray-600 mb-6">
                Thank you for subscribing! You'll receive our latest updates, 
                exclusive content, and special offers directly in your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <a href="/products">Explore Products</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/community">Join Community</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-orange-500 via-orange-600 to-red-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 blur-xl"></div>
        <div className="absolute top-32 right-20 w-32 h-32 bg-white/5 blur-2xl"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 blur-xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <Badge className="bg-white/20 text-white border-white/30 mb-4">
              <Sparkles className="w-3 h-3 mr-1" />
              Stay Connected
            </Badge>
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Never Miss an Update
            </h2>
            <p className="text-xl text-orange-100 max-w-2xl mx-auto">
              Join 50,000+ makers and developers who get the latest Linnuonews, 
              tutorials, and exclusive offers delivered to their inbox.
            </p>
          </div>

          {/* Newsletter Form */}
          <Card className="mb-12 shadow-2xl">
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="h-12 text-lg"
                    disabled={isLoading}
                  />
                </div>
                <Button 
                  type="submit" 
                  size="lg" 
                  className="bg-orange-500 hover:bg-orange-600 h-12 px-8"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-b-2 border-white rounded-full mr-2"></div>
                      Subscribing...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4 mr-2" />
                      Subscribe
                    </>
                  )}
                </Button>
              </form>

              <p className="text-sm text-gray-500 text-center">
                By subscribing, you agree to our Privacy Policy and consent to receive updates from our team.
              </p>
            </CardContent>
          </Card>

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-white/20 flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-orange-100 text-sm">
                    {benefit.description}
                  </p>
                </div>
              )
            })}
          </div>

          {/* Social Proof */}
          <div className="text-center mt-12">
            <div className="flex items-center justify-center gap-2 text-orange-100 mb-4">
              <Users className="w-5 h-5" />
              <span className="text-lg font-medium">
                Join 50,000+ subscribers worldwide
              </span>
            </div>
            <div className="flex justify-center items-center gap-8 text-white/80">
              <div className="text-center">
                <div className="text-2xl font-bold">98%</div>
                <div className="text-sm">Satisfaction Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">Weekly</div>
                <div className="text-sm">Updates</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">0</div>
                <div className="text-sm">Spam</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
