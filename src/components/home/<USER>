'use client'

import { useHomeTranslations } from '@/hooks/use-translations'
import { useEffect, useState } from 'react'
import Image from 'next/image'
// 移除图标导入

export function CommunitySection() {
  const { t } = useHomeTranslations()
  const [counters, setCounters] = useState([0, 0, 0, 0])

  // 简化的统计数据配置
  const companyStats = [
    {
      value: 7,
      suffix: '年',
      labelKey: 'founded'
    },
    {
      value: 50,
      suffix: '+',
      labelKey: 'globalUsers'
    },
    {
      value: 30,
      suffix: '+',
      labelKey: 'countries'
    },
    {
      value: 6000,
      suffix: '+',
      labelKey: 'awards'
    }
  ]

  // 数字动画效果
  useEffect(() => {
    const duration = 2000 // 2秒动画
    const steps = 60 // 60帧
    const stepTime = duration / steps

    companyStats.forEach((stat, index) => {
      let currentStep = 0
      const increment = stat.value / steps
      
      const timer = setInterval(() => {
        currentStep++
        const currentValue = Math.min(Math.floor(increment * currentStep), stat.value)
        
        setCounters(prev => {
          const newCounters = [...prev]
          newCounters[index] = currentValue
          return newCounters
        })
        
        if (currentStep >= steps) {
          clearInterval(timer)
        }
      }, stepTime)
    })
  }, [])

  

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
            dangerouslySetInnerHTML={{ __html: t("communitySection.title") }}
          />
          <div className="text-lg text-gray-600 max-w-3xl mx-auto whitespace-pre-line">
            {t("communitySection.subtitle")}
          </div>
        </div>

        {/* 左右布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* 左侧：公司介绍文字 + 统计数据 */}
          <div className="space-y-8">
            {/* 公司介绍文字 */}
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                {t("communitySection.description.paragraph1")}
              </p>
              <p>
                {t("communitySection.description.paragraph2")}
              </p>
              <p>
                {t("communitySection.description.paragraph3")}
              </p>
            </div>
            
            {/* 统计数据 */}
            <div className="grid grid-cols-2 gap-6">
              {companyStats.map((stat, index) => {
                return (
                  <div 
                    key={index} 
                    className="bg-white border border-gray-200 text-center p-6 rounded-lg shadow-sm"
                  >
                    {/* 数字 */}
                    <div className="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">
                      {counters[index]}{stat.suffix}
                    </div>
                    
                    {/* 标签 */}
                    <div className="text-sm font-medium text-gray-600">
                      {t(`communitySection.stats.${stat.labelKey}`)}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* 右侧：公司照片 */}
          <div className="relative">
            <div className="relative overflow-hidden">
              <div className="relative w-full aspect-[4/3]">
                <Image
                  src="/images/company.png"
                  alt="公司厂房"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
                />
              </div>
              {/* 覆盖层 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
