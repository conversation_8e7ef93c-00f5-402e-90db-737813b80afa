'use client'

import React, { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { getHeroSlides, type HeroSlide } from '@/data/unified-data-fetcher'
import { OptimizedImage } from '@/components/ui/optimized-image'
// import { useTranslations } from '@/hooks/use-translations' // 不再需要
import { useImageCarousel, useCarouselKeyboardNavigation, useCarouselTouchGestures } from '@/hooks/use-image-carousel'
import { cn } from '@/lib/utils'

interface ImageCarouselProps {
  height?: string
  autoPlayInterval?: number
  showControls?: boolean
  showIndicators?: boolean
  className?: string
}

export function ImageCarousel({
  height = 'w-full aspect-[2500/734] max-h-[600px]',
  autoPlayInterval = 6000,
  showControls = true,
  showIndicators = true,
  className = ''
}: ImageCarouselProps) {
  // 使用轮播图Hook
  const {
    slides,
    currentSlide,
    isAutoPlaying,
    isLoading,
    error,
    nextSlide,
    prevSlide,
    goToSlide,
    setAutoPlaying,
    currentSlideData
  } = useImageCarousel({ autoPlayInterval, autoPlay: true })

  // const { t } = useTranslations() // 不再需要

  // 使用键盘导航和触摸手势hooks
  useCarouselKeyboardNavigation(nextSlide, prevSlide, setAutoPlaying, isAutoPlaying)
  const touchHandlers = useCarouselTouchGestures(nextSlide, prevSlide, setAutoPlaying)



  // 加载状态
  if (isLoading) {
    return (
      <div className={`relative ${height} bg-gray-100 ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="text-gray-600">Loading images...</p>
          </div>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error && slides.length === 0) {
    return (
      <div className={`relative ${height} bg-gray-100 ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center space-y-4">
            <p className="text-red-600">Error: {error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-orange-500 text-white hover:bg-orange-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className={cn(
        'relative w-full overflow-hidden bg-gray-100',
        height,
        'min-h-[200px] sm:min-h-[300px] md:min-h-[400px]',
        className
      )}
      onMouseEnter={() => setAutoPlaying(false)}
      onMouseLeave={() => setAutoPlaying(true)}
      {...touchHandlers}
    >
      {/* 主图片 */}
      {currentSlideData.ctaLink ? (
        <Link href={currentSlideData.ctaLink} className="block w-full h-full">
          <div className="relative w-full h-full">
            <OptimizedImage
              src={currentSlideData.image}
              alt={currentSlideData.title}
              fill
              className="object-contain transition-transform duration-700 hover:scale-105"
              style={{
                objectPosition: 'center'
              }}
              priority={currentSlide === 0}
              quality={90}
              sizes="100vw"
            />
          </div>
        </Link>
      ) : (
        <div className="relative w-full h-full">
          <OptimizedImage
            src={currentSlideData.image}
            alt={currentSlideData.title}
            fill
            className="object-contain transition-transform duration-700"
            style={{
              objectPosition: 'center'
            }}
            priority={currentSlide === 0}
            quality={90}
            sizes="100vw"
          />
        </div>
      )}

      {/* 渐变遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none" />

      {/* 导航控制 */}
      {showControls && slides.length > 1 && (
        <>
          {/* 左箭头 */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white shadow-lg transition-all duration-200 group z-10"
            aria-label="Previous image"
          >
            <ChevronLeft className="w-6 h-6 text-gray-700 group-hover:text-orange-500 transition-colors" />
          </button>

          {/* 右箭头 */}
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white shadow-lg transition-all duration-200 group z-10"
            aria-label="Next image"
          >
            <ChevronRight className="w-6 h-6 text-gray-700 group-hover:text-orange-500 transition-colors" />
          </button>
        </>
      )}

      {/* 指示器 */}
      {showIndicators && slides.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 transition-all duration-300 ${
                index === currentSlide 
                  ? 'bg-white w-8' 
                  : 'bg-white/60 hover:bg-white/80'
              }`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>
      )}


    </div>
  )
}
