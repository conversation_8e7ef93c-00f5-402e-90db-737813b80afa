'use client'

import { useRef, ReactNode } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'

interface ParallaxSectionProps {
  children: ReactNode
  className?: string
  direction?: 'up' | 'down' | 'left' | 'right'
  speed?: number
  offset?: number
}

export function ParallaxSection({
  children,
  className = '',
  direction = 'up',
  speed = 0.3, // 视差效果强度，值越大效果越强
  offset = 0 // 开始动画的偏移量
}: ParallaxSectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null)
  
  // 使用滚动进度来驱动动画
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: [`${offset} 1`, '1 0']
  })
  
  // 根据方向设置不同的变换
  let transform
  const distance = 100 * speed // 移动距离（像素）
  
  switch (direction) {
    case 'up':
      transform = useTransform(scrollYProgress, [0, 1], [`translateY(${distance}px)`, 'translateY(0)'])
      break
    case 'down':
      transform = useTransform(scrollYProgress, [0, 1], [`translateY(-${distance}px)`, 'translateY(0)'])
      break
    case 'left':
      transform = useTransform(scrollYProgress, [0, 1], [`translateX(${distance}px)`, 'translateX(0)'])
      break
    case 'right':
      transform = useTransform(scrollYProgress, [0, 1], [`translateX(-${distance}px)`, 'translateX(0)'])
      break
    default:
      transform = useTransform(scrollYProgress, [0, 1], [`translateY(${distance}px)`, 'translateY(0)'])
  }
  
  // 同时添加淡入效果
  const opacity = useTransform(scrollYProgress, [0, 0.5], [0, 1])
  
  return (
    <div ref={sectionRef} className={`relative overflow-hidden ${className}`}>
      <motion.div
        style={{ 
          transform,
          opacity,
          willChange: 'transform, opacity' // 性能优化
        }}
        className="w-full h-full"
      >
        {children}
      </motion.div>
    </div>
  )
}