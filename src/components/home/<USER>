'use client'

import React, { useState, useRef } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { useCounterAnimation } from '@/hooks/use-counter-animation'
import { LucideIcon } from 'lucide-react'
import { ReactNode } from 'react'

interface AnimatedStatsCardProps {
  icon: LucideIcon | ReactNode
  value: string | number
  label: string
  color: string
  index: number
}

export function AnimatedStatsCard({
  icon,
  value,
  label,
  color,
  index
}: AnimatedStatsCardProps) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // 使用数字动画 Hook
  const { ref: counterRef, value: animatedValue, isAnimating } = useCounterAnimation({
    end: value,
    duration: 2000 + index * 200, // 错开动画时间
    start: 0
  })

  // 处理鼠标移动视差效果
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return

    const rect = cardRef.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const deltaX = (e.clientX - centerX) / rect.width
    const deltaY = (e.clientY - centerY) / rect.height

    setMousePosition({
      x: deltaX * 10, // 调整视差强度
      y: deltaY * 10
    })
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    setMousePosition({ x: 0, y: 0 })
  }

  return (
    <Card
      ref={cardRef}
      className="text-center hover:shadow-2xl transition-all duration-500 bg-white/10 backdrop-blur-sm border border-white/20 hover:border-white/40 shadow-lg group overflow-hidden relative cursor-pointer"
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        transform: isHovered
          ? `perspective(1000px) rotateX(${-mousePosition.y * 0.5}deg) rotateY(${mousePosition.x * 0.5}deg) translateZ(30px) scale(1.05)`
          : 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px) scale(1)',
        transition: isHovered ? 'transform 0.1s ease-out' : 'transform 0.5s ease-out',
        transformStyle: 'preserve-3d',
        animationDelay: `${index * 100}ms`,
        animation: 'fadeInUp 0.8s ease-out forwards'
      }}
    >
      {/* 背景渐变效果 */}
      <div
        className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
          background: `radial-gradient(circle at ${50 + mousePosition.x * 2}% ${50 + mousePosition.y * 2}%, rgba(255,165,0,0.15) 0%, rgba(59,130,246,0.05) 50%, transparent 70%)`
        }}
      />

      {/* 边框光效 */}
      <div
        className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
          background: `linear-gradient(45deg, transparent 30%, rgba(255,165,0,0.3) 50%, transparent 70%)`,
          mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          maskComposite: 'xor',
          padding: '2px'
        }}
      />
      
      {/* 背景光晕 */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent blur-xl group-hover:from-white/20 transition-all duration-500"></div>
      
      <CardContent className="p-6 relative z-10">
        {/* 图标容器 */}
        <div 
          className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-Linnuo-orange to-orange-600 mb-6 group-hover:scale-110 transition-all duration-500 shadow-lg group-hover:shadow-xl`}
          style={{
            transform: isHovered 
              ? `translate(${mousePosition.x * 2}px, ${mousePosition.y * 2}px)` 
              : 'translate(0px, 0px)'
          }}
        >
          {React.isValidElement(icon) ? (
            React.cloneElement(icon as React.ReactElement, {
              className: "w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300"
            })
          ) : typeof icon === 'function' ? (
            React.createElement(icon as LucideIcon, {
              className: "w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300"
            })
          ) : (
            <div className="w-8 h-8 bg-white/20 flex items-center justify-center">
              <span className="text-white text-sm">?</span>
            </div>
          )}
        </div>

        {/* 数字显示 */}
        <div
          ref={counterRef}
          className={`text-4xl lg:text-5xl font-bold mb-3 transition-all duration-300 ${
            isAnimating
              ? 'text-white animate-pulse'
              : 'text-white group-hover:text-blue-200'
          }`}
          style={{
            transform: isHovered
              ? `translate(${mousePosition.x}px, ${mousePosition.y}px) scale(1.05)`
              : 'translate(0px, 0px) scale(1)',
            textShadow: isHovered
              ? '0 4px 12px rgba(255,255,255,0.3)'
              : isAnimating
                ? '0 2px 8px rgba(255,255,255,0.2)'
                : 'none',
            filter: isAnimating ? 'brightness(1.1)' : 'brightness(1)'
          }}
        >
          {animatedValue}
        </div>

        {/* 标签 */}
        <div 
          className="text-blue-200 font-medium transition-colors duration-300"
          style={{
            transform: isHovered 
              ? `translate(${mousePosition.x * 0.5}px, ${mousePosition.y * 0.5}px)` 
              : 'translate(0px, 0px)'
          }}
        >
          {label}
        </div>

        {/* 装饰性光效 */}
        <div 
          className="absolute top-0 left-0 w-full h-full pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)`,
            transform: `translateX(${mousePosition.x * 10}px) translateY(${mousePosition.y * 10}px)`
          }}
        />
      </CardContent>
    </Card>
  )
}
