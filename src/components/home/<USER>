'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'

interface AnimatedButtonProps {
  children: ReactNode
  href?: string
  onClick?: () => void
  className?: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  icon?: ReactNode
  iconPosition?: 'left' | 'right'
  disabled?: boolean
  fullWidth?: boolean
  type?: 'button' | 'submit' | 'reset'
  ariaLabel?: string
  external?: boolean
}

export function AnimatedButton({
  children,
  href,
  onClick,
  className = '',
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  disabled = false,
  fullWidth = false,
  type = 'button',
  ariaLabel,
  external = false
}: AnimatedButtonProps) {
  // 基础样式
  const baseStyles = 'inline-flex items-center justify-center font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
  
  // 尺寸样式
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  // 变体样式
  const variantStyles = {
    primary: 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 disabled:bg-blue-300',
    secondary: 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 disabled:bg-blue-300',
    outline: 'border border-blue-500 text-blue-500 hover:bg-blue-50 active:bg-blue-100 disabled:border-blue-300 disabled:text-blue-300',
    ghost: 'text-blue-500 hover:bg-blue-50 active:bg-blue-100 disabled:text-blue-300'
  }
  
  // 宽度样式
  const widthStyle = fullWidth ? 'w-full' : ''
  
  // 组合所有样式
  const buttonStyles = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${widthStyle} ${className}`
  
  // 按钮动画变体
  const buttonVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 }
  }
  
  // 图标动画变体
  const iconVariants = {
    initial: { x: 0 },
    hover: iconPosition === 'right' ? { x: 5 } : { x: -5 }
  }
  
  // 渲染按钮内容
  const renderContent = () => (
    <>
      {icon && iconPosition === 'left' && (
        <motion.span 
          className="mr-2" 
          variants={iconVariants}
        >
          {icon}
        </motion.span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <motion.span 
          className="ml-2" 
          variants={iconVariants}
        >
          {icon}
        </motion.span>
      )}
    </>
  )
  
  // 如果有href，渲染为链接
  if (href) {
    const linkProps = external ? { target: '_blank', rel: 'noopener noreferrer' } : {}
    
    return (
      <motion.div
        initial="initial"
        whileHover={disabled ? {} : "hover"}
        whileTap={disabled ? {} : "tap"}
        variants={buttonVariants}
        className="inline-block"
      >
        <Link 
          href={href} 
          className={`${buttonStyles} ${disabled ? 'pointer-events-none opacity-60' : ''}`}
          aria-label={ariaLabel}
          {...linkProps}
        >
          {renderContent()}
        </Link>
      </motion.div>
    )
  }
  
  // 否则渲染为按钮
  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={buttonStyles}
      aria-label={ariaLabel}
      initial="initial"
      whileHover={disabled ? {} : "hover"}
      whileTap={disabled ? {} : "tap"}
      variants={buttonVariants}
    >
      {renderContent()}
    </motion.button>
  )
}