'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getFeaturedNews } from '@/data/unified-data-fetcher'
import { useHomeTranslations } from '@/hooks/use-translations'
import { useLanguage } from '@/contexts/simple-language-context'
import { ArrowRight, Eye } from 'lucide-react'

export function NewsSection() {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  const { t } = useHomeTranslations()
  const { language } = useLanguage()

  // 获取新闻数据
  const [featuredPosts, setFeaturedPosts] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadNews = async () => {
      try {
        const news = await getFeaturedNews(language)
        setFeaturedPosts(news.map(article => ({
          id: article.id,
          title: article.title,
          excerpt: article.excerpt,
          category: article.category,
          author: article.author,
          date: article.date,
          readTime: '5 min read',
          image: article.image || '/images/background/prod_bg.jpg',
          link: `/news/${article.slug}`
        })))
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to load featured news:', error)
        }
        setFeaturedPosts([])
      } finally {
        setIsLoading(false)
      }
    }

    loadNews()
  }, [language]) // 依赖语言变化重新加载

  // Intersection Observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.2 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }



  return (
    <section
      ref={sectionRef}
      className="relative py-20 lg:py-24 bg-gradient-to-br from-white via-blue-50/30 to-slate-50/50 overflow-hidden"
    >


      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>


          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text">
            {t("newsSection.title")}
          </h2>
          <p className="text-xl lg:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t("newsSection.description")}
          </p>


        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gradient-to-br from-gray-200 to-gray-300 h-[580px] mb-6"></div>
              </div>
            ))}
          </div>
        )}

        {/* News Grid */}
        {!isLoading && (
          <div className={`transition-all duration-1000 delay-300 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 max-w-6xl mx-auto">
              {featuredPosts.map((post, index) => (
                <div
                  key={post.id}
                  className="transform"
                  style={{
                    animationDelay: `${index * 200}ms`,
                    animation: 'fadeInUp 0.8s ease-out forwards'
                  }}
                >
                  <Card className="group transition-all duration-500 border border-gray-200/50 hover:border-Linnuo-orange/30 bg-white overflow-hidden h-full flex flex-col min-h-[400px] relative shadow-lg hover:shadow-Linnuo-orange/10"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 0.95) 100%)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  >
                    {/* Image */}
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={post.image || '/images/background/prod_bg.jpg'}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-700"
                      />
                      {/* 渐变遮罩 - 增强文字可读性
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent group-hover:from-black/50 transition-all duration-500"></div> */}

                      {/* 微妙的内边框效果 */}
                      <div className="absolute inset-0 border-b border-white/10"></div>

                      {/* 分类标签 - 重新设计 */}
                      <div className="absolute top-4 left-4 z-10">
                        <Badge
                          className="text-white border-0 px-3 py-1.5 text-xs font-semibold shadow-lg backdrop-blur-sm"
                          style={{
                            background: 'linear-gradient(135deg, rgba(255, 107, 53, 0.95) 0%, rgba(255, 138, 91, 0.95) 100%)',
                            boxShadow: '0 4px 12px rgba(255, 107, 53, 0.3)'
                          }}
                        >
                          {post.category}
                        </Badge>
                      </div>




                    </div>

                    {/* Content */}
                    <CardContent className="p-6 flex-1 flex flex-col relative justify-between">


                      {/* 标题和内容 */}
                      <div className="flex-1 relative z-10">
                        <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-Linnuo-orange transition-colors duration-300 line-clamp-2 leading-tight">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300 mb-4">
                          {post.content ? post.content.substring(0, 30) + '...' : post.excerpt + '...'}
                        </p>
                      </div>



                      {/* 底部信息和操作 */}
                      <div className="flex items-center justify-between relative z-10">
                        {/* 作者信息 */}
                        <div className="flex items-center gap-2">
                          <div className="w-7 h-7 bg-gradient-to-br from-Linnuo-orange to-orange-600 flex items-center justify-center text-white text-xs font-bold shadow-lg relative overflow-hidden">
                            <span className="relative z-10">{post.author.charAt(0)}</span>
                          </div>
                          <div>
                            <p className="text-xs font-semibold text-gray-900 group-hover:text-Linnuo-orange transition-colors duration-300">By {post.author}</p>
                            <p className="text-xs text-gray-500">{formatDate(post.date)}</p>
                          </div>
                        </div>

                        {/* 阅读更多按钮 */}
                        <Button
                          asChild
                          size="sm"
                          className="bg-gradient-to-r from-Linnuo-orange to-orange-600 hover:from-orange-600 hover:to-Linnuo-orange shadow-lg hover:shadow-xl transition-all duration-300 text-xs px-4 py-2 relative overflow-hidden"
                        >
                          <Link href={post.link} className="flex items-center gap-1 relative z-10">
                            <span className="font-medium">{t("newsSection.readMore")}</span>
                            <ArrowRight className="w-3 h-3" />
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>

            {/* Empty State */}
            {featuredPosts.length === 0 && (
              <div className="text-center py-20">
                <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 mx-auto mb-6 flex items-center justify-center">
                  <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
                <p className="text-xl text-gray-500">{t("newsSection.noNews")}</p>
              </div>
            )}
          </div>
        )}
        

      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </section>
  )
}
