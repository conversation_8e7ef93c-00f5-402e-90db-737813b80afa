'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useHomeTranslations } from '@/hooks/use-translations'
import { useLanguage } from '@/contexts/simple-language-context'

// 固定的产品分类配置
interface ProductCategory {
  id: string
  slug: string
  imageEn: string  // 英文图片
  imageZh: string  // 中文图片
  alt: string
}

const FIXED_CATEGORIES: ProductCategory[] = [
  {
    id: '1',
    slug: 'scalable-embedded-series',
    imageEn: '/images/products/scable-emablemed.jpg',
    imageZh: '/images/products/zh/scable-emablemed.jpg',
    alt: 'scalableEmbeddedSeries'
  },
  {
    id: '2',
    slug: 'mini-size-series',
    imageEn: '/images/products/mini-size.jpg',
    imageZh: '/images/products/zh/mini-size.jpg',
    alt: 'miniSizeSeries'
  },
  {
    id: '3',
    slug: 'single-board-computer',
    imageEn: '/images/products/singe-board.jpg',  // 保持原有英文图片名
    imageZh: '/images/products/zh/single-board.jpg', // 使用正确的中文图片名
    alt: 'singleBoardComputer'
  },
  {
    id: '4',
    slug: 'universal-embedded-series',
    imageEn: '/images/products/universe-embedded.jpg',
    imageZh: '/images/products/zh/universe-embedded.jpg',
    alt: 'universalEmbeddedSeries'
  },
  {
    id: '5',
    slug: 'all-in-one-ipc',
    imageEn: '/images/products/all-in-one.jpg',
    imageZh: '/images/products/zh/all-in-one.jpg',
    alt: 'allInOneIpc'
  }
]

// 获取当前语言对应的图片路径
function getImageForLanguage(category: ProductCategory, language: 'en' | 'zh'): string {
  return language === 'zh' ? category.imageZh : category.imageEn
}

export function FeaturedProducts() {
  const { t } = useHomeTranslations()
  const { language } = useLanguage()

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* 标题部分 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4" dangerouslySetInnerHTML={{ __html: t('featuredProducts.mainTitle') }}>
          </h2>
        </div>
        
        {/* 产品分类网格 - 上3下2布局 */}
        <div className="max-w-6xl mx-auto">
          {/* 上排3个分类 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {FIXED_CATEGORIES.slice(0, 3).map((category) => (
              <Link
                key={category.id}
                href={`/products?category=${category.slug}`}
                className="block overflow-hidden transition-transform duration-300 hover:scale-105"
              >
                <div className="relative w-full aspect-[474/312]">
                  <Image
                    src={getImageForLanguage(category, language)}
                    alt={category.alt}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 474px"
                    priority={true} // 预加载首屏图片
                  />
                  {/* 预加载另一种语言的图片以实现快速切换 */}
                  <link
                    rel="preload"
                    as="image"
                    href={getImageForLanguage(category, language === 'en' ? 'zh' : 'en')}
                  />
                </div>
              </Link>
            ))}
          </div>

          {/* 下排2个分类 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {FIXED_CATEGORIES.slice(3, 5).map((category) => (
              <Link
                key={category.id}
                href={`/products?category=${category.slug}`}
                className="block overflow-hidden transition-transform duration-300 hover:scale-105"
              >
                <div className="relative w-full aspect-[474/312]">
                  <Image
                    src={getImageForLanguage(category, language)}
                    alt={category.alt}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 474px"
                    priority={true} // 预加载首屏图片
                  />
                  {/* 预加载另一种语言的图片以实现快速切换 */}
                  <link
                    rel="preload"
                    as="image"
                    href={getImageForLanguage(category, language === 'en' ? 'zh' : 'en')}
                  />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
