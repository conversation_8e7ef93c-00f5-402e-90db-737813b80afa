'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { getHeroSlides, type HeroSlide } from '@/data/unified-data-fetcher'
import { useHeroTranslations } from '@/hooks/use-translations'
import { useLanguage } from '@/contexts/simple-language-context'

interface SimpleCarouselProps {
  height?: string
  autoPlayInterval?: number
  showControls?: boolean
  showIndicators?: boolean
  className?: string
}

export function SimpleCarousel({
  height = 'h-[500px] lg:h-[600px]',
  autoPlayInterval = 3000,
  showControls = true,
  showIndicators = true,
  className = ''
}: SimpleCarouselProps) {
  const [slides, setSlides] = useState<HeroSlide[]>([])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { t } = useHeroTranslations()
  const { language } = useLanguage()

  // 检查语言上下文是否已经水合完成
  const [isLanguageReady, setIsLanguageReady] = useState(false)
  
  useEffect(() => {
    // 等待语言上下文初始化完成
    const checkLanguageReady = () => {
      if (typeof window !== 'undefined') {
        // 检查localStorage中的语言设置是否与language状态一致
        try {
          const savedLanguage = localStorage.getItem('language')
          if (savedLanguage === language) {
            setIsLanguageReady(true)
            if (process.env.NODE_ENV === 'development') {
              console.log('🌐 SimpleCarousel: 语言上下文已就绪，当前语言:', language)
            }
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('SimpleCarousel: 检查语言状态失败:', error)
          }
          setIsLanguageReady(true) // 容错处理
        }
      } else {
        // 服务端渲染时直接设置为就绪
        setIsLanguageReady(true)
      }
    }

    checkLanguageReady()
  }, [language])

  // 加载幻灯片数据 - 只有在语言上下文就绪后才开始加载
  useEffect(() => {
    if (!isLanguageReady) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 SimpleCarousel: 等待语言上下文就绪...')
      }
      return
    }

    // 仅在开发环境下输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 ==> SimpleCarousel组件useEffect开始执行 <==', { language, isLanguageReady, timestamp: new Date().toISOString() })
    }
    // 仅在开发环境下输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('SimpleCarousel: useEffect开始执行，语言:', language, '语言就绪:', isLanguageReady)
    }
    
    const loadSlides = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 SimpleCarousel加载轮播图数据，语言:', language)
      }
      // 仅在开发环境下输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log('SimpleCarousel: 加载轮播图数据，语言:', language)
      }
      setIsLoading(true)
      setError(null)

      try {
        const allSlides = await getHeroSlides(language)
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 SimpleCarousel获取到轮播图:', allSlides.length, '个，语言:', language)
        }

        // 过滤出活跃的轮播图
        const activeSlides = allSlides.filter(slide => slide.active)
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 SimpleCarousel活跃轮播图:', activeSlides.length, '个')
        }

        if (activeSlides.length === 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log('⚠️ SimpleCarousel没有活跃轮播图，使用所有轮播图')
          }
          setSlides(allSlides)
        } else {
          setSlides(activeSlides)
        }

        // 显示轮播图详情
        if (process.env.NODE_ENV === 'development') {
          activeSlides.forEach((slide, index) => {
            console.log(`  ${index + 1}. ${slide.title} - ${slide.image}`)
          })
        }

      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ SimpleCarousel轮播图加载失败:', err)
        }
        setError(err instanceof Error ? err.message : 'Failed to load slides')
        setSlides([])
      } finally {
        setIsLoading(false)
      }
    }

    loadSlides()
  }, [language, isLanguageReady]) // 同时依赖语言和语言就绪状态

  // 自动播放功能
  useEffect(() => {
    if (!isAutoPlaying || slides.length === 0) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, slides.length, autoPlayInterval])

  // 切换到下一张
  const nextSlide = useCallback(() => {
    if (slides.length === 0) return
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [slides.length])

  // 切换到上一张
  const prevSlide = useCallback(() => {
    if (slides.length === 0) return
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }, [slides.length])

  // 切换到指定幻灯片
  const goToSlide = useCallback((index: number) => {
    if (index >= 0 && index < slides.length) {
      setCurrentSlide(index)
    }
  }, [slides.length])

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          prevSlide()
          setIsAutoPlaying(false)
          break
        case 'ArrowRight':
          event.preventDefault()
          nextSlide()
          setIsAutoPlaying(false)
          break
        case ' ':
          event.preventDefault()
          setIsAutoPlaying(!isAutoPlaying)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [nextSlide, prevSlide])

  // 触摸手势
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      nextSlide()
      setIsAutoPlaying(false)
    } else if (isRightSwipe) {
      prevSlide()
      setIsAutoPlaying(false)
    }
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className={`relative ${height} bg-gray-100 ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center space-y-4">
            <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="text-gray-600">{t("carousel.loading")}</p>
          </div>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error && slides.length === 0) {
    return (
      <div className={`relative ${height} bg-gray-100 ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center space-y-4">
            <p className="text-red-600">{t("carousel.error")}: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white"
            >
              {t("carousel.retry")}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className={`relative ${height} overflow-hidden ${className}`}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* 图片容器 - 添加淡入淡出动画 */}
      <div className="relative w-full h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-all duration-700 ease-in-out ${
              index === currentSlide
                ? 'opacity-100 scale-100'
                : 'opacity-0 scale-110'
            }`}
            style={{
              transform: index === currentSlide
                ? 'scale(1)'
                : 'scale(1.1)',
              transition: 'all 0.7s ease-in-out'
            }}
          >
            {slide.ctaLink && slide.ctaLink !== '#' ? (
              <Link href={slide.ctaLink} className="block w-full h-full">
                <img
                  src={slide.image}
                  alt={slide.title}
                  className="w-full h-full object-cover"
                  style={{ objectPosition: 'center' }}
                />
              </Link>
            ) : (
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-cover"
                style={{ objectPosition: 'center' }}
              />
            )}
          </div>
        ))}
      </div>

      {/* 渐变遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none" />

      {/* 导航控制按钮 */}
      {showControls && slides.length > 1 && (
        <>
          {/* 左箭头 */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white shadow-lg transition-all duration-200 group z-10 rounded-full"
            aria-label={t("carousel.previous") || "Previous image"}
          >
            <ChevronLeft className="w-6 h-6 text-gray-700 group-hover:text-orange-500 transition-colors" />
          </button>

          {/* 右箭头 */}
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white shadow-lg transition-all duration-200 group z-10 rounded-full"
            aria-label={t("carousel.next") || "Next image"}
          >
            <ChevronRight className="w-6 h-6 text-gray-700 group-hover:text-orange-500 transition-colors" />
          </button>
        </>
      )}

      {/* 轮播图指示器 */}
      {showIndicators && slides.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`h-3 rounded-full transition-all duration-300 ${
                index === currentSlide 
                  ? 'bg-white w-8' 
                  : 'bg-white/60 hover:bg-white/80 w-3'
              }`}
              aria-label={`${t("carousel.goToSlide") || "Go to slide"} ${index + 1}`}
            />
          ))}
        </div>
      )}

    </div>
  )
}
