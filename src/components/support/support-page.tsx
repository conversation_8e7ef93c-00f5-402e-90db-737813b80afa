"use client"

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

import { useSampleApplicationTranslations } from '@/hooks/use-translations'
import { submitSampleRequest, type SampleRequestData } from '@/lib/api'

import { useLanguage } from '@/contexts/simple-language-context'
import { usePageMetadata } from '@/hooks/use-page-metadata'

export function SupportPage() {
  const { t } = useSampleApplicationTranslations()
  const { language } = useLanguage()

  // 设置页面元数据
  usePageMetadata()

  const [formData, setFormData] = useState({
    name: '',
    company: '',
    phone: '',
    email: '',
    sampleName: '',
    quantity: '',
    purpose: '',
    requiredDate: '',
    requirements: '',
    address: '',
    urgency: 'normal'
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // 准备提交数据
      const requestData: SampleRequestData = {
        name: formData.name,
        company: formData.company,
        email: formData.email,
        phone: formData.phone,
        sampleName: formData.sampleName,
        quantity: parseInt(formData.quantity) || 1,
        purpose: formData.purpose,
        requirements: formData.requirements,
        requiredDate: formData.requiredDate,
        address: formData.address,
        urgency: formData.urgency as 'normal' | 'urgent' | 'very_urgent'
      }

      console.log('📋 准备提交的表单数据:', requestData)

      // 提交到 Strapi
      const result = await submitSampleRequest(requestData)

      if (result.success) {
        console.log('✅ 样品申请提交成功')
        setSubmitStatus('success')
        setErrorMessage('')
        // 重置表单
        setFormData({
          name: '',
          company: '',
          phone: '',
          email: '',
          sampleName: '',
          quantity: '',
          purpose: '',
          requiredDate: '',
          requirements: '',
          address: '',
          urgency: 'normal'
        })
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ 样品申请提交失败:', result.error)
        }
        setSubmitStatus('error')
        setErrorMessage(result.error?.message || '提交失败，请稍后重试')
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ 样品申请提交异常:', error)
      }
      setSubmitStatus('error')
      setErrorMessage(t("networkError"))
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitStatus === 'success') {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          {/* <div className="w-16 h-16 bg-green-100 flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">✅</span>
          </div> */}
          <h1 className="text-3xl font-bold text-green-600 mb-4">
            {t("successTitle")}
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            {t("successDescription")}
          </p>
          <div className="space-y-3">
            <Button
              onClick={() => setSubmitStatus('idle')}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {t("submitAnother")}
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/products'}
              className="w-full"
            >
              {t("browseProducts")}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (submitStatus === 'error') {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          {/* <div className="w-16 h-16 bg-red-100 flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">❌</span>
          </div> */}
          <h1 className="text-3xl font-bold text-red-600 mb-4">
            {t("errorTitle")}
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            {errorMessage || t("errorDescription")}
          </p>
          <div className="space-y-3">
            <Button
              onClick={() => setSubmitStatus('idle')}
              className="w-full bg-slate-700 hover:bg-slate-800"
            >
              {t("retrySubmit")}
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="w-full"
            >
              {t("refreshPage")}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section
        className="relative py-16 overflow-hidden"
        style={{
          backgroundImage: 'url(/images/background/about-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          height: '400px'
        }}
      >
        {/* <div className="absolute inset-0 bg-gradient-to-r from-black/40 to-black/60"></div> */}
        <div className="relative container-custom h-full flex items-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white w-full"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">{t("title")}</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              {t("description") || "申请免费样品进行项目评估"}
            </p>
          </motion.div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Name and Company Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="text-gray-700 font-medium">
                  {t("form.name")}*
                </Label>
                <Input
                  id="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t("form.placeholders.name")}
                  className="mt-2 h-12 bg-gray-100 border-0 focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <Label htmlFor="company" className="text-gray-700 font-medium">
                  {t("form.company")}*
                </Label>
                <Input
                  id="company"
                  type="text"
                  required
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  placeholder={t("form.placeholders.company")}
                  className="mt-2 h-12 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Email and Phone Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email" className="text-gray-700 font-medium">
                  {t("form.email")}*
                </Label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder={t("form.placeholders.email")}
                  className="mt-2 h-12 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <Label htmlFor="phone" className="text-gray-700 font-medium">
                  {t("form.phoneNumber")}
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t("form.placeholders.phoneNumber")}
                  className="mt-2 h-12 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Sample Name and Quantity Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="sampleName" className="text-gray-700 font-medium">
                  {t("form.sampleName")}*
                </Label>
                <Input
                  id="sampleName"
                  type="text"
                  required
                  value={formData.sampleName}
                  onChange={(e) => handleInputChange('sampleName', e.target.value)}
                  placeholder={t("form.placeholders.sampleName")}
                  className="mt-2 h-12 bg-gray-100 border-0 focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <Label htmlFor="quantity" className="text-gray-700 font-medium">
                  {t("form.quantity")}*
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  required
                  min="1"
                  value={formData.quantity}
                  onChange={(e) => handleInputChange('quantity', e.target.value)}
                  placeholder={t("form.placeholders.quantity")}
                  className="mt-2 h-12 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Required Date and Urgency Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="requiredDate" className="text-gray-700 font-medium">
                  {t("form.timeOfRequest")}*
                </Label>
                <Input
                  id="requiredDate"
                  type="date"
                  required
                  value={formData.requiredDate}
                  onChange={(e) => handleInputChange('requiredDate', e.target.value)}
                  className="mt-2 h-12 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <Label htmlFor="urgency" className="text-gray-700 font-medium">
                  {t("form.urgency")}
                </Label>
                <select
                  value={formData.urgency}
                  onChange={(e) => handleInputChange('urgency', e.target.value)}
                  className="mt-2 h-12 w-full bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500 px-3"
                >
                  <option value="normal">{t("form.urgencyOptions.normal")}</option>
                  <option value="urgent">{t("form.urgencyOptions.urgent")}</option>
                  <option value="very_urgent">{t("form.urgencyOptions.very_urgent")}</option>
                </select>
              </div>
            </div>

            {/* Purpose */}
            <div>
              <Label htmlFor="purpose" className="text-gray-700 font-medium">
                {t("form.purpose")}*
              </Label>
              <Textarea
                id="purpose"
                required
                value={formData.purpose}
                onChange={(e) => handleInputChange('purpose', e.target.value)}
                placeholder={t("form.placeholders.purpose")}
                rows={3}
                className="mt-2 bg-gray-100 border-0 focus:bg-white focus:ring-2 focus:ring-blue-500 resize-none"
              />
            </div>

            {/* Requirements */}
            <div>
              <Label htmlFor="requirements" className="text-gray-700 font-medium">
                {t("form.requirements")}*
              </Label>
              <Textarea
                id="requirements"
                required
                value={formData.requirements}
                onChange={(e) => handleInputChange('requirements', e.target.value)}
                placeholder={t("form.placeholders.requirements")}
                rows={4}
                className="mt-2 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500 resize-none"
              />
            </div>

            {/* Shipping Address */}
            <div>
              <Label htmlFor="address" className="text-gray-700 font-medium">
                {t("form.address")}*
              </Label>
              <Textarea
                id="address"
                required
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder={t("form.placeholders.address")}
                rows={3}
                className="mt-2 bg-gray-100 border-0 rounded-none focus:bg-white focus:ring-2 focus:ring-blue-500 resize-none"
              />
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full h-12 bg-slate-700 hover:bg-slate-800 text-white font-medium"
              >
                {isSubmitting ? t("form.submitting") : t("form.submit")}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
