import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveTextProps {
  children: ReactNode
  className?: string
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div'
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl'
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'
  color?: 'primary' | 'secondary' | 'muted' | 'accent' | 'destructive'
  align?: 'left' | 'center' | 'right' | 'justify'
  responsive?: {
    sm?: string
    md?: string
    lg?: string
    xl?: string
  }
}

const sizeClasses = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
  '2xl': 'text-2xl',
  '3xl': 'text-3xl',
  '4xl': 'text-4xl',
  '5xl': 'text-5xl',
  '6xl': 'text-6xl'
}

const weightClasses = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
  extrabold: 'font-extrabold'
}

const colorClasses = {
  primary: 'text-foreground',
  secondary: 'text-muted-foreground',
  muted: 'text-muted-foreground',
  accent: 'text-accent-foreground',
  destructive: 'text-destructive'
}

const alignClasses = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify'
}

export function ResponsiveText({
  children,
  className,
  as: Component = 'p',
  size = 'base',
  weight = 'normal',
  color = 'primary',
  align = 'left',
  responsive,
  ...props
}: ResponsiveTextProps) {
  const responsiveClasses = []
  
  if (responsive?.sm) responsiveClasses.push(`sm:${responsive.sm}`)
  if (responsive?.md) responsiveClasses.push(`md:${responsive.md}`)
  if (responsive?.lg) responsiveClasses.push(`lg:${responsive.lg}`)
  if (responsive?.xl) responsiveClasses.push(`xl:${responsive.xl}`)

  return (
    <Component
      className={cn(
        sizeClasses[size],
        weightClasses[weight],
        colorClasses[color],
        alignClasses[align],
        ...responsiveClasses,
        className
      )}
      {...props}
    >
      {children}
    </Component>
  )
}

interface ResponsiveHeadingProps extends Omit<ResponsiveTextProps, 'as'> {
  level: 1 | 2 | 3 | 4 | 5 | 6
}

export function ResponsiveHeading({
  level,
  size,
  weight = 'bold',
  ...props
}: ResponsiveHeadingProps) {
  const Component = `h${level}` as const
  
  // Default sizes for headings if not specified
  const defaultSizes = {
    1: '4xl',
    2: '3xl',
    3: '2xl',
    4: 'xl',
    5: 'lg',
    6: 'base'
  } as const

  return (
    <ResponsiveText
      as={Component}
      size={size || defaultSizes[level]}
      weight={weight}
      {...props}
    />
  )
}

interface TruncatedTextProps {
  children: string
  maxLength: number
  className?: string
  showTooltip?: boolean
}

export function TruncatedText({
  children,
  maxLength,
  className,
  showTooltip = true
}: TruncatedTextProps) {
  const isTruncated = children.length > maxLength
  const displayText = isTruncated ? `${children.slice(0, maxLength)}...` : children

  if (showTooltip && isTruncated) {
    return (
      <span
        className={cn('cursor-help', className)}
        title={children}
      >
        {displayText}
      </span>
    )
  }

  return (
    <span className={className}>
      {displayText}
    </span>
  )
}

interface ResponsiveListProps {
  children: ReactNode
  className?: string
  variant?: 'unordered' | 'ordered' | 'none'
  spacing?: 'tight' | 'normal' | 'loose'
  columns?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
  }
}

const spacingClasses = {
  tight: 'space-y-1',
  normal: 'space-y-2',
  loose: 'space-y-4'
}

export function ResponsiveList({
  children,
  className,
  variant = 'unordered',
  spacing = 'normal',
  columns
}: ResponsiveListProps) {
  const Component = variant === 'ordered' ? 'ol' : variant === 'unordered' ? 'ul' : 'div'
  
  const columnClasses = []
  if (columns?.default) columnClasses.push(`columns-${columns.default}`)
  if (columns?.sm) columnClasses.push(`sm:columns-${columns.sm}`)
  if (columns?.md) columnClasses.push(`md:columns-${columns.md}`)
  if (columns?.lg) columnClasses.push(`lg:columns-${columns.lg}`)

  return (
    <Component
      className={cn(
        spacingClasses[spacing],
        ...columnClasses,
        variant === 'unordered' && 'list-disc list-inside',
        variant === 'ordered' && 'list-decimal list-inside',
        className
      )}
    >
      {children}
    </Component>
  )
}
