'use client'

import { useState, useEffect } from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface OptimizedLoadingProps {
  isLoading: boolean
  delay?: number // 延迟显示加载状态的时间（毫秒）
  minDuration?: number // 最小显示时间（毫秒）
  children?: React.ReactNode
  fallback?: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'skeleton' | 'pulse'
}

/**
 * 优化的加载组件
 * 特点：
 * 1. 防抖动：短时间的加载不会显示加载状态
 * 2. 最小显示时间：避免闪烁
 * 3. 多种加载样式
 */
export function OptimizedLoading({
  isLoading,
  delay = 200, // 200ms后才显示加载状态
  minDuration = 500, // 最少显示500ms
  children,
  fallback,
  className,
  size = 'md',
  variant = 'spinner'
}: OptimizedLoadingProps) {
  const [showLoading, setShowLoading] = useState(false)
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null)

  useEffect(() => {
    let delayTimer: NodeJS.Timeout
    let minDurationTimer: NodeJS.Timeout

    if (isLoading) {
      // 延迟显示加载状态
      delayTimer = setTimeout(() => {
        setShowLoading(true)
        setLoadingStartTime(Date.now())
      }, delay)
    } else {
      // 清除延迟定时器
      if (delayTimer) {
        clearTimeout(delayTimer)
      }

      // 如果正在显示加载状态，确保最小显示时间
      if (showLoading && loadingStartTime) {
        const elapsed = Date.now() - loadingStartTime
        const remaining = minDuration - elapsed

        if (remaining > 0) {
          minDurationTimer = setTimeout(() => {
            setShowLoading(false)
            setLoadingStartTime(null)
          }, remaining)
        } else {
          setShowLoading(false)
          setLoadingStartTime(null)
        }
      } else {
        setShowLoading(false)
        setLoadingStartTime(null)
      }
    }

    return () => {
      if (delayTimer) clearTimeout(delayTimer)
      if (minDurationTimer) clearTimeout(minDurationTimer)
    }
  }, [isLoading, delay, minDuration, showLoading, loadingStartTime])

  // 如果不需要显示加载状态，直接返回内容
  if (!showLoading) {
    return <>{children}</>
  }

  // 渲染加载状态
  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      {variant === 'spinner' && <SpinnerLoading size={size} />}
      {variant === 'skeleton' && <SkeletonLoading />}
      {variant === 'pulse' && <PulseLoading />}
    </div>
  )
}

// 旋转加载器
function SpinnerLoading({ size }: { size: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div className="flex items-center space-x-2">
      <Loader2 className={cn('animate-spin', sizeClasses[size])} />
      <span className="text-sm text-muted-foreground">加载中...</span>
    </div>
  )
}

// 骨架屏加载
function SkeletonLoading() {
  return (
    <div className="space-y-3">
      <div className="h-4 bg-gray-200 animate-pulse"></div>
      <div className="h-4 bg-gray-200 animate-pulse w-3/4"></div>
      <div className="h-4 bg-gray-200 animate-pulse w-1/2"></div>
    </div>
  )
}

// 脉冲加载
function PulseLoading() {
  return (
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-blue-500 animate-pulse"></div>
      <div className="w-2 h-2 bg-blue-500 animate-pulse" style={{ animationDelay: '0.1s' }}></div>
      <div className="w-2 h-2 bg-blue-500 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
    </div>
  )
}

/**
 * 页面级别的优化加载组件
 */
export function PageOptimizedLoading({
  isLoading,
  children,
  loadingText = '页面加载中...'
}: {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
}) {
  return (
    <OptimizedLoading
      isLoading={isLoading}
      delay={100} // 页面级加载延迟更短
      minDuration={300} // 最小显示时间更短
      className="min-h-[200px]"
      fallback={
        <div className="flex flex-col items-center justify-center min-h-[200px] space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-sm text-muted-foreground">{loadingText}</p>
        </div>
      }
    >
      {children}
    </OptimizedLoading>
  )
}

/**
 * 组件级别的优化加载组件
 */
export function ComponentOptimizedLoading({
  isLoading,
  children,
  skeleton
}: {
  isLoading: boolean
  children: React.ReactNode
  skeleton?: React.ReactNode
}) {
  return (
    <OptimizedLoading
      isLoading={isLoading}
      delay={300} // 组件级加载延迟更长
      minDuration={200} // 最小显示时间更短
      variant="skeleton"
      fallback={skeleton}
    >
      {children}
    </OptimizedLoading>
  )
}

/**
 * 按钮级别的优化加载组件
 */
export function ButtonOptimizedLoading({
  isLoading,
  children,
  loadingText = '处理中...'
}: {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
}) {
  return (
    <OptimizedLoading
      isLoading={isLoading}
      delay={0} // 按钮加载立即显示
      minDuration={500} // 确保用户能看到反馈
      size="sm"
      fallback={
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>{loadingText}</span>
        </div>
      }
    >
      {children}
    </OptimizedLoading>
  )
}
