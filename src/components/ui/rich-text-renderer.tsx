"use client"

import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import Image from 'next/image'

interface RichTextRendererProps {
  content: string | any
  className?: string
}

// 自定义组件用于渲染 Markdown 中的图片
const CustomImage = ({ src, alt, ...props }: any) => {
  // 处理相对路径的图片
  const imageSrc = src?.startsWith('http') ? src : `${process.env.NEXT_PUBLIC_STRAPI_URL}${src}`
  
  return (
    <Image
      src={imageSrc}
      alt={alt || ''}
      width={800}
      height={400}
      className="w-full h-auto object-cover my-6"
      unoptimized={!src?.startsWith('http')}
    />
  )
}

// 自定义组件映射
const components = {
  img: ({ src, alt, ...props }: any) => {
    const imageSrc = src?.startsWith('http') ? src : `${process.env.NEXT_PUBLIC_STRAPI_URL}${src}`
    return (
      <div className="my-6 overflow-hidden">
        <Image
          src={imageSrc}
          alt={alt || ''}
          width={800}
          height={400}
          className="w-full h-auto object-cover"
          unoptimized={!src?.startsWith('http')}
        />
      </div>
    )
  },
  h1: ({ children, ...props }: any) => (
    <h1 className="text-3xl font-bold mt-8 mb-4 text-gray-900" {...props}>
      {children}
    </h1>
  ),
  h2: ({ children, ...props }: any) => (
    <h2 className="text-2xl font-semibold mt-6 mb-3 text-gray-900" {...props}>
      {children}
    </h2>
  ),
  h3: ({ children, ...props }: any) => (
    <h3 className="text-xl font-semibold mt-4 mb-2 text-gray-900" {...props}>
      {children}
    </h3>
  ),
  p: ({ children, ...props }: any) => {
    // 检查段落是否只包含图片
    const hasOnlyImage = React.Children.count(children) === 1 && 
      React.Children.toArray(children).some((child: any) => 
        child?.type === 'img' || 
        (child?.props && child.props.src)
      )
    
    // 如果段落只包含图片，直接返回子元素，避免嵌套
    if (hasOnlyImage) {
      return <>{children}</>
    }
    
    return (
      <p className="mb-4 text-gray-700 leading-relaxed" {...props}>
        {children}
      </p>
    )
  },
  ul: ({ children, ...props }: any) => (
    <ul className="mb-4 ml-6 list-disc space-y-2" {...props}>
      {children}
    </ul>
  ),
  ol: ({ children, ...props }: any) => (
    <ol className="mb-4 ml-6 list-decimal space-y-2" {...props}>
      {children}
    </ol>
  ),
  li: ({ children, ...props }: any) => (
    <li className="text-gray-700" {...props}>
      {children}
    </li>
  ),
  blockquote: ({ children, ...props }: any) => (
    <blockquote className="border-l-4 border-Linnuo-orange pl-4 my-4 italic text-gray-600" {...props}>
      {children}
    </blockquote>
  ),
  code: ({ children, ...props }: any) => (
    <code className="bg-gray-100 px-2 py-1 text-sm font-mono" {...props}>
      {children}
    </code>
  ),
  pre: ({ children, ...props }: any) => (
    <pre className="bg-gray-100 p-4 overflow-x-auto my-4" {...props}>
      {children}
    </pre>
  ),
  a: ({ children, href, ...props }: any) => (
    <a 
      href={href} 
      className="text-Linnuo-orange hover:text-orange-600 underline" 
      target="_blank" 
      rel="noopener noreferrer"
      {...props}
    >
      {children}
    </a>
  ),
  table: ({ children, ...props }: any) => (
    <div className="overflow-x-auto my-4">
      <table className="min-w-full border-collapse border border-gray-300" {...props}>
        {children}
      </table>
    </div>
  ),
  th: ({ children, ...props }: any) => (
    <th className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left" {...props}>
      {children}
    </th>
  ),
  td: ({ children, ...props }: any) => (
    <td className="border border-gray-300 px-4 py-2" {...props}>
      {children}
    </td>
  ),
}

export function RichTextRenderer({ content, className = '' }: RichTextRendererProps) {
  // 处理不同类型的内容
  const renderContent = () => {
    if (!content) {
      return <p className="text-gray-500 italic">暂无内容</p>
    }

    // 如果是字符串，直接作为 Markdown 渲染
    if (typeof content === 'string') {
      // 检查是否是 HTML 内容
      if (content.includes('<') && content.includes('>')) {
        return (
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content }} 
          />
        )
      }
      
      // 作为 Markdown 渲染
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={components}
        >
          {content}
        </ReactMarkdown>
      )
    }

    // 如果是 Strapi 的结构化内容（JSON 格式）
    if (typeof content === 'object' && content !== null) {
      try {
        // 尝试将对象转换为可读的文本
        const textContent = JSON.stringify(content, null, 2)
        return (
          <pre className="bg-gray-100 p-4 overflow-x-auto whitespace-pre-wrap">
            {textContent}
          </pre>
        )
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('富文本内容解析错误:', error)
        }
        return <p className="text-red-500">内容解析错误</p>
      }
    }

    return <p className="text-gray-500 italic">不支持的内容格式</p>
  }

  return (
    <div className={`rich-text-content ${className}`}>
      {renderContent()}
    </div>
  )
}

export default RichTextRenderer
