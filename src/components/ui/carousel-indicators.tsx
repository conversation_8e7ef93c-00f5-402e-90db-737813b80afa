import { cn } from '@/lib/utils'

interface CarouselIndicatorsProps {
  total: number
  current: number
  onSelect: (index: number) => void
  className?: string
  variant?: 'dots' | 'lines' | 'thumbnails'
  size?: 'sm' | 'md' | 'lg'
  showProgress?: boolean
  autoPlayDuration?: number
}

export function CarouselIndicators({
  total,
  current,
  onSelect,
  className,
  variant = 'dots',
  size = 'md',
  showProgress = false,
  autoPlayDuration = 5000
}: CarouselIndicatorsProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  const activeSizeClasses = {
    sm: 'w-6',
    md: 'w-8',
    lg: 'w-10'
  }

  if (variant === 'dots') {
    return (
      <div className={cn('flex space-x-2', className)}>
        {Array.from({ length: total }, (_, index) => (
          <button
            key={index}
            onClick={() => onSelect(index)}
            className={cn(
              'transition-all duration-300 relative overflow-hidden',
              sizeClasses[size],
              index === current 
                ? cn('bg-orange-500', activeSizeClasses[size])
                : 'bg-gray-300 hover:bg-gray-400'
            )}
            aria-label={`Go to slide ${index + 1}`}
          >
            {showProgress && index === current && (
              <div 
                className="absolute inset-0 bg-orange-600 origin-left"
                style={{
                  animation: `progress ${autoPlayDuration}ms linear infinite`
                }}
              />
            )}
          </button>
        ))}
      </div>
    )
  }

  if (variant === 'lines') {
    return (
      <div className={cn('flex space-x-1', className)}>
        {Array.from({ length: total }, (_, index) => (
          <button
            key={index}
            onClick={() => onSelect(index)}
            className={cn(
              'h-1 transition-all duration-300 relative overflow-hidden',
              index === current 
                ? 'bg-orange-500 w-8'
                : 'bg-gray-300 hover:bg-gray-400 w-4'
            )}
            aria-label={`Go to slide ${index + 1}`}
          >
            {showProgress && index === current && (
              <div 
                className="absolute inset-0 bg-orange-600 origin-left"
                style={{
                  animation: `progress ${autoPlayDuration}ms linear infinite`
                }}
              />
            )}
          </button>
        ))}
      </div>
    )
  }

  return null
}

// 轮播图控制按钮组件
interface CarouselControlsProps {
  onPrevious: () => void
  onNext: () => void
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'minimal' | 'floating'
}

export function CarouselControls({
  onPrevious,
  onNext,
  className,
  size = 'md',
  variant = 'default'
}: CarouselControlsProps) {
  const sizeClasses = {
    sm: 'p-1 w-8 h-8',
    md: 'p-2 w-10 h-10',
    lg: 'p-3 w-12 h-12'
  }

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const variantClasses = {
    default: 'bg-white shadow-lg hover:shadow-xl',
    minimal: 'bg-white/80 hover:bg-white',
    floating: 'bg-black/20 hover:bg-black/40 text-white'
  }

  return (
    <div className={cn('flex items-center space-x-4', className)}>
      <button
        onClick={onPrevious}
        className={cn(
          'transition-all duration-200 group',
          sizeClasses[size],
          variantClasses[variant]
        )}
        aria-label="Previous slide"
      >
        <svg
          className={cn(
            'text-gray-600 group-hover:text-orange-500 transition-colors',
            iconSizeClasses[size],
            variant === 'floating' && 'text-white group-hover:text-orange-300'
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button
        onClick={onNext}
        className={cn(
          'transition-all duration-200 group',
          sizeClasses[size],
          variantClasses[variant]
        )}
        aria-label="Next slide"
      >
        <svg
          className={cn(
            'text-gray-600 group-hover:text-orange-500 transition-colors',
            iconSizeClasses[size],
            variant === 'floating' && 'text-white group-hover:text-orange-300'
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  )
}

// 自动播放指示器组件
interface AutoPlayIndicatorProps {
  isPlaying: boolean
  onToggle: () => void
  className?: string
}

export function AutoPlayIndicator({
  isPlaying,
  onToggle,
  className
}: AutoPlayIndicatorProps) {
  return (
    <button
      onClick={onToggle}
      className={cn(
        'flex items-center space-x-2 text-sm text-gray-500 hover:text-gray-700 transition-colors',
        className
      )}
      aria-label={isPlaying ? 'Pause autoplay' : 'Resume autoplay'}
    >
      <div className={cn(
        'w-2 h-2 transition-colors',
        isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
      )} />
      <span>{isPlaying ? 'Auto-playing' : 'Paused'}</span>
    </button>
  )
}

// 进度条组件
interface CarouselProgressProps {
  current: number
  total: number
  duration: number
  isPlaying: boolean
  className?: string
}

export function CarouselProgress({
  current,
  total,
  duration,
  isPlaying,
  className
}: CarouselProgressProps) {
  return (
    <div className={cn('w-full bg-gray-200 h-1', className)}>
      <div 
        className="bg-orange-500 h-1 transition-all duration-300"
        style={{
          width: `${((current + 1) / total) * 100}%`,
          animation: isPlaying ? `progress ${duration}ms linear infinite` : 'none'
        }}
      />
    </div>
  )
}

// 添加CSS动画到全局样式
export const carouselStyles = `
  @keyframes progress {
    from {
      transform: scaleX(0);
    }
    to {
      transform: scaleX(1);
    }
  }
`
