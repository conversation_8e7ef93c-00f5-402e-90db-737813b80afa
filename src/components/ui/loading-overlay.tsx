"use client"

import React from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  className?: string
}

export function LoadingOverlay({
  isVisible,
  message = "Loading...",
  className
}: LoadingOverlayProps) {
  // 优化：使用 CSS 动画而不是 JavaScript 来控制显示/隐藏
  return (
    <div
      className={cn(
        "fixed inset-0 z-[9999] flex items-center justify-center bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm transition-all duration-200",
        isVisible ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none",
        className
      )}
      style={{
        transform: isVisible ? 'scale(1)' : 'scale(0.95)',
      }}
    >
      <div className="flex flex-col items-center space-y-3 p-6 bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 transform transition-transform duration-200">
        <Loader2 className="h-6 w-6 animate-spin text-Linnuo-orange" />
        <p className="text-xs text-gray-600 dark:text-gray-300 font-medium">
          {message}
        </p>
      </div>
    </div>
  )
}
