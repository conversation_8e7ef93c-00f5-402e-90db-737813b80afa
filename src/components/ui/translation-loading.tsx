'use client'

import { useLanguage } from '@/contexts/simple-language-context'
import { Loader2, Languages } from 'lucide-react'
import { useEffect, useState } from 'react'

export function TranslationLoading() {
  const { isLoading, language } = useLanguage()
  const [showLoading, setShowLoading] = useState(false)

  // 使用延迟显示，避免快速切换时的闪烁
  useEffect(() => {
    let timer: NodeJS.Timeout

    if (isLoading) {
      timer = setTimeout(() => {
        setShowLoading(true)
      }, 100) // 100ms 延迟
    } else {
      setShowLoading(false)
    }

    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isLoading])

  if (!showLoading) return null

  return (
    <div className="fixed inset-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md z-50 flex items-center justify-center transition-all duration-300">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex items-center space-x-4 min-w-[240px]">
        <div className="relative">
          <Languages className="h-6 w-6 text-Linnuo-orange" />
          <Loader2 className="h-4 w-4 animate-spin text-Linnuo-orange absolute -top-1 -right-1" />
        </div>
        <div className="flex flex-col">
          <span className="text-base font-medium text-gray-700 dark:text-gray-300">
            {language === 'zh' ? '切换语言中...' : 'Switching language...'}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {language === 'zh' ? '正在调整页面布局，请稍候' : 'Adjusting page layout, please wait'}
          </span>
        </div>
      </div>
    </div>
  )
}
