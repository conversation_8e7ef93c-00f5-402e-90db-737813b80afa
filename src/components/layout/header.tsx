"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import <PERSON><PERSON><PERSON> from '@/components/logo/AnimatedLogo';


import { Menu, X, ChevronDown, Search, Globe } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useSearch } from "@/contexts/search-context"
import { useNavigationTranslations, useProductsTranslations, useCommonTranslations } from "@/hooks/use-translations"
import { useLanguage } from "@/contexts/simple-language-context"
import { ButtonOptimizedLoading } from "@/components/ui/optimized-loading"
import { getProductCategories } from "@/lib/api/product-categories"
import type { ProductCategory } from "@/lib/api/product-categories"
import { useQuery } from "@tanstack/react-query"


// 产品分类将从API动态获取

export function Header() {
  const router = useRouter()
  const [isOpen, setIsOpen] = React.useState(false)
  const [activeDropdown, setActiveDropdown] = React.useState<string | null>(null)
  const [mobileExpandedItems, setMobileExpandedItems] = React.useState<string[]>([])
  const [hoverTimeout, setHoverTimeout] = React.useState<NodeJS.Timeout | null>(null)
  const [pathname, setPathname] = React.useState("")

  const { openSearch } = useSearch()
  const { language, setLanguage: setLanguageContext } = useLanguage()  // 重命名避免冲突
  const [isLanguageSwitching, setIsLanguageSwitching] = React.useState(false)
  
  // 使用 React Query 获取产品分类数据
  const { data: productCategories = [], isLoading: categoriesLoading } = useQuery({
    queryKey: ['product-categories', language],
    queryFn: () => getProductCategories(language),
    staleTime: 15 * 60 * 1000, // 15分钟
    enabled: !!language, // 只有当language存在时才执行查询
  })
  const { t: navT } = useNavigationTranslations()
  const { t: productsT } = useProductsTranslations()
  const { t: commonT } = useCommonTranslations()

  // 产品分类数据变化监听
  React.useEffect(() => {
    // 仅在开发环境下输出日志
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Header: 产品分类数据更新，语言:', language, '分类数量:', productCategories.length)
    }
  }, [language, productCategories])

  // Force recompilation trigger

  // 使用 useMemo 稳定导航配置，避免语言切换时重复渲染
  const navigation = React.useMemo(() => [
    {
      id: "home",
      name: navT("home"),
      href: "/",
    },
    {
      id: "products",
      name: navT("products"),
      href: "/products",
      children: productCategories.map(category => ({
        name: category.name,
        href: `/products?category=${category.slug}`
      })),
    },
    {
      id: "salesAndSupport",
      name: navT("salesAndSupport"),
      href: "/support",
      children: [
        { name: navT("sampleApplication"), href: "/support" },
        { name: navT("afterSalesService"), href: "/after-sales" },
        { name: navT("downloads"), href: "/downloads" },
        { name: navT("customizedService"), href: "/sales-and-support/customized" },
      ],
    },
    {
      id: "application",
      name: navT("application"),
      href: "/application",
    },
    {
      id: "about",
      name: navT("about"),
      href: "/about",
      children: [
        { name: navT("aboutUs"), href: "/about" },
        { name: navT("news"), href: "/news" },
        { name: navT("contact"), href: "/contact" },
      ],
    },
  ], [navT, productsT, language, productCategories])

  // 移除语言切换时的 useEffect，改为在语言切换函数中处理

  // 安全地获取pathname，避免SSR错误
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      setPathname(window.location.pathname)
    }
  }, [])

  // 优化：使用 useCallback 稳定函数引用，减少子组件重新渲染
  const toggleMobileMenu = React.useCallback(() => {
    setIsOpen(prev => !prev)
    setActiveDropdown(null)
    setMobileExpandedItems([])
  }, [])
  
  // 处理移动菜单项的展开/折叠
  const toggleMobileItem = React.useCallback((itemId: string) => {
    setMobileExpandedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId)
      } else {
        return [...prev, itemId]
      }
    })
  }, [])

  const toggleDropdown = React.useCallback((name: string, event?: React.MouseEvent) => {
    // 阻止事件冒泡，防止点击事件传播到父元素
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }
    setActiveDropdown(prev => prev === name ? null : name)
  }, [])

  const closeDropdown = React.useCallback(() => {
    setActiveDropdown(null)
  }, [])

  const handleNavClick = React.useCallback(() => {
    closeDropdown()
  }, [closeDropdown])

  // 优化：使用 useCallback 稳定函数引用，减少子组件重新渲染
  const handleLanguageChange = React.useCallback(async (value: string) => {
    try {
      console.log('🌐 Header开始语言切换:', value)
      
      // 重置所有下拉菜单状态
      setActiveDropdown(null)
      setIsOpen(false)

      // 🔧 关键修复：必须先调用语言上下文的 setLanguage 来设置用户操作标记
      console.log('🔧 Header调用语言上下文setLanguage，设置用户操作标记')
      await setLanguageContext(value as 'en' | 'zh')
      
      console.log('🔄 Header语言状态更新完成，无需跳转页面')
      
      // 🔧 移除页面跳转，让轮播图等组件自然响应语言变化
      // setTimeout(() => {
      //   window.location.href = '/'
      // }, 100)
      
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Header语言切换失败:', error)
      }
    }
  }, [setLanguageContext])

  // 优化：使用 useCallback 稳定函数引用
  const handleMouseEnter = React.useCallback((itemName: string) => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
    setActiveDropdown(itemName)
  }, [hoverTimeout])

  const handleMouseLeave = React.useCallback(() => {
    const timeout = setTimeout(() => {
      setActiveDropdown(null)
    }, 200) // 200ms delay before hiding
    setHoverTimeout(timeout)
  }, [])

  const handleDropdownMouseEnter = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
  }

  const handleDropdownMouseLeave = () => {
    const timeout = setTimeout(() => {
      setActiveDropdown(null)
    }, 200) // 200ms delay before hiding
    setHoverTimeout(timeout)
  }

  React.useEffect(() => {
    const handleClickOutside = () => {
      setActiveDropdown(null)
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [])

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout)
      }
    }
  }, [hoverTimeout])

  // Keyboard shortcut for search
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        openSearch()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [openSearch])

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container-custom">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <AnimatedLogo />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2">
            {navigation.map((item) => (
              <div key={item.id} className="relative">
                {item.children ? (
                  <div
                    className="relative group"
                    onMouseEnter={() => handleMouseEnter(item.id)}
                    onMouseLeave={handleMouseLeave}
                  >
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center space-x-1 nav-link",
                        pathname.startsWith(item.href) && "text-Linnuo-blue"
                      )}
                      onClick={handleNavClick}
                    >
                      <span className="text-center">{item.name}</span>
                      <ChevronDown className="h-4 w-4 flex-shrink-0" />
                    </Link>

                    {activeDropdown === item.id && (
                      <div
                        className="absolute top-full left-0 mt-1 w-56 bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50 opacity-100 transform translate-y-0 transition-all duration-200"
                        onMouseEnter={handleDropdownMouseEnter}
                        onMouseLeave={handleDropdownMouseLeave}
                      >
                        {item.children.map((child) => (
                          child.href.startsWith('http') ? (
                            <a
                              key={child.name}
                              href={child.href}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-Linnuo-blue transition-colors"
                              onClick={closeDropdown}
                            >
                              {child.name}
                            </a>
                          ) : (
                            <Link
                              key={child.name}
                              href={child.href}
                              className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-Linnuo-blue transition-colors"
                              onClick={handleNavClick}
                            >
                              {child.name}
                            </Link>
                          )
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                          "nav-link",
                          pathname === item.href && "text-Linnuo-blue"
                        )}
                    onClick={handleNavClick}
                  >
                    <span className="text-center">{item.name}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Search, Language Switcher and Mobile Menu */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:flex"
              onClick={openSearch}
              title={`${commonT("search")} (Ctrl+K)`}
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Language Switcher - 重写功能 */}
            <div className="hidden md:flex items-center">
              <ButtonOptimizedLoading
                isLoading={isLanguageSwitching}
                loadingText="切换中..."
              >
                <Select value={language} onValueChange={handleLanguageChange} disabled={isLanguageSwitching}>
                  <SelectTrigger className={cn(
                    "w-auto h-9 border-none bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 language-selector transition-colors duration-150 px-2",
                    isLanguageSwitching && "opacity-50 cursor-not-allowed"
                  )}>
                    <div className="flex items-center space-x-1">
                      <Globe className="h-4 w-4" />
                      <SelectValue>
                        {language === 'en' ? 'English' : '中文'}
                      </SelectValue>
                    </div>
                  </SelectTrigger>
                  <SelectContent className="min-w-[100px]" side="bottom" align="end" sideOffset={4}>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="zh">中文</SelectItem>
                  </SelectContent>
                </Select>
              </ButtonOptimizedLoading>
            </div>



            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleMobileMenu}
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-700">
            <div className="py-4 space-y-4">
              {navigation.map((item) => (
                <div key={item.id}>
                  {item.children ? (
                    <div>
                      <div className="flex items-center">
                        <Link
                          href={item.href}
                          className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-Linnuo-blue transition-colors"
                          onClick={(e) => {
                            // 对于有子菜单的项，始终阻止默认行为
                            e.preventDefault()
                          }}
                        >
                          {item.name}
                        </Link>
                        <button
                          className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-Linnuo-orange transition-colors"
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            // 使用新的toggleMobileItem函数切换子菜单展开状态
                            toggleMobileItem(item.id)
                          }}
                        >
                          <ChevronDown
                            className={cn(
                              "h-4 w-4 transition-transform",
                              mobileExpandedItems.includes(item.id) && "rotate-180"
                            )}
                          />
                        </button>
                      </div>
                      
                      {mobileExpandedItems.includes(item.id) && (
                        <div className="pl-8 space-y-2">
                          {item.children.map((child) => (
                            child.href.startsWith('http') ? (
                              <a
                                key={child.name}
                                href={child.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-Linnuo-orange transition-colors"
                                onClick={(e) => {
                                  // 允许外部链接正常打开
                                  // 直接关闭移动菜单，不使用延时
                                  setIsOpen(false)
                                  setActiveDropdown(null)
                                  setMobileExpandedItems([])
                                }}
                              >
                                {child.name}
                              </a>
                            ) : (
                              <Link
                                key={child.name}
                                href={child.href}
                                className="block py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-Linnuo-orange transition-colors"
                                onClick={(e) => {
                                  // 允许子菜单项正常导航
                                  // 直接关闭移动菜单，不使用延时
                                  setIsOpen(false)
                                  setActiveDropdown(null)
                                  setMobileExpandedItems([])
                                }}
                              >
                                {child.name}
                              </Link>
                            )
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-Linnuo-orange transition-colors"
                      onClick={(e) => {
                        // 非下拉菜单项正常导航并关闭菜单
                        // 直接关闭移动菜单，不使用延时
                        setIsOpen(false)
                        setActiveDropdown(null)
                        setMobileExpandedItems([])
                      }}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              
              <div className="px-4 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={openSearch}
                >
                  <Search className="h-4 w-4 mr-2" />
                  {commonT("search")}
                </Button>

                {/* Mobile Language Switcher */}
                <div className="flex items-center space-x-2 px-4 py-2">
                  <Globe className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{commonT('language')}:</span>
                  <ButtonOptimizedLoading
                    isLoading={isLanguageSwitching}
                    loadingText="切换中..."
                  >
                    <Select value={language} onValueChange={handleLanguageChange} disabled={isLanguageSwitching}>
                      <SelectTrigger className={cn(
                        "flex-1 h-9 min-w-[100px]",
                        isLanguageSwitching && "opacity-50 cursor-not-allowed"
                      )}>
                        <SelectValue>
                          {language === 'en' ? 'English' : '中文'}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="zh">中文</SelectItem>
                      </SelectContent>
                    </Select>
                  </ButtonOptimizedLoading>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
