"use client"

import Link from "next/link"
import Image from "next/image"
import { Phone, Mail, QrCode, MessageCircle } from "lucide-react"
import { useFooterTranslations, useNavigationTranslations, useProductsTranslations } from "@/hooks/use-translations"
import { useLanguage } from "@/contexts/simple-language-context"
import { useCompanyInfo } from "@/hooks/use-api"

// Footer链接配置将在组件内部动态生成

export function Footer() {
  const { t: footerT } = useFooterTranslations()
  const { t: navT } = useNavigationTranslations()
  const { t: productsT } = useProductsTranslations()
  const { language } = useLanguage()

  // 获取动态公司信息
  const { data: companyInfo } = useCompanyInfo()

  // 联系方式的双语内容
  const contactLabels = {
    zh: {
      salesManager: "销售经理",
      email: "邮件",
      wechat: "微信",
      whatsapp: "WhatsApp",
      contactSales: "联系销售"
    },
    en: {
      salesManager: "Sales Manager",
      email: "Email",
      wechat: "WeChat",
      whatsapp: "WhatsApp",
      contactSales: "Contact Sales"
    }
  }

  const currentLabels = contactLabels[language]

  // 使用动态数据或fallback到默认值
  const contactInfo = companyInfo ? {
    businessPhone: companyInfo.businessPhone,
    businessHours: companyInfo.businessHours,
    businessEmail: companyInfo.businessEmail,
    techPhone: companyInfo.techPhone,
    techHours: companyInfo.techHours,
    address: companyInfo.address,
    companyName: companyInfo.companyName,
    companySlogan: companyInfo.companySlogan,
  } : {
    // Fallback默认值
    businessPhone: "+86-152-7791-5606",
    businessHours: "(Mon-Fri 9:00-18:00)",
    businessEmail: "<EMAIL>",
    techPhone: "+86-755-8888-8888",
    techHours: "(Mon-Fri 9:00-18:00)",
    address: "Shenzhen, Guangdong, China",
    companyName: "Linnuo",
    companySlogan: "Embedded Computing Solutions",
  }

  // 动态生成Footer链接配置
  const footerSections = {
    products: {
      title: footerT("products"),
      links: [
        { name: productsT("categories.scalableEmbeddedSeries"), href: "/products?category=scalable-embedded-series" },
        { name: productsT("categories.miniSizeSeries"), href: "/products?category=mini-size-series" },
        { name: productsT("categories.universalEmbeddedSeries"), href: "/products?category=universal-embedded-series" },
        { name: productsT("categories.allInOneIpc"), href: "/products?category=all-in-one-ipc" },
        { name: productsT("viewAll"), href: "/products" },
      ],
    },

    salesSupport: {
      title: footerT("support"),
      links: [
        { name: navT("sampleApplication"), href: "/support" },
        { name: navT("afterSalesService"), href: "/after-sales" },
        { name: navT("downloads"), href: "/downloads" },
        { name: navT("customizedService"), href: "/sales-and-support/customized" },
      ],
    },
    company: {
      title: footerT("company"),
      links: [
        { name: navT("aboutUs"), href: "/about" },
        { name: navT("news"), href: "/news" },
        { name: navT("contact"), href: "/contact" },
      ],
    },
  }

  return (
    <footer className="bg-slate-800 text-white">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="py-8 lg:py-10">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 lg:gap-8">
            {/* Products */}
            <div>
              <h3 className="text-white font-medium mb-4 text-base">
                {footerSections.products.title}
              </h3>
              <ul className="space-y-2">
                {footerSections.products.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Sales & Support */}
            <div>
              <h3 className="text-white font-medium mb-4 text-base">
                {footerSections.salesSupport.title}
              </h3>
              <ul className="space-y-2">
                {footerSections.salesSupport.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-white font-medium mb-4 text-base">
                {footerSections.company.title}
              </h3>
              <ul className="space-y-2">
                {footerSections.company.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Us */}
            <div className="col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-2">
              {/* Company Logo */}
              <div className="mb-6">
                <div className="flex items-center justify-start mb-4">
                  <Image
                    src="/logo-foot-bg-black.svg"
                    alt="Linnuo Logo"
                    width={350}
                    height={60}
                    className="object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-white font-semibold text-lg">{contactInfo.companyName}</h3>
                  <p className="text-gray-400 text-xs">{contactInfo.companySlogan}</p>
                </div>
              </div>

              <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start">
                <div className="flex-1">
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <Phone className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-gray-300">{currentLabels.salesManager}: {contactInfo.businessPhone}</p>
                        <p className="text-gray-400 text-xs">{contactInfo.businessHours}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Mail className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-gray-300 break-all">{currentLabels.email}: {contactInfo.businessEmail}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <MessageCircle className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-gray-300">{currentLabels.wechat}: x15277915606</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <MessageCircle className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <a 
                          href="https://wa.me/8617688888888" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-gray-300 hover:text-white transition-colors"
                        >
                          {currentLabels.whatsapp}: +86 152 7791 5606 
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* QR Code */}
                <div className="mt-4 lg:mt-0 lg:ml-8 flex flex-col items-center lg:items-start">
                  <div className="w-20 h-20 lg:w-24 lg:h-24 bg-white flex items-center justify-center mb-2">
                    <QrCode className="w-16 h-16 lg:w-20 lg:h-20 text-gray-800" />
                  </div>
                  <p className="text-gray-300 text-xs text-center">{currentLabels.contactSales}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="py-4 border-t border-gray-600 text-center">
          <div className="text-sm text-gray-400">
            Copyright © 2018 - {new Date().getFullYear()} {contactInfo.companyName}. {footerT("allRightsReserved")}
          </div>
        </div>
      </div>
    </footer>
  )
}
