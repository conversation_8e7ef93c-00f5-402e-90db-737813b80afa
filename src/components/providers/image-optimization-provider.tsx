'use client'

import { createContext, useContext, useEffect, ReactNode } from 'react'
import { useImagePreloader } from '@/hooks/use-image-preloader'

interface ImageOptimizationContextType {
  preloadImage: (src: string) => Promise<void>
  preloadImages: (sources: string[]) => Promise<void>
}

const ImageOptimizationContext = createContext<ImageOptimizationContextType | null>(null)

interface ImageOptimizationProviderProps {
  children: ReactNode
}

export function ImageOptimizationProvider({ children }: ImageOptimizationProviderProps) {
  const { preload, preloadMultiple } = useImagePreloader()

  // Preload critical images on mount - 优化性能
  useEffect(() => {
    const criticalImages = [
      // '/images/products/lattepanda-sigma-hero.jpg',
      // '/images/products/sigma-main.jpg',
      // '/images/products/Linnuo-mu.jpg',
      // '/images/products/delta3-main.jpg',
      // '/images/background/prod_bg.jpg', // 添加首页关键图片
      // '/images/logo.svg'
    ]

    // 使用 requestIdleCallback 在浏览器空闲时预加载
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        preloadMultiple(criticalImages, { priority: 'high' })
          .catch(error => {
            // Failed to preload critical images - continue without preloading
          })
      })
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        preloadMultiple(criticalImages, { priority: 'high' })
          .catch(error => {
            // Failed to preload critical images - continue without preloading
          })
      }, 100)
    }
  }, [preloadMultiple])

  const contextValue: ImageOptimizationContextType = {
    preloadImage: preload,
    preloadImages: preloadMultiple
  }

  return (
    <ImageOptimizationContext.Provider value={contextValue}>
      {children}
    </ImageOptimizationContext.Provider>
  )
}

export function useImageOptimization() {
  const context = useContext(ImageOptimizationContext)
  if (!context) {
    // Return no-op functions if provider is not available
    return {
      preloadImage: async () => {},
      preloadImages: async () => {}
    }
  }
  return context
}
