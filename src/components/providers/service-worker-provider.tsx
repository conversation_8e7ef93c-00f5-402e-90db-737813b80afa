'use client'

import { useEffect } from 'react'
import { useServiceWorker } from '@/hooks/use-service-worker'

interface ServiceWorkerProviderProps {
  children: React.ReactNode
}

export function ServiceWorkerProvider({ children }: ServiceWorkerProviderProps) {
  const { state, preloadImages } = useServiceWorker()

  useEffect(() => {
    // Preload critical images once service worker is active
    if (state.isActive) {
      const criticalImages = [
        '/images/products/lattepanda-sigma-hero.jpg',
        '/images/products/sigma-main.jpg',
        '/images/products/Linnuo-mu.jpg',
        '/images/hero/sigma-hero.jpg',
        '/images/hero/mu-hero.jpg',
        '/images/hero/delta-hero.jpg'
      ]

      preloadImages(criticalImages)
        .then(() => {
          // Critical images preloaded successfully
        })
        .catch((error) => {
          // Failed to preload some critical images - continue without preloading
        })
    }
  }, [state.isActive, preloadImages])

  // Log service worker status in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Service Worker Status:', state)
    }
  }, [state])

  return <>{children}</>
}
