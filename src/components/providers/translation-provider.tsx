'use client'

import { useEffect, useState } from 'react'
import { useLanguage } from '@/contexts/simple-language-context'
import { staticTranslations } from '@/locales/static-translations'

interface TranslationProviderProps {
  children: React.ReactNode
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const { language, isLoading } = useLanguage()
  const [isTranslationsReady, setIsTranslationsReady] = useState(false)

  useEffect(() => {
    // 预加载当前语言的所有翻译
    const preloadTranslations = async () => {
      try {
        // 确保翻译数据已加载
        const translations = staticTranslations[language]
        if (translations) {
          // 模拟短暂延迟以确保翻译完全加载
          await new Promise(resolve => setTimeout(resolve, 50))
          setIsTranslationsReady(true)
        }
      } catch (error) {
        console.warn('Failed to preload translations:', error)
        setIsTranslationsReady(true) // 即使失败也继续渲染
      }
    }

    setIsTranslationsReady(false)
    preloadTranslations()
  }, [language])

  // 在翻译加载期间显示加载状态
  if (isLoading || !isTranslationsReady) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">
            {language === 'zh' ? '正在加载...' : 'Loading...'}
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
