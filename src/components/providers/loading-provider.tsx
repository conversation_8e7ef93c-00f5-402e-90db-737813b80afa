"use client"

import React from 'react'
import { useLanguage } from '@/contexts/simple-language-context'
import { LoadingOverlay } from '@/components/ui/loading-overlay'
import { useTranslations } from '@/hooks/use-translations'

interface LoadingProviderProps {
  children: React.ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const { isLoading, language } = useLanguage()
  const { t } = useTranslations('common')
  const [showLoading, setShowLoading] = React.useState(false)

  // 优化：只在加载时间超过阈值时显示加载指示器
  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (isLoading) {
      // 延迟显示加载指示器，避免闪烁
      timeoutId = setTimeout(() => {
        setShowLoading(true)
      }, 100) // 100ms 延迟
    } else {
      setShowLoading(false)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [isLoading])

  const loadingMessage = language === 'zh' ? '正在切换语言...' : 'Switching language...'

  return (
    <>
      {children}
      <LoadingOverlay
        isVisible={showLoading}
        message={loadingMessage}
      />
    </>
  )
}
