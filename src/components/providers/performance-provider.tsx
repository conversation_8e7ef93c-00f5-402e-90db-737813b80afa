"use client"

import React, { useEffect } from 'react'

interface PerformanceProviderProps {
  children: React.ReactNode
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  useEffect(() => {
    // 预加载关键资源
    const preloadCriticalResources = () => {
      const criticalImages = [
        '/images/background/prod_bg.jpg',
        '/logo-top-bg-white.svg',
        '/images/products/Linnuo-3-delta.jpg'
      ]

      criticalImages.forEach(src => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'image'
        link.href = src
        document.head.appendChild(link)
      })
    }

    // 预连接到外部域名
    const preconnectDomains = () => {
      const domains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
      ]

      domains.forEach(domain => {
        const link = document.createElement('link')
        link.rel = 'preconnect'
        link.href = domain
        link.crossOrigin = 'anonymous'
        document.head.appendChild(link)
      })
    }

    // 性能监控
    const monitorPerformance = () => {
      if ('performance' in window) {
        // 监控页面加载时间
        window.addEventListener('load', () => {
          setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
            if (perfData) {
              console.log('Page Load Performance:', {
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                totalTime: perfData.loadEventEnd - perfData.fetchStart
              })
            }
          }, 0)
        })

        // 监控资源加载
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'resource' && entry.duration > 1000) {
              console.warn('Slow resource detected:', entry.name, entry.duration + 'ms')
            }
          })
        })
        observer.observe({ entryTypes: ['resource'] })

        // 监控长任务
        if ('PerformanceObserver' in window) {
          const longTaskObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              console.warn('Long task detected:', entry.duration + 'ms')
            })
          })
          try {
            longTaskObserver.observe({ entryTypes: ['longtask'] })
          } catch (e) {
            // longtask 可能不被支持
          }
        }
      }
    }

    // 优化字体加载
    const optimizeFonts = () => {
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          console.log('All fonts loaded')
        })
      }
    }

    preloadCriticalResources()
    preconnectDomains()
    monitorPerformance()
    optimizeFonts()
  }, [])

  return <>{children}</>
}
