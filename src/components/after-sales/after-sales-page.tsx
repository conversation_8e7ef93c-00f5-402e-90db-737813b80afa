'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Download,
  Mail,
  Phone
} from 'lucide-react'
import { useAfterSalesTranslations } from '@/hooks/use-translations'



export function AfterSalesPage() {
  const { t } = useAfterSalesTranslations()

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Background Image */}
      <section
        className="relative py-24 lg:py-32 bg-cover bg-center bg-no-repeat min-h-[60vh] lg:min-h-[70vh] flex items-center"
        style={{
          backgroundImage: 'url(/images/background/about-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center text-white">
            <h1 className="text-4xl lg:text-5xl font-bold mb-4 drop-shadow-lg">
              {t('title')}
            </h1>
            <p className="text-lg opacity-90">
              {t('subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* After-sales Service Content */}
            <div className="bg-white shadow-lg rounded-lg p-8">
              <div 
                className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
                style={{ whiteSpace: 'pre-line' }}
              >
                {t('content')}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-8 sm:py-12 lg:py-16 bg-gradient-to-br bg-blue-400 to-indigo-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">

            {/* Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
              {/* Download Card */}
              <Card className="group text-center bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 h-full">
                <CardContent className="p-6 lg:p-8 h-full flex flex-col">
                  <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors duration-300">
                    <Download className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">
                    {t('contactSupport.repairForm.title')}
                  </h3>
                  <p className="text-sm text-gray-600 mb-6 leading-relaxed flex-grow">
                    {t('contactSupport.repairForm.description')}
                  </p>
                  <Button 
                    size="default" 
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = '/excel/Linnuo-after-sales.xlsx';
                      link.download = 'Linnuo-after-sales.xlsx';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                  >
                    Download
                  </Button>
                </CardContent>
              </Card>

              {/* Email Card */}
              <Card className="group text-center bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 h-full">
                <CardContent className="p-6 lg:p-8 h-full flex flex-col">
                  <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors duration-300">
                    <Mail className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">
                    {t('contactSupport.emailSupport.title')}
                  </h3>
                  <p className="text-sm text-gray-600 mb-6 leading-relaxed flex-grow">
                    {t('contactSupport.emailSupport.description')}
                  </p>
                  <Button 
                    size="default" 
                    variant="outline" 
                    className="w-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
                    onClick={() => window.open(`mailto:${t('contactSupport.emailSupport.email')}`)}
                  >
                    {t('contactSupport.emailSupport.email')}
                  </Button>
                </CardContent>
              </Card>

              {/* Phone Card */}
              <Card className="group text-center bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 h-full">
                <CardContent className="p-6 lg:p-8 h-full flex flex-col">
                  <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors duration-300">
                    <Phone className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">
                    {t('contactSupport.phoneSupport.title')}
                  </h3>
                  <p className="text-sm text-gray-600 mb-6 leading-relaxed flex-grow">
                    {t('contactSupport.phoneSupport.description')}
                  </p>
                  <Button 
                    size="default" 
                    variant="outline" 
                    className="w-full border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
                    onClick={() => window.open(`tel:${t('contactSupport.phoneSupport.phone')}`)}
                  >
                    {t('contactSupport.phoneSupport.phone')}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
