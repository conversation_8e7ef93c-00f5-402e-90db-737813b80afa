import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  BookOpen, 
  Cpu, 
  Settings, 
  Download, 
  Code, 
  HelpCircle,
  ArrowRight,
  Star,
  Clock,
  Users
} from 'lucide-react'

const quickStartCards = [
  {
    title: 'Quick Start Guide',
    description: 'Get your Linnuoup and running in minutes',
    href: '/docs/quick-start',
    icon: BookOpen,
    badge: 'Popular',
    time: '5 min read'
  },
  {
    title: 'Hardware Overview',
    description: 'Learn about the hardware specifications and capabilities',
    href: '/docs/hardware',
    icon: Cpu,
    time: '10 min read'
  },
  {
    title: 'Software Setup',
    description: 'Install and configure software for your projects',
    href: '/docs/software',
    icon: Code,
    time: '15 min read'
  },
  {
    title: 'Configuration',
    description: 'Configure BIOS, network, and system settings',
    href: '/docs/configuration',
    icon: Settings,
    time: '8 min read'
  }
]

const popularTopics = [
  {
    title: 'GPIO Pin Layout',
    href: '/docs/hardware/pin-layout',
    views: '12.5k',
    category: 'Hardware'
  },
  {
    title: 'Installing Windows 11',
    href: '/docs/software/os/windows-11',
    views: '8.2k',
    category: 'Software'
  },
  {
    title: 'Arduino IDE Setup',
    href: '/docs/software/dev-tools/arduino',
    views: '6.8k',
    category: 'Development'
  },
  {
    title: 'Network Configuration',
    href: '/docs/configuration/network',
    views: '5.4k',
    category: 'Configuration'
  },
  {
    title: 'Troubleshooting Boot Issues',
    href: '/docs/troubleshooting/boot',
    views: '4.9k',
    category: 'Support'
  }
]

const recentUpdates = [
  {
    title: 'Linnuo3 Delta BIOS Update v1.2.3',
    date: '2024-01-15',
    type: 'Firmware',
    href: '/docs/downloads/firmware'
  },
  {
    title: 'New GPIO Library for Python',
    date: '2024-01-10',
    type: 'Software',
    href: '/docs/software/libraries'
  },
  {
    title: 'Ubuntu 22.04 LTS Support Guide',
    date: '2024-01-05',
    type: 'Guide',
    href: '/docs/software/os/ubuntu'
  }
]

export function DocsOverview() {
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          LinnuoDocumentation
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl">
          Everything you need to know about Linnuosingle board computers. 
          From getting started guides to advanced configuration and troubleshooting.
        </p>
      </div>

      {/* Quick Start Cards */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Start</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStartCards.map((card) => (
            <Card key={card.href} className="group hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-2">
                  <card.icon className="w-8 h-8 text-orange-600" />
                  {card.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {card.badge}
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg group-hover:text-orange-600 transition-colors">
                  {card.title}
                </CardTitle>
                <CardDescription className="text-sm">
                  {card.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock className="w-3 h-3 mr-1" />
                    {card.time}
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={card.href} className="flex items-center">
                      <span className="flex items-center">
                        Read
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </span>
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Popular Topics */}
        <div className="lg:col-span-2">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Topics</h2>
          <Card>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100">
                {popularTopics.map((topic, index) => (
                  <Link
                    key={topic.href}
                    href={topic.href}
                    className="flex items-center justify-between p-4 hover:bg-gray-50 transition-colors group"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 group-hover:text-orange-600 transition-colors">
                        {topic.title}
                      </h3>
                      <div className="flex items-center mt-1 space-x-4">
                        <Badge variant="outline" className="text-xs">
                          {topic.category}
                        </Badge>
                        <div className="flex items-center text-xs text-gray-500">
                          <Users className="w-3 h-3 mr-1" />
                          {topic.views} views
                        </div>
                      </div>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-orange-600 transition-colors" />
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Updates */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Updates</h2>
          <Card>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100">
                {recentUpdates.map((update, index) => (
                  <Link
                    key={update.href}
                    href={update.href}
                    className="block p-4 hover:bg-gray-50 transition-colors group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 group-hover:text-orange-600 transition-colors text-sm">
                          {update.title}
                        </h3>
                        <div className="flex items-center mt-2 space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {update.type}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {new Date(update.date).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <HelpCircle className="w-5 h-5 mr-2 text-orange-600" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Can't find what you're looking for? Our community and support team are here to help.
              </p>
              <div className="space-y-2">
                <Button variant="outline" size="sm" asChild className="w-full">
                  <Link href="/docs/troubleshooting">
                    Troubleshooting Guide
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="w-full">
                  <Link href="/support">
                    Contact Support
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="w-full">
                  <Link href="/community">
                    Join Community
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Featured Downloads */}
      <section className="mt-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Featured Downloads</h2>
          <Button variant="outline" asChild>
            <Link href="/docs/downloads" className="flex items-center">
              <span className="flex items-center">
                View All Downloads
                <ArrowRight className="w-4 h-4 ml-2" />
              </span>
            </Link>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="group hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Download className="w-8 h-8 text-orange-600" />
                <Badge className="bg-green-100 text-green-800">Latest</Badge>
              </div>
              <CardTitle className="text-lg">Firmware Updates</CardTitle>
              <CardDescription>
                Latest BIOS and firmware updates for all Linnuomodels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full bg-orange-500 hover:bg-orange-600">
                <Link href="/docs/downloads/firmware">
                  Download Firmware
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Code className="w-8 h-8 text-orange-600" />
                <Badge variant="outline">Tools</Badge>
              </div>
              <CardTitle className="text-lg">Development Tools</CardTitle>
              <CardDescription>
                IDEs, compilers, and development environments for your projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" asChild className="w-full">
                <Link href="/docs/downloads/tools">
                  Browse Tools
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Settings className="w-8 h-8 text-orange-600" />
                <Badge variant="outline">Drivers</Badge>
              </div>
              <CardTitle className="text-lg">System Drivers</CardTitle>
              <CardDescription>
                Essential drivers for Windows, Linux, and other operating systems
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" asChild className="w-full">
                <Link href="/docs/downloads/drivers">
                  Get Drivers
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
