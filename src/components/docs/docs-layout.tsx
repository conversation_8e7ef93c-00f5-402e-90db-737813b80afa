'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Menu, 
  X, 
  BookOpen, 
  Cpu, 
  Settings, 
  Download, 
  Code, 
  HelpCircle,
  ChevronRight,
  ChevronDown
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface DocsLayoutProps {
  children: React.ReactNode
}

interface NavItem {
  title: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  badge?: string
  children?: NavItem[]
}

const navigation: NavItem[] = [
  {
    title: 'Getting Started',
    href: '/docs',
    icon: BookOpen,
    children: [
      { title: 'Introduction', href: '/docs/introduction' },
      { title: 'Quick Start', href: '/docs/quick-start' },
      { title: 'Installation', href: '/docs/installation' },
      { title: 'First Boot', href: '/docs/first-boot' },
    ]
  },
  {
    title: 'Hardware',
    href: '/docs/hardware',
    icon: Cpu,
    children: [
      { title: 'Specifications', href: '/docs/hardware/specifications' },
      { title: 'Pin Layout', href: '/docs/hardware/pin-layout' },
      { title: 'Connections', href: '/docs/hardware/connections' },
      { title: 'Expansion', href: '/docs/hardware/expansion' },
    ]
  },
  {
    title: 'Software',
    href: '/docs/software',
    icon: Code,
    children: [
      { title: 'Operating Systems', href: '/docs/software/os' },
      { title: 'Drivers', href: '/docs/software/drivers' },
      { title: 'Development Tools', href: '/docs/software/dev-tools' },
      { title: 'Libraries', href: '/docs/software/libraries' },
    ]
  },
  {
    title: 'Configuration',
    href: '/docs/configuration',
    icon: Settings,
    children: [
      { title: 'BIOS Settings', href: '/docs/configuration/bios' },
      { title: 'Network Setup', href: '/docs/configuration/network' },
      { title: 'GPIO Configuration', href: '/docs/configuration/gpio' },
      { title: 'Power Management', href: '/docs/configuration/power' },
    ]
  },
  {
    title: 'Downloads',
    href: '/docs/downloads',
    icon: Download,
    children: [
      { title: 'Firmware', href: '/docs/downloads/firmware' },
      { title: 'Drivers', href: '/docs/downloads/drivers' },
      { title: 'Tools', href: '/docs/downloads/tools' },
      { title: 'Images', href: '/docs/downloads/images' },
    ]
  },
  {
    title: 'Troubleshooting',
    href: '/docs/troubleshooting',
    icon: HelpCircle,
    badge: 'Help',
    children: [
      { title: 'Common Issues', href: '/docs/troubleshooting/common' },
      { title: 'Boot Problems', href: '/docs/troubleshooting/boot' },
      { title: 'Hardware Issues', href: '/docs/troubleshooting/hardware' },
      { title: 'FAQ', href: '/docs/troubleshooting/faq' },
    ]
  },
]

export function DocsLayout({ children }: DocsLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedItems, setExpandedItems] = useState<string[]>(['/docs'])
  const [pathname, setPathname] = useState('')

  // 安全地获取pathname，避免SSR错误
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      setPathname(window.location.pathname)
    }
  }, [])

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => 
      prev.includes(href) 
        ? prev.filter(item => item !== href)
        : [...prev, href]
    )
  }

  const isActive = (href: string) => {
    if (href === '/docs') {
      return pathname === '/docs'
    }
    return pathname.startsWith(href)
  }

  const isExpanded = (href: string) => expandedItems.includes(href)

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const active = isActive(item.href)
    const expanded = isExpanded(item.href)

    return (
      <div key={item.href}>
        <div className="flex items-center">
          <Link
            href={item.href}
            className={cn(
              'flex items-center w-full px-3 py-2 text-sm transition-colors',
              level > 0 && 'ml-4',
              active 
                ? 'bg-orange-100 text-orange-900 font-medium' 
                : 'text-gray-700 hover:bg-gray-100'
            )}
            onClick={() => setSidebarOpen(false)}
          >
            {item.icon && (
              <item.icon className={cn(
                'w-4 h-4 mr-3',
                active ? 'text-orange-600' : 'text-gray-500'
              )} />
            )}
            <span className="flex-1">{item.title}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {item.badge}
              </Badge>
            )}
          </Link>
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.href)}
              className="p-1 text-gray-500 hover:text-gray-700"
            >
              {expanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          )}
        </div>
        
        {hasChildren && expanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex min-h-screen">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        'fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Documentation</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Search */}
          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search documentation..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigation.map(item => renderNavItem(item))}
          </nav>

          {/* Sidebar footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <p>Need help? Contact our support team.</p>
              <Link href="/support" className="text-orange-600 hover:text-orange-700">
                Get Support →
              </Link>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex-1 lg:ml-0">
        {/* Mobile header */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 text-gray-500 hover:text-gray-700"
          >
            <Menu className="w-5 h-5" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Documentation</h1>
          <div className="w-9" /> {/* Spacer */}
        </div>

        {/* Content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  )
}
