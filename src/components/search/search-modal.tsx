'use client'

import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSearch } from '@/contexts/search-context'
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  X, 
  Filter, 
  Package, 
  FileText, 
  Loader2,
  ArrowRight,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { useSearchTranslations } from '@/hooks/use-translations'

const typeIcons = {
  product: Package,
  news: FileText
}

const typeLabels = {
  product: 'resultTypes.product',
  news: 'resultTypes.news'
}

export function SearchModal() {
  const router = useRouter()
  const { t } = useSearchTranslations()
  const {
    isSearchOpen,
    searchQuery,
    searchResults,
    searchFilters,
    isSearching,
    closeSearch,
    setSearchQuery,
    setSearchFilters,
    performSearch
  } = useSearch()

  const [selectedIndex, setSelectedIndex] = useState(0)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  // Focus input when modal opens
  useEffect(() => {
    if (isSearchOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isSearchOpen])

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('Linnuo-recent-searches')
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved))
      } catch (error) {
        // Failed to parse recent searches, use empty array
      }
    }
  }, [])

  // Handle search input changes
  const handleSearchChange = async (value: string) => {
    setSearchQuery(value)
    setSelectedIndex(0)
    
    if (value.trim()) {
      await performSearch(value)
    }
  }

  // Handle search submission
  const handleSearchSubmit = (query: string = searchQuery) => {
    if (!query.trim()) return

    // Save to recent searches
    const newRecentSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5)
    setRecentSearches(newRecentSearches)
    localStorage.setItem('Linnuo-recent-searches', JSON.stringify(newRecentSearches))

    // Navigate to search results page
    router.push(`/search?q=${encodeURIComponent(query)}&type=${searchFilters.type}`)
    closeSearch()
  }

  // Handle result selection
  const handleResultSelect = (url: string) => {
    router.push(url)
    closeSearch()
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!searchResults.length) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => Math.min(prev + 1, searchResults.length - 1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => Math.max(prev - 1, 0))
        break
      case 'Enter':
        e.preventDefault()
        if (searchResults[selectedIndex]) {
          handleResultSelect(searchResults[selectedIndex].url)
        } else {
          handleSearchSubmit()
        }
        break
      case 'Escape':
        closeSearch()
        break
    }
  }

  // Filter options
  const filterOptions = [
    { value: 'all', label: t('filters.all'), icon: Search },
    { value: 'product', label: t('filters.products'), icon: Package },
    { value: 'news', label: t('filters.news'), icon: FileText }
  ] as const

  return (
    <Dialog open={isSearchOpen} onOpenChange={closeSearch}>
      <DialogContent className="max-w-2xl p-0 gap-0 overflow-hidden [&>button]:hidden">
        {/* Hidden accessibility elements for screen readers */}
        <DialogTitle className="sr-only">{t('title')}</DialogTitle>
        <DialogDescription className="sr-only">
          {t('placeholder')}
        </DialogDescription>
        <div className="flex items-center border-b px-4 py-3">
          <Search className="w-5 h-5 text-gray-400 mr-3" />
          <Input
            ref={inputRef}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={t('placeholder')}
            className="border-0 focus-visible:ring-0 text-base"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchQuery('')}
              className="ml-2 h-8 w-8 p-0 hover:bg-gray-100 rounded-full transition-colors"
              title={t('clearSearch')}
            >
              <X className="w-4 h-4 text-gray-500 hover:text-gray-700" />
            </Button>
          )}
        </div>

        {/* Filter Tabs */}
        <div className="flex items-center gap-1 px-4 py-2 border-b bg-gray-50">
          {filterOptions.map((option) => {
            const Icon = option.icon
            return (
              <Button
                key={option.value}
                variant={searchFilters.type === option.value ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setSearchFilters({ ...searchFilters, type: option.value })}
                className="h-8"
              >
                <Icon className="w-3 h-3 mr-1" />
                {option.label}
              </Button>
            )
          })}
        </div>

        <div className="max-h-96 overflow-y-auto" ref={resultsRef}>
          {/* Loading State */}
          {isSearching && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">{t('searching')}</span>
            </div>
          )}

          {/* Search Results */}
          {!isSearching && searchQuery && searchResults.length > 0 && (
            <div className="py-2">
              {searchResults.map((result, index) => {
                const Icon = typeIcons[result.type]
                return (
                  <button
                    key={result.id}
                    onClick={() => handleResultSelect(result.url)}
                    className={cn(
                      "w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors",
                      index === selectedIndex && "bg-gray-50"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      {result.image ? (
                        <div className="w-10 h-10 overflow-hidden flex-shrink-0">
                          <Image
                            src={result.image}
                            alt={result.title}
                            width={40}
                            height={40}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-10 h-10 bg-gray-100 flex items-center justify-center flex-shrink-0">
                          <Icon className="w-5 h-5 text-gray-400" />
                        </div>
                      )}
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900 truncate">
                            {result.title}
                          </h3>
                          <Badge variant="secondary" className="text-xs">
                            {t(typeLabels[result.type])}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {result.description}
                        </p>
                      </div>
                      
                      <ArrowRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    </div>
                  </button>
                )
              })}
            </div>
          )}

          {/* No Results */}
          {!isSearching && searchQuery && searchResults.length === 0 && (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">{t('noResults')}</h3>
              <p className="text-gray-600 text-sm">
                {t('noResultsDescription')}
              </p>
            </div>
          )}

          {/* Recent Searches */}
          {!searchQuery && recentSearches.length > 0 && (
            <div className="py-2">
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                {t('suggestions.recentSearches')}
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSearchSubmit(search)}
                  className="w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-700">{search}</span>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!searchQuery && recentSearches.length === 0 && (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">{t('startSearch')}</h3>
              <p className="text-gray-600 text-sm">
                {t('startSearchDescription')}
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t px-4 py-2 bg-gray-50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span>↑↓ {t('navigation.navigate')}</span>
              <span>↵ {t('navigation.select')}</span>
              <span>Esc {t('navigation.close')}</span>
            </div>
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearchSubmit()}
                className="h-6 text-xs"
              >
                {t('viewAllResults')}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
