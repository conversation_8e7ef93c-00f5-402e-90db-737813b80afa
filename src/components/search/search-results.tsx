'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useSearch, SearchResult, SearchFilters } from '@/contexts/search-context'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Search, 
  Package, 
  FileText, 
  Filter,
  SortAsc,
  Grid,
  List
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { useSearchTranslations } from '@/hooks/use-translations'

const typeIcons = {
  product: Package,
  news: FileText
}

// 类型标签将在组件内部动态生成

export function SearchResults() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { performSearch } = useSearch()
  const { t } = useSearchTranslations()
  
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [filters, setFilters] = useState<SearchFilters>({ type: 'all' })
  const [isLoading, setIsLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')

  // Initialize from URL params
  useEffect(() => {
    const q = searchParams.get('q') || ''
    const type = (searchParams.get('type') as SearchFilters['type']) || 'all'
    
    setQuery(q)
    setFilters({ type })
    
    if (q) {
      handleSearch(q, { type })
    }
  }, [searchParams])

  const handleSearch = async (searchQuery: string, searchFilters: SearchFilters = filters) => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    try {
      const searchResults = await performSearch(searchQuery, searchFilters)
      setResults(searchResults)
      
      // Update URL
      const params = new URLSearchParams()
      params.set('q', searchQuery)
      params.set('type', searchFilters.type)
      router.replace(`/search?${params.toString()}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFilterChange = (newFilters: SearchFilters) => {
    setFilters(newFilters)
    if (query) {
      handleSearch(query, newFilters)
    }
  }

  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 px-1">$1</mark>')
  }

  // 动态生成筛选选项
  const filterOptions = [
    { value: 'all', labelKey: 'all', icon: Search },
    { value: 'product', labelKey: 'products', icon: Package },
    { value: 'news', labelKey: 'news', icon: FileText }
  ]

  const resultsByType = results.reduce((acc, result) => {
    if (!acc[result.type]) acc[result.type] = []
    acc[result.type].push(result)
    return acc
  }, {} as Record<string, SearchResult[]>)

  return (
    <div className="max-w-4xl mx-auto">
      {/* Search Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">{t("title")}</h1>
        
        {/* Search Input */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch(query)}
              placeholder={t("placeholder")}
              className="pl-10"
            />
          </div>
          <Button onClick={() => handleSearch(query)} disabled={!query.trim()}>
            {t("searchButton")}
          </Button>
        </div>

        {/* Filters and View Options */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {filterOptions.map((option) => {
              const Icon = option.icon
              const count = option.value === 'all' ? results.length : (resultsByType[option.value]?.length || 0)
              
              return (
                <Button
                  key={option.value}
                  variant={filters.type === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange({ ...filters, type: option.value as any })}
                  className="h-9"
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {t(`filters.${option.labelKey}`)}
                  {count > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {count}
                    </Badge>
                  )}
                </Button>
              )
            })}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Results */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin h-8 w-8 border-b-2 border-primary rounded-full"></div>
          <span className="ml-3 text-gray-600">{t("searching")}</span>
        </div>
      ) : results.length > 0 ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <p className="text-gray-600">
              {t("resultsFound", { count: results.length, query })}
            </p>
          </div>

          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 gap-6' 
              : 'space-y-4'
          )}>
            {results.map((result) => {
              const Icon = typeIcons[result.type]
              
              return (
                <Card key={result.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <Link href={result.url} className="block">
                      <div className="flex items-start gap-4">
                        {result.image ? (
                          <div className="w-16 h-16 overflow-hidden flex-shrink-0">
                            <Image
                              src={result.image}
                              alt={result.title}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-16 h-16 bg-gray-100 flex items-center justify-center flex-shrink-0">
                            <Icon className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 
                              className="font-semibold text-gray-900 hover:text-orange-600 transition-colors"
                              dangerouslySetInnerHTML={{ 
                                __html: highlightText(result.title, query) 
                              }}
                            />
                            <Badge variant="outline" className="text-xs">
                              {(() => {
                                switch (result.type) {
                                  case 'product':
                                    return t('filters.products')
                                  case 'news':
                                    return t('filters.news')
                                  default:
                                    return result.type
                                }
                              })()}
                            </Badge>
                          </div>
                          
                          <p 
                            className="text-gray-600 text-sm line-clamp-2"
                            dangerouslySetInnerHTML={{ 
                              __html: highlightText(result.description, query) 
                            }}
                          />
                          
                          {result.category && (
                            <p className="text-xs text-gray-500 mt-2">
                              {t("category")}: {result.category}
                            </p>
                          )}
                        </div>
                      </div>
                    </Link>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      ) : query ? (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("noResults")}</h3>
          <p className="text-gray-600 mb-6">
            {t("noResultsDescription", { query })}
          </p>
          <Button variant="outline" onClick={() => setQuery('')}>
            {t("clearSearch")}
          </Button>
        </div>
      ) : (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("startSearch")}</h3>
          <p className="text-gray-600">
            {t("startSearchDescription")}
          </p>
        </div>
      )}
    </div>
  )
}
